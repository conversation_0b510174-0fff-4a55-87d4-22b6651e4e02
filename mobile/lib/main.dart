import 'dart:async';

import 'package:dio/dio.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:form_builder_validators/localization/l10n.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/core/config/theme.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/core/network/connection_checker.dart';
import 'package:s3g/src/sync/sync_service.dart';
import 'package:s3g/firebase_options.dart';
import 'package:s3g/routes/routes.dart';
import 'package:s3g/src/authentication/authentication.dart';
import 'package:s3g/src/authentication/presentation/bloc/sign_out/sign_out_cubit.dart';
import 'package:s3g/src/user_notification/user_notification.dart';
import 'package:s3g/src/app_update/app_update.dart';
import 'package:timeago/timeago.dart' as timeago;

void main() async {
  WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();

  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);

  await configureDependencies();

  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  try {
    await FirebaseMessagingService().initNotifications();
  } catch (e) {
    debugPrint(e.toString());
  }

  await NotificationService.initialize();

  timeago.setLocaleMessages('fr', timeago.FrMessages());

  getIt<Dio>().transformer = BackgroundTransformer();

  FlutterNativeSplash.remove();

  runApp(
    MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => getIt<AuthenticatedCubit>(),
        ),
        BlocProvider(
          create: (context) => getIt<SignOutCubit>(),
        ),
        BlocProvider(
          create: (context) => RefreshDataBloc(),
        ),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  late final SyncService _syncService;
  late final AppUpdateService _appUpdateService;
  bool _syncStarted = false;

  StreamSubscription<InternetStatus>? _subscription;

  @override
  void initState() {
    super.initState();

    // Initialize sync service
    _syncService = getIt<SyncService>();

    // Initialize app update service
    _appUpdateService = getIt<AppUpdateService>();

    // Check authentication
    context.read<AuthenticatedCubit>().checkUserAuthenticated();

    // Listen to internet connection changes
    // and sync data when connection is available
    _subscription =
        getIt<ConnectionChecker>().ic.onStatusChange.listen((status) {
      if (status == InternetStatus.connected && _syncStarted) {
        _syncService.syncAll();
      }
    });
  }

  @override
  void dispose() {
    context.read<AuthenticatedCubit>().dispose();
    _subscription?.cancel();
    _appUpdateService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthenticatedCubit, AuthenticatedState>(
      listener: (context, state) {
        if (state.status == AuthenticatedStatus.authenticated &&
            !_syncStarted) {
          // User is authenticated, start sync
          _syncStarted = true;

          // Trigger initial sync
          _syncService.syncAll();

          // Start periodic update checking
          _appUpdateService.startPeriodicUpdateCheck(context);
        } else if (state.status == AuthenticatedStatus.unauthenticated &&
            _syncStarted) {
          // User is not authenticated, stop sync
          _syncStarted = false;

          // Stop update checking
          _appUpdateService.stopPeriodicUpdateCheck();
        }
      },
      child: MaterialApp.router(
        title: AppConstants.appName,
        locale: const Locale('fr'),
        localizationsDelegates: const [
          FormBuilderLocalizations.delegate,
          ...GlobalMaterialLocalizations.delegates,
          GlobalWidgetsLocalizations.delegate,
        ],
        supportedLocales: FormBuilderLocalizations.supportedLocales,
        debugShowCheckedModeBanner: AppConstants.isDevelopment,
        theme: AppTheme.lightTheme.copyWith(
          // Enable spell check globally for French
          textSelectionTheme: const TextSelectionThemeData(
            selectionColor: Color.fromRGBO(239, 58, 79, 0.3),
          ),
        ),
        builder: (context, child) => InitWithAuthentication(child: child),
        routerConfig: appRouter,
      ),
    );
  }
}
