import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../../domain/repositories/app_update_repository.dart';
import '../widgets/update_dialog.dart';

class AppUpdateService {
  final AppUpdateRepository _repository;
  Timer? _updateCheckTimer;
  static const Duration _checkInterval = Duration(hours: 6); // Check every 6 hours
  
  AppUpdateService(this._repository);

  /// Start periodic update checking
  void startPeriodicUpdateCheck(BuildContext context) {
    // Check immediately on start
    _checkForUpdates(context);
    
    // Set up periodic checking
    _updateCheckTimer = Timer.periodic(_checkInterval, (_) {
      _checkForUpdates(context);
    });
  }

  /// Stop periodic update checking
  void stopPeriodicUpdateCheck() {
    _updateCheckTimer?.cancel();
    _updateCheckTimer = null;
  }

  /// Manual update check
  Future<void> checkForUpdatesManually(BuildContext context) async {
    await _checkForUpdates(context);
  }

  Future<void> _checkForUpdates(BuildContext context) async {
    try {
      // First check if user is signed in as tester
      final testerResult = await _repository.isTesterSignedIn();
      final isSignedIn = testerResult.fold(
        (error) {
          if (kDebugMode) {
            print('Error checking tester status: $error');
          }
          return false;
        },
        (signedIn) => signedIn,
      );

      if (!isSignedIn) {
        if (kDebugMode) {
          print('User is not signed in as tester, skipping update check');
        }
        return;
      }

      // Check for updates
      final updateResult = await _repository.isNewVersionAvailable();
      updateResult.fold(
        (error) {
          if (kDebugMode) {
            print('Error checking for updates: $error');
          }
        },
        (isAvailable) {
          if (isAvailable && context.mounted) {
            _showUpdateDialog(context);
          }
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('Unexpected error during update check: $e');
      }
    }
  }

  void _showUpdateDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => UpdateDialog(
        onUpdate: () async {
          Navigator.of(context).pop();
          await _repository.updateIfNewReleaseAvailable();
        },
        onLater: () {
          Navigator.of(context).pop();
        },
      ),
    );
  }

  /// Sign in as tester
  Future<bool> signInAsTester() async {
    final result = await _repository.signInTester();
    return result.fold(
      (error) {
        if (kDebugMode) {
          print('Error signing in as tester: $error');
        }
        return false;
      },
      (_) => true,
    );
  }

  /// Sign out as tester
  Future<bool> signOutAsTester() async {
    final result = await _repository.signOutTester();
    return result.fold(
      (error) {
        if (kDebugMode) {
          print('Error signing out as tester: $error');
        }
        return false;
      },
      (_) => true,
    );
  }

  void dispose() {
    stopPeriodicUpdateCheck();
  }
}
