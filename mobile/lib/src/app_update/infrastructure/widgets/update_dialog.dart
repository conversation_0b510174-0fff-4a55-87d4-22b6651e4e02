import 'package:flutter/material.dart';

class UpdateDialog extends StatelessWidget {
  final VoidCallback onUpdate;
  final VoidCallback onLater;
  final bool isForced;

  const UpdateDialog({
    super.key,
    required this.onUpdate,
    required this.onLater,
    this.isForced = false,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Row(
        children: [
          Icon(
            Icons.system_update,
            color: Colors.blue,
          ),
          SizedBox(width: 8),
          Text('Mise à jour disponible'),
        ],
      ),
      content: const Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Une nouvelle version de l\'application est disponible.',
            style: TextStyle(fontSize: 16),
          ),
          SizedBox(height: 12),
          Text(
            'Voulez-vous mettre à jour maintenant pour bénéficier des dernières fonctionnalités et améliorations ?',
            style: TextStyle(fontSize: 14, color: Colors.grey),
          ),
        ],
      ),
      actions: [
        if (!isForced)
          TextButton(
            onPressed: onLater,
            child: const Text('Plus tard'),
          ),
        ElevatedButton(
          onPressed: onUpdate,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
          ),
          child: const Text('Mettre à jour'),
        ),
      ],
    );
  }
}
