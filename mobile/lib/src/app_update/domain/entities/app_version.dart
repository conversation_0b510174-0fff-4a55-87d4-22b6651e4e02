import 'package:equatable/equatable.dart';

class AppVersion extends Equatable {
  final String version;
  final int buildNumber;
  final String? downloadUrl;
  final String? releaseNotes;
  final DateTime? releaseDate;
  final bool isForced;

  const AppVersion({
    required this.version,
    required this.buildNumber,
    this.downloadUrl,
    this.releaseNotes,
    this.releaseDate,
    this.isForced = false,
  });

  /// Compare two versions to determine if this version is newer
  /// Returns true if this version is newer than the other
  bool isNewerThan(AppVersion other) {
    // First compare by version string (semantic versioning)
    final thisVersionParts = version.split('.').map(int.parse).toList();
    final otherVersionParts = other.version.split('.').map(int.parse).toList();

    // Ensure both have same number of parts by padding with zeros
    final maxLength = thisVersionParts.length > otherVersionParts.length
        ? thisVersionParts.length
        : otherVersionParts.length;

    while (thisVersionParts.length < maxLength) {
      thisVersionParts.add(0);
    }
    while (otherVersionParts.length < maxLength) {
      otherVersionParts.add(0);
    }

    // Compare each part
    for (int i = 0; i < maxLength; i++) {
      if (thisVersionParts[i] > otherVersionParts[i]) {
        return true;
      } else if (thisVersionParts[i] < otherVersionParts[i]) {
        return false;
      }
    }

    // If version strings are equal, compare build numbers
    return buildNumber > other.buildNumber;
  }

  @override
  List<Object?> get props => [
        version,
        buildNumber,
        downloadUrl,
        releaseNotes,
        releaseDate,
        isForced,
      ];

  @override
  String toString() {
    return 'AppVersion(version: $version, buildNumber: $buildNumber)';
  }
}
