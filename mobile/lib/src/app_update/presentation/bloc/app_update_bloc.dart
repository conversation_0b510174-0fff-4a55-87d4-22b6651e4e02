import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';

import '../../domain/entities/app_version.dart';
import '../../domain/usecases/check_for_updates.dart';

part 'app_update_event.dart';
part 'app_update_state.dart';

@injectable
class AppUpdateBloc extends Bloc<AppUpdateEvent, AppUpdateState> {
  final CheckForUpdates _checkForUpdates;
  final GetCurrentVersion _getCurrentVersion;
  final GetLatestVersion _getLatestVersion;
  final UpdateIfAvailable _updateIfAvailable;

  AppUpdateBloc({
    required CheckForUpdates checkForUpdates,
    required GetCurrentVersion getCurrentVersion,
    required GetLatestVersion getLatestVersion,
    required UpdateIfAvailable updateIfAvailable,
  })  : _checkForUpdates = checkForUpdates,
        _getCurrentVersion = getCurrentVersion,
        _getLatestVersion = getLatestVersion,
        _updateIfAvailable = updateIfAvailable,
        super(AppUpdateInitial()) {
    on<CheckForUpdatesEvent>(_onCheckForUpdates);
    on<GetCurrentVersionEvent>(_onGetCurrentVersion);
    on<GetLatestVersionEvent>(_onGetLatestVersion);
    on<UpdateAppEvent>(_onUpdateApp);
    on<DismissUpdateEvent>(_onDismissUpdate);
  }

  Future<void> _onCheckForUpdates(
    CheckForUpdatesEvent event,
    Emitter<AppUpdateState> emit,
  ) async {
    emit(AppUpdateLoading());

    final result = await _checkForUpdates();
    result.fold(
      (error) => emit(AppUpdateError(error)),
      (isAvailable) {
        if (isAvailable) {
          emit(AppUpdateAvailable());
        } else {
          emit(AppUpdateNotAvailable());
        }
      },
    );
  }

  Future<void> _onGetCurrentVersion(
    GetCurrentVersionEvent event,
    Emitter<AppUpdateState> emit,
  ) async {
    final result = await _getCurrentVersion();
    result.fold(
      (error) => emit(AppUpdateError(error)),
      (version) => emit(AppUpdateCurrentVersion(version)),
    );
  }

  Future<void> _onGetLatestVersion(
    GetLatestVersionEvent event,
    Emitter<AppUpdateState> emit,
  ) async {
    final result = await _getLatestVersion();
    result.fold(
      (error) => emit(AppUpdateError(error)),
      (version) => emit(AppUpdateLatestVersion(version)),
    );
  }

  Future<void> _onUpdateApp(
    UpdateAppEvent event,
    Emitter<AppUpdateState> emit,
  ) async {
    emit(AppUpdateInProgress());

    final result = await _updateIfAvailable();
    result.fold(
      (error) => emit(AppUpdateError(error)),
      (_) => emit(AppUpdateSuccess()),
    );
  }

  Future<void> _onDismissUpdate(
    DismissUpdateEvent event,
    Emitter<AppUpdateState> emit,
  ) async {
    emit(AppUpdateDismissed());
  }
}
