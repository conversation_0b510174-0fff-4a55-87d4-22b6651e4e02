import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:go_router/go_router.dart';

import '../awasome_notification/notification_service.dart';
import '../../../../routes/config.dart';

Future<void> handleMessage(RemoteMessage message) async {
  if (message.notification != null) {
    // Extract routing data from message data
    final routeData = message.data;

    NotificationService.show(
      title: message.notification!.title ?? '',
      body: message.notification!.body ?? '',
      payload: routeData.isNotEmpty
          ? routeData.map((key, value) => MapEntry(key, value.toString()))
          : null,
    );
  }
}

Future<void> handleMessageTap(RemoteMessage message) async {
  // Handle notification tap for navigation
  final routeData = message.data;

  if (routeData.isNotEmpty && routeData.containsKey('route')) {
    final route = routeData['route'];
    if (route != null && route.isNotEmpty) {
      // Navigate to the specified route
      rootNavigatorKey.currentContext?.go(route);
    }
  }
}

class FirebaseMessagingService {
  final _firebaseMessaging = FirebaseMessaging.instance;

  Future<void> initNotifications() async {
    await _firebaseMessaging.requestPermission();

    final token = await getToken();

    if (kDebugMode) {
      print("FirebaseMessaging token: $token");
    }

    // Background
    FirebaseMessaging.onBackgroundMessage(handleMessage);

    // Foreground
    FirebaseMessaging.onMessage.listen(handleMessage);

    // Handle notification taps when app is in background/terminated
    FirebaseMessaging.onMessageOpenedApp.listen(handleMessageTap);

    // Handle notification tap when app is terminated and opened from notification
    final initialMessage = await _firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      handleMessageTap(initialMessage);
    }
  }

  Future<String?> getToken() async {
    return await _firebaseMessaging.getToken();
  }
}
