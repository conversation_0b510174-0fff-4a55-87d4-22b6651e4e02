(()=>{var qo=Object.create;var Ti=Object.defineProperty;var Go=Object.getOwnPropertyDescriptor;var Ko=Object.getOwnPropertyNames;var Jo=Object.getPrototypeOf,Qo=Object.prototype.hasOwnProperty;var Kr=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var Zo=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of Ko(t))!Qo.call(e,i)&&i!==r&&Ti(e,i,{get:()=>t[i],enumerable:!(n=Go(t,i))||n.enumerable});return e};var ea=(e,t,r)=>(r=e!=null?qo(Jo(e)):{},Zo(t||!e||!e.__esModule?Ti(r,"default",{value:e,enumerable:!0}):r,e));var uo=Kr(()=>{});var po=Kr(()=>{});var ho=Kr((Hs,yr)=>{(function(){"use strict";var e="input is invalid type",t="finalize already called",r=typeof window=="object",n=r?window:{};n.JS_MD5_NO_WINDOW&&(r=!1);var i=!r&&typeof self=="object",o=!n.JS_MD5_NO_NODE_JS&&typeof process=="object"&&process.versions&&process.versions.node;o?n=global:i&&(n=self);var a=!n.JS_MD5_NO_COMMON_JS&&typeof yr=="object"&&yr.exports,d=typeof define=="function"&&define.amd,f=!n.JS_MD5_NO_ARRAY_BUFFER&&typeof ArrayBuffer<"u",u="0123456789abcdef".split(""),y=[128,32768,8388608,-2147483648],m=[0,8,16,24],O=["hex","array","digest","buffer","arrayBuffer","base64"],E="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),S=[],C;if(f){var I=new ArrayBuffer(68);C=new Uint8Array(I),S=new Uint32Array(I)}var $=Array.isArray;(n.JS_MD5_NO_NODE_JS||!$)&&($=function(l){return Object.prototype.toString.call(l)==="[object Array]"});var A=ArrayBuffer.isView;f&&(n.JS_MD5_NO_ARRAY_BUFFER_IS_VIEW||!A)&&(A=function(l){return typeof l=="object"&&l.buffer&&l.buffer.constructor===ArrayBuffer});var k=function(l){var h=typeof l;if(h==="string")return[l,!0];if(h!=="object"||l===null)throw new Error(e);if(f&&l.constructor===ArrayBuffer)return[new Uint8Array(l),!1];if(!$(l)&&!A(l))throw new Error(e);return[l,!1]},Y=function(l){return function(h){return new X(!0).update(h)[l]()}},ne=function(){var l=Y("hex");o&&(l=J(l)),l.create=function(){return new X},l.update=function(p){return l.create().update(p)};for(var h=0;h<O.length;++h){var v=O[h];l[v]=Y(v)}return l},J=function(l){var h=uo(),v=po().Buffer,p;v.from&&!n.JS_MD5_NO_BUFFER_FROM?p=v.from:p=function(M){return new v(M)};var j=function(M){if(typeof M=="string")return h.createHash("md5").update(M,"utf8").digest("hex");if(M==null)throw new Error(e);return M.constructor===ArrayBuffer&&(M=new Uint8Array(M)),$(M)||A(M)||M.constructor===v?h.createHash("md5").update(p(M)).digest("hex"):l(M)};return j},V=function(l){return function(h,v){return new Q(h,!0).update(v)[l]()}},de=function(){var l=V("hex");l.create=function(p){return new Q(p)},l.update=function(p,j){return l.create(p).update(j)};for(var h=0;h<O.length;++h){var v=O[h];l[v]=V(v)}return l};function X(l){if(l)S[0]=S[16]=S[1]=S[2]=S[3]=S[4]=S[5]=S[6]=S[7]=S[8]=S[9]=S[10]=S[11]=S[12]=S[13]=S[14]=S[15]=0,this.blocks=S,this.buffer8=C;else if(f){var h=new ArrayBuffer(68);this.buffer8=new Uint8Array(h),this.blocks=new Uint32Array(h)}else this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];this.h0=this.h1=this.h2=this.h3=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}X.prototype.update=function(l){if(this.finalized)throw new Error(t);var h=k(l);l=h[0];for(var v=h[1],p,j=0,M,R=l.length,Z=this.blocks,ze=this.buffer8;j<R;){if(this.hashed&&(this.hashed=!1,Z[0]=Z[16],Z[16]=Z[1]=Z[2]=Z[3]=Z[4]=Z[5]=Z[6]=Z[7]=Z[8]=Z[9]=Z[10]=Z[11]=Z[12]=Z[13]=Z[14]=Z[15]=0),v)if(f)for(M=this.start;j<R&&M<64;++j)p=l.charCodeAt(j),p<128?ze[M++]=p:p<2048?(ze[M++]=192|p>>>6,ze[M++]=128|p&63):p<55296||p>=57344?(ze[M++]=224|p>>>12,ze[M++]=128|p>>>6&63,ze[M++]=128|p&63):(p=65536+((p&1023)<<10|l.charCodeAt(++j)&1023),ze[M++]=240|p>>>18,ze[M++]=128|p>>>12&63,ze[M++]=128|p>>>6&63,ze[M++]=128|p&63);else for(M=this.start;j<R&&M<64;++j)p=l.charCodeAt(j),p<128?Z[M>>>2]|=p<<m[M++&3]:p<2048?(Z[M>>>2]|=(192|p>>>6)<<m[M++&3],Z[M>>>2]|=(128|p&63)<<m[M++&3]):p<55296||p>=57344?(Z[M>>>2]|=(224|p>>>12)<<m[M++&3],Z[M>>>2]|=(128|p>>>6&63)<<m[M++&3],Z[M>>>2]|=(128|p&63)<<m[M++&3]):(p=65536+((p&1023)<<10|l.charCodeAt(++j)&1023),Z[M>>>2]|=(240|p>>>18)<<m[M++&3],Z[M>>>2]|=(128|p>>>12&63)<<m[M++&3],Z[M>>>2]|=(128|p>>>6&63)<<m[M++&3],Z[M>>>2]|=(128|p&63)<<m[M++&3]);else if(f)for(M=this.start;j<R&&M<64;++j)ze[M++]=l[j];else for(M=this.start;j<R&&M<64;++j)Z[M>>>2]|=l[j]<<m[M++&3];this.lastByteIndex=M,this.bytes+=M-this.start,M>=64?(this.start=M-64,this.hash(),this.hashed=!0):this.start=M}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296<<0,this.bytes=this.bytes%4294967296),this},X.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var l=this.blocks,h=this.lastByteIndex;l[h>>>2]|=y[h&3],h>=56&&(this.hashed||this.hash(),l[0]=l[16],l[16]=l[1]=l[2]=l[3]=l[4]=l[5]=l[6]=l[7]=l[8]=l[9]=l[10]=l[11]=l[12]=l[13]=l[14]=l[15]=0),l[14]=this.bytes<<3,l[15]=this.hBytes<<3|this.bytes>>>29,this.hash()}},X.prototype.hash=function(){var l,h,v,p,j,M,R=this.blocks;this.first?(l=R[0]-680876937,l=(l<<7|l>>>25)-271733879<<0,p=(-1732584194^l&2004318071)+R[1]-117830708,p=(p<<12|p>>>20)+l<<0,v=(-271733879^p&(l^-271733879))+R[2]-1126478375,v=(v<<17|v>>>15)+p<<0,h=(l^v&(p^l))+R[3]-1316259209,h=(h<<22|h>>>10)+v<<0):(l=this.h0,h=this.h1,v=this.h2,p=this.h3,l+=(p^h&(v^p))+R[0]-680876936,l=(l<<7|l>>>25)+h<<0,p+=(v^l&(h^v))+R[1]-389564586,p=(p<<12|p>>>20)+l<<0,v+=(h^p&(l^h))+R[2]+606105819,v=(v<<17|v>>>15)+p<<0,h+=(l^v&(p^l))+R[3]-1044525330,h=(h<<22|h>>>10)+v<<0),l+=(p^h&(v^p))+R[4]-176418897,l=(l<<7|l>>>25)+h<<0,p+=(v^l&(h^v))+R[5]+1200080426,p=(p<<12|p>>>20)+l<<0,v+=(h^p&(l^h))+R[6]-1473231341,v=(v<<17|v>>>15)+p<<0,h+=(l^v&(p^l))+R[7]-45705983,h=(h<<22|h>>>10)+v<<0,l+=(p^h&(v^p))+R[8]+1770035416,l=(l<<7|l>>>25)+h<<0,p+=(v^l&(h^v))+R[9]-1958414417,p=(p<<12|p>>>20)+l<<0,v+=(h^p&(l^h))+R[10]-42063,v=(v<<17|v>>>15)+p<<0,h+=(l^v&(p^l))+R[11]-1990404162,h=(h<<22|h>>>10)+v<<0,l+=(p^h&(v^p))+R[12]+1804603682,l=(l<<7|l>>>25)+h<<0,p+=(v^l&(h^v))+R[13]-40341101,p=(p<<12|p>>>20)+l<<0,v+=(h^p&(l^h))+R[14]-1502002290,v=(v<<17|v>>>15)+p<<0,h+=(l^v&(p^l))+R[15]+1236535329,h=(h<<22|h>>>10)+v<<0,l+=(v^p&(h^v))+R[1]-165796510,l=(l<<5|l>>>27)+h<<0,p+=(h^v&(l^h))+R[6]-1069501632,p=(p<<9|p>>>23)+l<<0,v+=(l^h&(p^l))+R[11]+643717713,v=(v<<14|v>>>18)+p<<0,h+=(p^l&(v^p))+R[0]-373897302,h=(h<<20|h>>>12)+v<<0,l+=(v^p&(h^v))+R[5]-701558691,l=(l<<5|l>>>27)+h<<0,p+=(h^v&(l^h))+R[10]+38016083,p=(p<<9|p>>>23)+l<<0,v+=(l^h&(p^l))+R[15]-660478335,v=(v<<14|v>>>18)+p<<0,h+=(p^l&(v^p))+R[4]-405537848,h=(h<<20|h>>>12)+v<<0,l+=(v^p&(h^v))+R[9]+568446438,l=(l<<5|l>>>27)+h<<0,p+=(h^v&(l^h))+R[14]-1019803690,p=(p<<9|p>>>23)+l<<0,v+=(l^h&(p^l))+R[3]-187363961,v=(v<<14|v>>>18)+p<<0,h+=(p^l&(v^p))+R[8]+1163531501,h=(h<<20|h>>>12)+v<<0,l+=(v^p&(h^v))+R[13]-1444681467,l=(l<<5|l>>>27)+h<<0,p+=(h^v&(l^h))+R[2]-51403784,p=(p<<9|p>>>23)+l<<0,v+=(l^h&(p^l))+R[7]+1735328473,v=(v<<14|v>>>18)+p<<0,h+=(p^l&(v^p))+R[12]-1926607734,h=(h<<20|h>>>12)+v<<0,j=h^v,l+=(j^p)+R[5]-378558,l=(l<<4|l>>>28)+h<<0,p+=(j^l)+R[8]-2022574463,p=(p<<11|p>>>21)+l<<0,M=p^l,v+=(M^h)+R[11]+1839030562,v=(v<<16|v>>>16)+p<<0,h+=(M^v)+R[14]-35309556,h=(h<<23|h>>>9)+v<<0,j=h^v,l+=(j^p)+R[1]-1530992060,l=(l<<4|l>>>28)+h<<0,p+=(j^l)+R[4]+1272893353,p=(p<<11|p>>>21)+l<<0,M=p^l,v+=(M^h)+R[7]-155497632,v=(v<<16|v>>>16)+p<<0,h+=(M^v)+R[10]-1094730640,h=(h<<23|h>>>9)+v<<0,j=h^v,l+=(j^p)+R[13]+681279174,l=(l<<4|l>>>28)+h<<0,p+=(j^l)+R[0]-358537222,p=(p<<11|p>>>21)+l<<0,M=p^l,v+=(M^h)+R[3]-722521979,v=(v<<16|v>>>16)+p<<0,h+=(M^v)+R[6]+76029189,h=(h<<23|h>>>9)+v<<0,j=h^v,l+=(j^p)+R[9]-640364487,l=(l<<4|l>>>28)+h<<0,p+=(j^l)+R[12]-421815835,p=(p<<11|p>>>21)+l<<0,M=p^l,v+=(M^h)+R[15]+530742520,v=(v<<16|v>>>16)+p<<0,h+=(M^v)+R[2]-995338651,h=(h<<23|h>>>9)+v<<0,l+=(v^(h|~p))+R[0]-198630844,l=(l<<6|l>>>26)+h<<0,p+=(h^(l|~v))+R[7]+1126891415,p=(p<<10|p>>>22)+l<<0,v+=(l^(p|~h))+R[14]-1416354905,v=(v<<15|v>>>17)+p<<0,h+=(p^(v|~l))+R[5]-57434055,h=(h<<21|h>>>11)+v<<0,l+=(v^(h|~p))+R[12]+1700485571,l=(l<<6|l>>>26)+h<<0,p+=(h^(l|~v))+R[3]-1894986606,p=(p<<10|p>>>22)+l<<0,v+=(l^(p|~h))+R[10]-1051523,v=(v<<15|v>>>17)+p<<0,h+=(p^(v|~l))+R[1]-2054922799,h=(h<<21|h>>>11)+v<<0,l+=(v^(h|~p))+R[8]+1873313359,l=(l<<6|l>>>26)+h<<0,p+=(h^(l|~v))+R[15]-30611744,p=(p<<10|p>>>22)+l<<0,v+=(l^(p|~h))+R[6]-1560198380,v=(v<<15|v>>>17)+p<<0,h+=(p^(v|~l))+R[13]+1309151649,h=(h<<21|h>>>11)+v<<0,l+=(v^(h|~p))+R[4]-145523070,l=(l<<6|l>>>26)+h<<0,p+=(h^(l|~v))+R[11]-1120210379,p=(p<<10|p>>>22)+l<<0,v+=(l^(p|~h))+R[2]+718787259,v=(v<<15|v>>>17)+p<<0,h+=(p^(v|~l))+R[9]-343485551,h=(h<<21|h>>>11)+v<<0,this.first?(this.h0=l+1732584193<<0,this.h1=h-271733879<<0,this.h2=v-1732584194<<0,this.h3=p+271733878<<0,this.first=!1):(this.h0=this.h0+l<<0,this.h1=this.h1+h<<0,this.h2=this.h2+v<<0,this.h3=this.h3+p<<0)},X.prototype.hex=function(){this.finalize();var l=this.h0,h=this.h1,v=this.h2,p=this.h3;return u[l>>>4&15]+u[l&15]+u[l>>>12&15]+u[l>>>8&15]+u[l>>>20&15]+u[l>>>16&15]+u[l>>>28&15]+u[l>>>24&15]+u[h>>>4&15]+u[h&15]+u[h>>>12&15]+u[h>>>8&15]+u[h>>>20&15]+u[h>>>16&15]+u[h>>>28&15]+u[h>>>24&15]+u[v>>>4&15]+u[v&15]+u[v>>>12&15]+u[v>>>8&15]+u[v>>>20&15]+u[v>>>16&15]+u[v>>>28&15]+u[v>>>24&15]+u[p>>>4&15]+u[p&15]+u[p>>>12&15]+u[p>>>8&15]+u[p>>>20&15]+u[p>>>16&15]+u[p>>>28&15]+u[p>>>24&15]},X.prototype.toString=X.prototype.hex,X.prototype.digest=function(){this.finalize();var l=this.h0,h=this.h1,v=this.h2,p=this.h3;return[l&255,l>>>8&255,l>>>16&255,l>>>24&255,h&255,h>>>8&255,h>>>16&255,h>>>24&255,v&255,v>>>8&255,v>>>16&255,v>>>24&255,p&255,p>>>8&255,p>>>16&255,p>>>24&255]},X.prototype.array=X.prototype.digest,X.prototype.arrayBuffer=function(){this.finalize();var l=new ArrayBuffer(16),h=new Uint32Array(l);return h[0]=this.h0,h[1]=this.h1,h[2]=this.h2,h[3]=this.h3,l},X.prototype.buffer=X.prototype.arrayBuffer,X.prototype.base64=function(){for(var l,h,v,p="",j=this.array(),M=0;M<15;)l=j[M++],h=j[M++],v=j[M++],p+=E[l>>>2]+E[(l<<4|h>>>4)&63]+E[(h<<2|v>>>6)&63]+E[v&63];return l=j[M],p+=E[l>>>2]+E[l<<4&63]+"==",p};function Q(l,h){var v,p=k(l);if(l=p[0],p[1]){var j=[],M=l.length,R=0,Z;for(v=0;v<M;++v)Z=l.charCodeAt(v),Z<128?j[R++]=Z:Z<2048?(j[R++]=192|Z>>>6,j[R++]=128|Z&63):Z<55296||Z>=57344?(j[R++]=224|Z>>>12,j[R++]=128|Z>>>6&63,j[R++]=128|Z&63):(Z=65536+((Z&1023)<<10|l.charCodeAt(++v)&1023),j[R++]=240|Z>>>18,j[R++]=128|Z>>>12&63,j[R++]=128|Z>>>6&63,j[R++]=128|Z&63);l=j}l.length>64&&(l=new X(!0).update(l).array());var ze=[],Rt=[];for(v=0;v<64;++v){var Ut=l[v]||0;ze[v]=92^Ut,Rt[v]=54^Ut}X.call(this,h),this.update(Rt),this.oKeyPad=ze,this.inner=!0,this.sharedMemory=h}Q.prototype=new X,Q.prototype.finalize=function(){if(X.prototype.finalize.call(this),this.inner){this.inner=!1;var l=this.array();X.call(this,this.sharedMemory),this.update(this.oKeyPad),this.update(l),X.prototype.finalize.call(this)}};var me=ne();me.md5=me,me.md5.hmac=de(),a?yr.exports=me:(n.md5=me,d&&define(function(){return me}))})()});var Hi=["top","right","bottom","left"],Pi=["start","end"],Mi=Hi.reduce((e,t)=>e.concat(t,t+"-"+Pi[0],t+"-"+Pi[1]),[]),Et=Math.min,tt=Math.max,hr=Math.round,pr=Math.floor,nn=e=>({x:e,y:e}),ta={left:"right",right:"left",bottom:"top",top:"bottom"},na={start:"end",end:"start"};function Jr(e,t,r){return tt(e,Et(t,r))}function jt(e,t){return typeof e=="function"?e(t):e}function pt(e){return e.split("-")[0]}function xt(e){return e.split("-")[1]}function $i(e){return e==="x"?"y":"x"}function Qr(e){return e==="y"?"height":"width"}function Pn(e){return["top","bottom"].includes(pt(e))?"y":"x"}function Zr(e){return $i(Pn(e))}function Wi(e,t,r){r===void 0&&(r=!1);let n=xt(e),i=Zr(e),o=Qr(i),a=i==="x"?n===(r?"end":"start")?"right":"left":n==="start"?"bottom":"top";return t.reference[o]>t.floating[o]&&(a=mr(a)),[a,mr(a)]}function ra(e){let t=mr(e);return[vr(e),t,vr(t)]}function vr(e){return e.replace(/start|end/g,t=>na[t])}function ia(e,t,r){let n=["left","right"],i=["right","left"],o=["top","bottom"],a=["bottom","top"];switch(e){case"top":case"bottom":return r?t?i:n:t?n:i;case"left":case"right":return t?o:a;default:return[]}}function oa(e,t,r,n){let i=xt(e),o=ia(pt(e),r==="start",n);return i&&(o=o.map(a=>a+"-"+i),t&&(o=o.concat(o.map(vr)))),o}function mr(e){return e.replace(/left|right|bottom|top/g,t=>ta[t])}function aa(e){return{top:0,right:0,bottom:0,left:0,...e}}function ei(e){return typeof e!="number"?aa(e):{top:e,right:e,bottom:e,left:e}}function _n(e){return{...e,top:e.y,left:e.x,right:e.x+e.width,bottom:e.y+e.height}}function Ri(e,t,r){let{reference:n,floating:i}=e,o=Pn(t),a=Zr(t),d=Qr(a),f=pt(t),u=o==="y",y=n.x+n.width/2-i.width/2,m=n.y+n.height/2-i.height/2,O=n[d]/2-i[d]/2,E;switch(f){case"top":E={x:y,y:n.y-i.height};break;case"bottom":E={x:y,y:n.y+n.height};break;case"right":E={x:n.x+n.width,y:m};break;case"left":E={x:n.x-i.width,y:m};break;default:E={x:n.x,y:n.y}}switch(xt(t)){case"start":E[a]-=O*(r&&u?-1:1);break;case"end":E[a]+=O*(r&&u?-1:1);break}return E}var sa=async(e,t,r)=>{let{placement:n="bottom",strategy:i="absolute",middleware:o=[],platform:a}=r,d=o.filter(Boolean),f=await(a.isRTL==null?void 0:a.isRTL(t)),u=await a.getElementRects({reference:e,floating:t,strategy:i}),{x:y,y:m}=Ri(u,n,f),O=n,E={},S=0;for(let C=0;C<d.length;C++){let{name:I,fn:$}=d[C],{x:A,y:k,data:Y,reset:ne}=await $({x:y,y:m,initialPlacement:n,placement:O,strategy:i,middlewareData:E,rects:u,platform:a,elements:{reference:e,floating:t}});y=A??y,m=k??m,E={...E,[I]:{...E[I],...Y}},ne&&S<=50&&(S++,typeof ne=="object"&&(ne.placement&&(O=ne.placement),ne.rects&&(u=ne.rects===!0?await a.getElementRects({reference:e,floating:t,strategy:i}):ne.rects),{x:y,y:m}=Ri(u,O,f)),C=-1)}return{x:y,y:m,placement:O,strategy:i,middlewareData:E}};async function Cn(e,t){var r;t===void 0&&(t={});let{x:n,y:i,platform:o,rects:a,elements:d,strategy:f}=e,{boundary:u="clippingAncestors",rootBoundary:y="viewport",elementContext:m="floating",altBoundary:O=!1,padding:E=0}=jt(t,e),S=ei(E),I=d[O?m==="floating"?"reference":"floating":m],$=_n(await o.getClippingRect({element:(r=await(o.isElement==null?void 0:o.isElement(I)))==null||r?I:I.contextElement||await(o.getDocumentElement==null?void 0:o.getDocumentElement(d.floating)),boundary:u,rootBoundary:y,strategy:f})),A=m==="floating"?{...a.floating,x:n,y:i}:a.reference,k=await(o.getOffsetParent==null?void 0:o.getOffsetParent(d.floating)),Y=await(o.isElement==null?void 0:o.isElement(k))?await(o.getScale==null?void 0:o.getScale(k))||{x:1,y:1}:{x:1,y:1},ne=_n(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:d,rect:A,offsetParent:k,strategy:f}):A);return{top:($.top-ne.top+S.top)/Y.y,bottom:(ne.bottom-$.bottom+S.bottom)/Y.y,left:($.left-ne.left+S.left)/Y.x,right:(ne.right-$.right+S.right)/Y.x}}var la=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:n,placement:i,rects:o,platform:a,elements:d,middlewareData:f}=t,{element:u,padding:y=0}=jt(e,t)||{};if(u==null)return{};let m=ei(y),O={x:r,y:n},E=Zr(i),S=Qr(E),C=await a.getDimensions(u),I=E==="y",$=I?"top":"left",A=I?"bottom":"right",k=I?"clientHeight":"clientWidth",Y=o.reference[S]+o.reference[E]-O[E]-o.floating[S],ne=O[E]-o.reference[E],J=await(a.getOffsetParent==null?void 0:a.getOffsetParent(u)),V=J?J[k]:0;(!V||!await(a.isElement==null?void 0:a.isElement(J)))&&(V=d.floating[k]||o.floating[S]);let de=Y/2-ne/2,X=V/2-C[S]/2-1,Q=Et(m[$],X),me=Et(m[A],X),l=Q,h=V-C[S]-me,v=V/2-C[S]/2+de,p=Jr(l,v,h),j=!f.arrow&&xt(i)!=null&&v!==p&&o.reference[S]/2-(v<l?Q:me)-C[S]/2<0,M=j?v<l?v-l:v-h:0;return{[E]:O[E]+M,data:{[E]:p,centerOffset:v-p-M,...j&&{alignmentOffset:M}},reset:j}}});function fa(e,t,r){return(e?[...r.filter(i=>xt(i)===e),...r.filter(i=>xt(i)!==e)]:r.filter(i=>pt(i)===i)).filter(i=>e?xt(i)===e||(t?vr(i)!==i:!1):!0)}var ca=function(e){return e===void 0&&(e={}),{name:"autoPlacement",options:e,async fn(t){var r,n,i;let{rects:o,middlewareData:a,placement:d,platform:f,elements:u}=t,{crossAxis:y=!1,alignment:m,allowedPlacements:O=Mi,autoAlignment:E=!0,...S}=jt(e,t),C=m!==void 0||O===Mi?fa(m||null,E,O):O,I=await Cn(t,S),$=((r=a.autoPlacement)==null?void 0:r.index)||0,A=C[$];if(A==null)return{};let k=Wi(A,o,await(f.isRTL==null?void 0:f.isRTL(u.floating)));if(d!==A)return{reset:{placement:C[0]}};let Y=[I[pt(A)],I[k[0]],I[k[1]]],ne=[...((n=a.autoPlacement)==null?void 0:n.overflows)||[],{placement:A,overflows:Y}],J=C[$+1];if(J)return{data:{index:$+1,overflows:ne},reset:{placement:J}};let V=ne.map(Q=>{let me=xt(Q.placement);return[Q.placement,me&&y?Q.overflows.slice(0,2).reduce((l,h)=>l+h,0):Q.overflows[0],Q.overflows]}).sort((Q,me)=>Q[1]-me[1]),X=((i=V.filter(Q=>Q[2].slice(0,xt(Q[0])?2:3).every(me=>me<=0))[0])==null?void 0:i[0])||V[0][0];return X!==d?{data:{index:$+1,overflows:ne},reset:{placement:X}}:{}}}},ua=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var r,n;let{placement:i,middlewareData:o,rects:a,initialPlacement:d,platform:f,elements:u}=t,{mainAxis:y=!0,crossAxis:m=!0,fallbackPlacements:O,fallbackStrategy:E="bestFit",fallbackAxisSideDirection:S="none",flipAlignment:C=!0,...I}=jt(e,t);if((r=o.arrow)!=null&&r.alignmentOffset)return{};let $=pt(i),A=pt(d)===d,k=await(f.isRTL==null?void 0:f.isRTL(u.floating)),Y=O||(A||!C?[mr(d)]:ra(d));!O&&S!=="none"&&Y.push(...oa(d,C,S,k));let ne=[d,...Y],J=await Cn(t,I),V=[],de=((n=o.flip)==null?void 0:n.overflows)||[];if(y&&V.push(J[$]),m){let l=Wi(i,a,k);V.push(J[l[0]],J[l[1]])}if(de=[...de,{placement:i,overflows:V}],!V.every(l=>l<=0)){var X,Q;let l=(((X=o.flip)==null?void 0:X.index)||0)+1,h=ne[l];if(h)return{data:{index:l,overflows:de},reset:{placement:h}};let v=(Q=de.filter(p=>p.overflows[0]<=0).sort((p,j)=>p.overflows[1]-j.overflows[1])[0])==null?void 0:Q.placement;if(!v)switch(E){case"bestFit":{var me;let p=(me=de.map(j=>[j.placement,j.overflows.filter(M=>M>0).reduce((M,R)=>M+R,0)]).sort((j,M)=>j[1]-M[1])[0])==null?void 0:me[0];p&&(v=p);break}case"initialPlacement":v=d;break}if(i!==v)return{reset:{placement:v}}}return{}}}};function Ii(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function Li(e){return Hi.some(t=>e[t]>=0)}var da=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){let{rects:r}=t,{strategy:n="referenceHidden",...i}=jt(e,t);switch(n){case"referenceHidden":{let o=await Cn(t,{...i,elementContext:"reference"}),a=Ii(o,r.reference);return{data:{referenceHiddenOffsets:a,referenceHidden:Li(a)}}}case"escaped":{let o=await Cn(t,{...i,altBoundary:!0}),a=Ii(o,r.floating);return{data:{escapedOffsets:a,escaped:Li(a)}}}default:return{}}}}};function Ui(e){let t=Et(...e.map(o=>o.left)),r=Et(...e.map(o=>o.top)),n=tt(...e.map(o=>o.right)),i=tt(...e.map(o=>o.bottom));return{x:t,y:r,width:n-t,height:i-r}}function pa(e){let t=e.slice().sort((i,o)=>i.y-o.y),r=[],n=null;for(let i=0;i<t.length;i++){let o=t[i];!n||o.y-n.y>n.height/2?r.push([o]):r[r.length-1].push(o),n=o}return r.map(i=>_n(Ui(i)))}var ha=function(e){return e===void 0&&(e={}),{name:"inline",options:e,async fn(t){let{placement:r,elements:n,rects:i,platform:o,strategy:a}=t,{padding:d=2,x:f,y:u}=jt(e,t),y=Array.from(await(o.getClientRects==null?void 0:o.getClientRects(n.reference))||[]),m=pa(y),O=_n(Ui(y)),E=ei(d);function S(){if(m.length===2&&m[0].left>m[1].right&&f!=null&&u!=null)return m.find(I=>f>I.left-E.left&&f<I.right+E.right&&u>I.top-E.top&&u<I.bottom+E.bottom)||O;if(m.length>=2){if(Pn(r)==="y"){let Q=m[0],me=m[m.length-1],l=pt(r)==="top",h=Q.top,v=me.bottom,p=l?Q.left:me.left,j=l?Q.right:me.right,M=j-p,R=v-h;return{top:h,bottom:v,left:p,right:j,width:M,height:R,x:p,y:h}}let I=pt(r)==="left",$=tt(...m.map(Q=>Q.right)),A=Et(...m.map(Q=>Q.left)),k=m.filter(Q=>I?Q.left===A:Q.right===$),Y=k[0].top,ne=k[k.length-1].bottom,J=A,V=$,de=V-J,X=ne-Y;return{top:Y,bottom:ne,left:J,right:V,width:de,height:X,x:J,y:Y}}return O}let C=await o.getElementRects({reference:{getBoundingClientRect:S},floating:n.floating,strategy:a});return i.reference.x!==C.reference.x||i.reference.y!==C.reference.y||i.reference.width!==C.reference.width||i.reference.height!==C.reference.height?{reset:{rects:C}}:{}}}};async function va(e,t){let{placement:r,platform:n,elements:i}=e,o=await(n.isRTL==null?void 0:n.isRTL(i.floating)),a=pt(r),d=xt(r),f=Pn(r)==="y",u=["left","top"].includes(a)?-1:1,y=o&&f?-1:1,m=jt(t,e),{mainAxis:O,crossAxis:E,alignmentAxis:S}=typeof m=="number"?{mainAxis:m,crossAxis:0,alignmentAxis:null}:{mainAxis:0,crossAxis:0,alignmentAxis:null,...m};return d&&typeof S=="number"&&(E=d==="end"?S*-1:S),f?{x:E*y,y:O*u}:{x:O*u,y:E*y}}var Vi=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var r,n;let{x:i,y:o,placement:a,middlewareData:d}=t,f=await va(t,e);return a===((r=d.offset)==null?void 0:r.placement)&&(n=d.arrow)!=null&&n.alignmentOffset?{}:{x:i+f.x,y:o+f.y,data:{...f,placement:a}}}}},ma=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:n,placement:i}=t,{mainAxis:o=!0,crossAxis:a=!1,limiter:d={fn:I=>{let{x:$,y:A}=I;return{x:$,y:A}}},...f}=jt(e,t),u={x:r,y:n},y=await Cn(t,f),m=Pn(pt(i)),O=$i(m),E=u[O],S=u[m];if(o){let I=O==="y"?"top":"left",$=O==="y"?"bottom":"right",A=E+y[I],k=E-y[$];E=Jr(A,E,k)}if(a){let I=m==="y"?"top":"left",$=m==="y"?"bottom":"right",A=S+y[I],k=S-y[$];S=Jr(A,S,k)}let C=d.fn({...t,[O]:E,[m]:S});return{...C,data:{x:C.x-r,y:C.y-n}}}}},ga=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){let{placement:r,rects:n,platform:i,elements:o}=t,{apply:a=()=>{},...d}=jt(e,t),f=await Cn(t,d),u=pt(r),y=xt(r),m=Pn(r)==="y",{width:O,height:E}=n.floating,S,C;u==="top"||u==="bottom"?(S=u,C=y===(await(i.isRTL==null?void 0:i.isRTL(o.floating))?"start":"end")?"left":"right"):(C=u,S=y==="end"?"top":"bottom");let I=E-f[S],$=O-f[C],A=!t.middlewareData.shift,k=I,Y=$;if(m){let J=O-f.left-f.right;Y=y||A?Et($,J):J}else{let J=E-f.top-f.bottom;k=y||A?Et(I,J):J}if(A&&!y){let J=tt(f.left,0),V=tt(f.right,0),de=tt(f.top,0),X=tt(f.bottom,0);m?Y=O-2*(J!==0||V!==0?J+V:tt(f.left,f.right)):k=E-2*(de!==0||X!==0?de+X:tt(f.top,f.bottom))}await a({...t,availableWidth:Y,availableHeight:k});let ne=await i.getDimensions(o.floating);return O!==ne.width||E!==ne.height?{reset:{rects:!0}}:{}}}};function rn(e){return zi(e)?(e.nodeName||"").toLowerCase():"#document"}function ft(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Bt(e){var t;return(t=(zi(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function zi(e){return e instanceof Node||e instanceof ft(e).Node}function kt(e){return e instanceof Element||e instanceof ft(e).Element}function Tt(e){return e instanceof HTMLElement||e instanceof ft(e).HTMLElement}function Fi(e){return typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof ft(e).ShadowRoot}function zn(e){let{overflow:t,overflowX:r,overflowY:n,display:i}=ht(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!["inline","contents"].includes(i)}function ba(e){return["table","td","th"].includes(rn(e))}function ti(e){let t=ni(),r=ht(e);return r.transform!=="none"||r.perspective!=="none"||(r.containerType?r.containerType!=="normal":!1)||!t&&(r.backdropFilter?r.backdropFilter!=="none":!1)||!t&&(r.filter?r.filter!=="none":!1)||["transform","perspective","filter"].some(n=>(r.willChange||"").includes(n))||["paint","layout","strict","content"].some(n=>(r.contain||"").includes(n))}function ya(e){let t=Tn(e);for(;Tt(t)&&!gr(t);){if(ti(t))return t;t=Tn(t)}return null}function ni(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function gr(e){return["html","body","#document"].includes(rn(e))}function ht(e){return ft(e).getComputedStyle(e)}function br(e){return kt(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function Tn(e){if(rn(e)==="html")return e;let t=e.assignedSlot||e.parentNode||Fi(e)&&e.host||Bt(e);return Fi(t)?t.host:t}function Yi(e){let t=Tn(e);return gr(t)?e.ownerDocument?e.ownerDocument.body:e.body:Tt(t)&&zn(t)?t:Yi(t)}function Vn(e,t,r){var n;t===void 0&&(t=[]),r===void 0&&(r=!0);let i=Yi(e),o=i===((n=e.ownerDocument)==null?void 0:n.body),a=ft(i);return o?t.concat(a,a.visualViewport||[],zn(i)?i:[],a.frameElement&&r?Vn(a.frameElement):[]):t.concat(i,Vn(i,[],r))}function Xi(e){let t=ht(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,i=Tt(e),o=i?e.offsetWidth:r,a=i?e.offsetHeight:n,d=hr(r)!==o||hr(n)!==a;return d&&(r=o,n=a),{width:r,height:n,$:d}}function ri(e){return kt(e)?e:e.contextElement}function Dn(e){let t=ri(e);if(!Tt(t))return nn(1);let r=t.getBoundingClientRect(),{width:n,height:i,$:o}=Xi(t),a=(o?hr(r.width):r.width)/n,d=(o?hr(r.height):r.height)/i;return(!a||!Number.isFinite(a))&&(a=1),(!d||!Number.isFinite(d))&&(d=1),{x:a,y:d}}var wa=nn(0);function qi(e){let t=ft(e);return!ni()||!t.visualViewport?wa:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function xa(e,t,r){return t===void 0&&(t=!1),!r||t&&r!==ft(e)?!1:t}function vn(e,t,r,n){t===void 0&&(t=!1),r===void 0&&(r=!1);let i=e.getBoundingClientRect(),o=ri(e),a=nn(1);t&&(n?kt(n)&&(a=Dn(n)):a=Dn(e));let d=xa(o,r,n)?qi(o):nn(0),f=(i.left+d.x)/a.x,u=(i.top+d.y)/a.y,y=i.width/a.x,m=i.height/a.y;if(o){let O=ft(o),E=n&&kt(n)?ft(n):n,S=O,C=S.frameElement;for(;C&&n&&E!==S;){let I=Dn(C),$=C.getBoundingClientRect(),A=ht(C),k=$.left+(C.clientLeft+parseFloat(A.paddingLeft))*I.x,Y=$.top+(C.clientTop+parseFloat(A.paddingTop))*I.y;f*=I.x,u*=I.y,y*=I.x,m*=I.y,f+=k,u+=Y,S=ft(C),C=S.frameElement}}return _n({width:y,height:m,x:f,y:u})}var Ea=[":popover-open",":modal"];function Gi(e){return Ea.some(t=>{try{return e.matches(t)}catch{return!1}})}function Oa(e){let{elements:t,rect:r,offsetParent:n,strategy:i}=e,o=i==="fixed",a=Bt(n),d=t?Gi(t.floating):!1;if(n===a||d&&o)return r;let f={scrollLeft:0,scrollTop:0},u=nn(1),y=nn(0),m=Tt(n);if((m||!m&&!o)&&((rn(n)!=="body"||zn(a))&&(f=br(n)),Tt(n))){let O=vn(n);u=Dn(n),y.x=O.x+n.clientLeft,y.y=O.y+n.clientTop}return{width:r.width*u.x,height:r.height*u.y,x:r.x*u.x-f.scrollLeft*u.x+y.x,y:r.y*u.y-f.scrollTop*u.y+y.y}}function Sa(e){return Array.from(e.getClientRects())}function Ki(e){return vn(Bt(e)).left+br(e).scrollLeft}function Aa(e){let t=Bt(e),r=br(e),n=e.ownerDocument.body,i=tt(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),o=tt(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),a=-r.scrollLeft+Ki(e),d=-r.scrollTop;return ht(n).direction==="rtl"&&(a+=tt(t.clientWidth,n.clientWidth)-i),{width:i,height:o,x:a,y:d}}function Da(e,t){let r=ft(e),n=Bt(e),i=r.visualViewport,o=n.clientWidth,a=n.clientHeight,d=0,f=0;if(i){o=i.width,a=i.height;let u=ni();(!u||u&&t==="fixed")&&(d=i.offsetLeft,f=i.offsetTop)}return{width:o,height:a,x:d,y:f}}function _a(e,t){let r=vn(e,!0,t==="fixed"),n=r.top+e.clientTop,i=r.left+e.clientLeft,o=Tt(e)?Dn(e):nn(1),a=e.clientWidth*o.x,d=e.clientHeight*o.y,f=i*o.x,u=n*o.y;return{width:a,height:d,x:f,y:u}}function Ni(e,t,r){let n;if(t==="viewport")n=Da(e,r);else if(t==="document")n=Aa(Bt(e));else if(kt(t))n=_a(t,r);else{let i=qi(e);n={...t,x:t.x-i.x,y:t.y-i.y}}return _n(n)}function Ji(e,t){let r=Tn(e);return r===t||!kt(r)||gr(r)?!1:ht(r).position==="fixed"||Ji(r,t)}function Ca(e,t){let r=t.get(e);if(r)return r;let n=Vn(e,[],!1).filter(d=>kt(d)&&rn(d)!=="body"),i=null,o=ht(e).position==="fixed",a=o?Tn(e):e;for(;kt(a)&&!gr(a);){let d=ht(a),f=ti(a);!f&&d.position==="fixed"&&(i=null),(o?!f&&!i:!f&&d.position==="static"&&!!i&&["absolute","fixed"].includes(i.position)||zn(a)&&!f&&Ji(e,a))?n=n.filter(y=>y!==a):i=d,a=Tn(a)}return t.set(e,n),n}function Ta(e){let{element:t,boundary:r,rootBoundary:n,strategy:i}=e,a=[...r==="clippingAncestors"?Ca(t,this._c):[].concat(r),n],d=a[0],f=a.reduce((u,y)=>{let m=Ni(t,y,i);return u.top=tt(m.top,u.top),u.right=Et(m.right,u.right),u.bottom=Et(m.bottom,u.bottom),u.left=tt(m.left,u.left),u},Ni(t,d,i));return{width:f.right-f.left,height:f.bottom-f.top,x:f.left,y:f.top}}function Pa(e){let{width:t,height:r}=Xi(e);return{width:t,height:r}}function Ma(e,t,r){let n=Tt(t),i=Bt(t),o=r==="fixed",a=vn(e,!0,o,t),d={scrollLeft:0,scrollTop:0},f=nn(0);if(n||!n&&!o)if((rn(t)!=="body"||zn(i))&&(d=br(t)),n){let m=vn(t,!0,o,t);f.x=m.x+t.clientLeft,f.y=m.y+t.clientTop}else i&&(f.x=Ki(i));let u=a.left+d.scrollLeft-f.x,y=a.top+d.scrollTop-f.y;return{x:u,y,width:a.width,height:a.height}}function ki(e,t){return!Tt(e)||ht(e).position==="fixed"?null:t?t(e):e.offsetParent}function Qi(e,t){let r=ft(e);if(!Tt(e)||Gi(e))return r;let n=ki(e,t);for(;n&&ba(n)&&ht(n).position==="static";)n=ki(n,t);return n&&(rn(n)==="html"||rn(n)==="body"&&ht(n).position==="static"&&!ti(n))?r:n||ya(e)||r}var Ra=async function(e){let t=this.getOffsetParent||Qi,r=this.getDimensions;return{reference:Ma(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,...await r(e.floating)}}};function Ia(e){return ht(e).direction==="rtl"}var La={convertOffsetParentRelativeRectToViewportRelativeRect:Oa,getDocumentElement:Bt,getClippingRect:Ta,getOffsetParent:Qi,getElementRects:Ra,getClientRects:Sa,getDimensions:Pa,getScale:Dn,isElement:kt,isRTL:Ia};function Fa(e,t){let r=null,n,i=Bt(e);function o(){var d;clearTimeout(n),(d=r)==null||d.disconnect(),r=null}function a(d,f){d===void 0&&(d=!1),f===void 0&&(f=1),o();let{left:u,top:y,width:m,height:O}=e.getBoundingClientRect();if(d||t(),!m||!O)return;let E=pr(y),S=pr(i.clientWidth-(u+m)),C=pr(i.clientHeight-(y+O)),I=pr(u),A={rootMargin:-E+"px "+-S+"px "+-C+"px "+-I+"px",threshold:tt(0,Et(1,f))||1},k=!0;function Y(ne){let J=ne[0].intersectionRatio;if(J!==f){if(!k)return a();J?a(!1,J):n=setTimeout(()=>{a(!1,1e-7)},100)}k=!1}try{r=new IntersectionObserver(Y,{...A,root:i.ownerDocument})}catch{r=new IntersectionObserver(Y,A)}r.observe(e)}return a(!0),o}function ji(e,t,r,n){n===void 0&&(n={});let{ancestorScroll:i=!0,ancestorResize:o=!0,elementResize:a=typeof ResizeObserver=="function",layoutShift:d=typeof IntersectionObserver=="function",animationFrame:f=!1}=n,u=ri(e),y=i||o?[...u?Vn(u):[],...Vn(t)]:[];y.forEach($=>{i&&$.addEventListener("scroll",r,{passive:!0}),o&&$.addEventListener("resize",r)});let m=u&&d?Fa(u,r):null,O=-1,E=null;a&&(E=new ResizeObserver($=>{let[A]=$;A&&A.target===u&&E&&(E.unobserve(t),cancelAnimationFrame(O),O=requestAnimationFrame(()=>{var k;(k=E)==null||k.observe(t)})),r()}),u&&!f&&E.observe(u),E.observe(t));let S,C=f?vn(e):null;f&&I();function I(){let $=vn(e);C&&($.x!==C.x||$.y!==C.y||$.width!==C.width||$.height!==C.height)&&r(),C=$,S=requestAnimationFrame(I)}return r(),()=>{var $;y.forEach(A=>{i&&A.removeEventListener("scroll",r),o&&A.removeEventListener("resize",r)}),m?.(),($=E)==null||$.disconnect(),E=null,f&&cancelAnimationFrame(S)}}var ii=ca,Zi=ma,eo=ua,to=ga,no=da,ro=la,io=ha,Bi=(e,t,r)=>{let n=new Map,i={platform:La,...r},o={...i.platform,_c:n};return sa(e,t,{...i,platform:o})},Na=e=>{let t={placement:"bottom",strategy:"absolute",middleware:[]},r=Object.keys(e),n=i=>e[i];return r.includes("offset")&&t.middleware.push(Vi(n("offset"))),r.includes("teleport")&&(t.strategy="fixed"),r.includes("placement")&&(t.placement=n("placement")),r.includes("autoPlacement")&&!r.includes("flip")&&t.middleware.push(ii(n("autoPlacement"))),r.includes("flip")&&t.middleware.push(eo(n("flip"))),r.includes("shift")&&t.middleware.push(Zi(n("shift"))),r.includes("inline")&&t.middleware.push(io(n("inline"))),r.includes("arrow")&&t.middleware.push(ro(n("arrow"))),r.includes("hide")&&t.middleware.push(no(n("hide"))),r.includes("size")&&t.middleware.push(to(n("size"))),t},ka=(e,t)=>{let r={component:{trap:!1},float:{placement:"bottom",strategy:"absolute",middleware:[]}},n=i=>e[e.indexOf(i)+1];if(e.includes("trap")&&(r.component.trap=!0),e.includes("teleport")&&(r.float.strategy="fixed"),e.includes("offset")&&r.float.middleware.push(Vi(t.offset||10)),e.includes("placement")&&(r.float.placement=n("placement")),e.includes("autoPlacement")&&!e.includes("flip")&&r.float.middleware.push(ii(t.autoPlacement)),e.includes("flip")&&r.float.middleware.push(eo(t.flip)),e.includes("shift")&&r.float.middleware.push(Zi(t.shift)),e.includes("inline")&&r.float.middleware.push(io(t.inline)),e.includes("arrow")&&r.float.middleware.push(ro(t.arrow)),e.includes("hide")&&r.float.middleware.push(no(t.hide)),e.includes("size")){let i=t.size?.availableWidth??null,o=t.size?.availableHeight??null;i&&delete t.size.availableWidth,o&&delete t.size.availableHeight,r.float.middleware.push(to({...t.size,apply({availableWidth:a,availableHeight:d,elements:f}){Object.assign(f.floating.style,{maxWidth:`${i??a}px`,maxHeight:`${o??d}px`})}}))}return r},ja=e=>{var t="0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz".split(""),r="";e||(e=Math.floor(Math.random()*t.length));for(var n=0;n<e;n++)r+=t[Math.floor(Math.random()*t.length)];return r};function Ba(e,t=()=>{}){let r=!1;return function(){r?t.apply(this,arguments):(r=!0,e.apply(this,arguments))}}function Ha(e){let t={dismissable:!0,trap:!1};function r(n,i=null){if(n){if(n.hasAttribute("aria-expanded")||n.setAttribute("aria-expanded",!1),i.hasAttribute("id"))n.setAttribute("aria-controls",i.getAttribute("id"));else{let o=`panel-${ja(8)}`;n.setAttribute("aria-controls",o),i.setAttribute("id",o)}i.setAttribute("aria-modal",!0),i.setAttribute("role","dialog")}}e.magic("float",n=>(i={},o={})=>{let a={...t,...o},d=Object.keys(i).length>0?Na(i):{middleware:[ii()]},f=n,u=n.parentElement.closest("[x-data]"),y=u.querySelector('[x-ref="panel"]');r(f,y);function m(){return y.style.display=="block"}function O(){y.style.display="none",f.setAttribute("aria-expanded","false"),a.trap&&y.setAttribute("x-trap","false"),ji(n,y,C)}function E(){y.style.display="block",f.setAttribute("aria-expanded","true"),a.trap&&y.setAttribute("x-trap","true"),C()}function S(){m()?O():E()}async function C(){return await Bi(n,y,d).then(({middlewareData:I,placement:$,x:A,y:k})=>{if(I.arrow){let Y=I.arrow?.x,ne=I.arrow?.y,J=d.middleware.filter(de=>de.name=="arrow")[0].options.element,V={top:"bottom",right:"left",bottom:"top",left:"right"}[$.split("-")[0]];Object.assign(J.style,{left:Y!=null?`${Y}px`:"",top:ne!=null?`${ne}px`:"",right:"",bottom:"",[V]:"-4px"})}if(I.hide){let{referenceHidden:Y}=I.hide;Object.assign(y.style,{visibility:Y?"hidden":"visible"})}Object.assign(y.style,{left:`${A}px`,top:`${k}px`})})}a.dismissable&&(window.addEventListener("click",I=>{!u.contains(I.target)&&m()&&S()}),window.addEventListener("keydown",I=>{I.key==="Escape"&&m()&&S()},!0)),S()}),e.directive("float",(n,{modifiers:i,expression:o},{evaluate:a,effect:d})=>{let f=o?a(o):{},u=i.length>0?ka(i,f):{},y=null;u.float.strategy=="fixed"&&(n.style.position="fixed");let m=V=>n.parentElement&&!n.parentElement.closest("[x-data]").contains(V.target)?n.close():null,O=V=>V.key==="Escape"?n.close():null,E=n.getAttribute("x-ref"),S=n.parentElement.closest("[x-data]"),C=S.querySelectorAll(`[\\@click^="$refs.${E}"]`),I=S.querySelectorAll(`[x-on\\:click^="$refs.${E}"]`);n.style.setProperty("display","none"),r([...C,...I][0],n),n._x_isShown=!1,n.trigger=null,n._x_doHide||(n._x_doHide=()=>{n.style.setProperty("display","none",i.includes("important")?"important":void 0)}),n._x_doShow||(n._x_doShow=()=>{n.style.setProperty("display","block",i.includes("important")?"important":void 0)});let $=()=>{n._x_doHide(),n._x_isShown=!1},A=()=>{n._x_doShow(),n._x_isShown=!0},k=()=>setTimeout(A),Y=Ba(V=>V?A():$(),V=>{typeof n._x_toggleAndCascadeWithTransitions=="function"?n._x_toggleAndCascadeWithTransitions(n,V,A,$):V?k():$()}),ne,J=!0;d(()=>a(V=>{!J&&V===ne||(i.includes("immediate")&&(V?k():$()),Y(V),ne=V,J=!1)})),n.open=async function(V){n.trigger=V.currentTarget?V.currentTarget:V,Y(!0),n.trigger.setAttribute("aria-expanded","true"),u.component.trap&&n.setAttribute("x-trap","true"),y=ji(n.trigger,n,()=>{Bi(n.trigger,n,u.float).then(({middlewareData:de,placement:X,x:Q,y:me})=>{if(de.arrow){let l=de.arrow?.x,h=de.arrow?.y,v=u.float.middleware.filter(j=>j.name=="arrow")[0].options.element,p={top:"bottom",right:"left",bottom:"top",left:"right"}[X.split("-")[0]];Object.assign(v.style,{left:l!=null?`${l}px`:"",top:h!=null?`${h}px`:"",right:"",bottom:"",[p]:"-4px"})}if(de.hide){let{referenceHidden:l}=de.hide;Object.assign(n.style,{visibility:l?"hidden":"visible"})}Object.assign(n.style,{left:`${Q}px`,top:`${me}px`})})}),window.addEventListener("click",m),window.addEventListener("keydown",O,!0)},n.close=function(){if(!n._x_isShown)return!1;Y(!1),n.trigger.setAttribute("aria-expanded","false"),u.component.trap&&n.setAttribute("x-trap","false"),y(),window.removeEventListener("click",m),window.removeEventListener("keydown",O,!1)},n.toggle=function(V){n._x_isShown?n.close():n.open(V)}})}var oo=Ha;function $a(e){e.store("lazyLoadedAssets",{loaded:new Set,check(a){return Array.isArray(a)?a.every(d=>this.loaded.has(d)):this.loaded.has(a)},markLoaded(a){Array.isArray(a)?a.forEach(d=>this.loaded.add(d)):this.loaded.add(a)}});let t=a=>new CustomEvent(a,{bubbles:!0,composed:!0,cancelable:!0}),r=(a,d={},f,u)=>{let y=document.createElement(a);return Object.entries(d).forEach(([m,O])=>y[m]=O),f&&(u?f.insertBefore(y,u):f.appendChild(y)),y},n=(a,d,f={},u=null,y=null)=>{let m=a==="link"?`link[href="${d}"]`:`script[src="${d}"]`;if(document.querySelector(m)||e.store("lazyLoadedAssets").check(d))return Promise.resolve();let O=a==="link"?{...f,href:d}:{...f,src:d},E=r(a,O,u,y);return new Promise((S,C)=>{E.onload=()=>{e.store("lazyLoadedAssets").markLoaded(d),S()},E.onerror=()=>{C(new Error(`Failed to load ${a}: ${d}`))}})},i=async(a,d,f=null,u=null)=>{let y={type:"text/css",rel:"stylesheet"};d&&(y.media=d);let m=document.head,O=null;if(f&&u){let E=document.querySelector(`link[href*="${u}"]`);E?(m=E.parentElement,O=f==="before"?E:E.nextSibling):(console.warn(`Target (${u}) not found for ${a}. Appending to head.`),m=document.head,O=null)}await n("link",a,y,m,O)},o=async(a,d,f=null,u=null,y=null)=>{let m=document.head,O=null;if(f&&u){let S=document.querySelector(`script[src*="${u}"]`);S?(m=S.parentElement,O=f==="before"?S:S.nextSibling):(console.warn(`Target (${u}) not found for ${a}. Falling back to head or body.`),m=document.head,O=null)}else(d.has("body-start")||d.has("body-end"))&&(m=document.body,d.has("body-start")&&(O=document.body.firstChild));let E={};y&&(E.type="module"),await n("script",a,E,m,O)};e.directive("load-css",(a,{expression:d},{evaluate:f})=>{let u=f(d),y=a.media,m=a.getAttribute("data-dispatch"),O=a.getAttribute("data-css-before")?"before":a.getAttribute("data-css-after")?"after":null,E=a.getAttribute("data-css-before")||a.getAttribute("data-css-after")||null;Promise.all(u.map(S=>i(S,y,O,E))).then(()=>{m&&window.dispatchEvent(t(`${m}-css`))}).catch(console.error)}),e.directive("load-js",(a,{expression:d,modifiers:f},{evaluate:u})=>{let y=u(d),m=new Set(f),O=a.getAttribute("data-js-before")?"before":a.getAttribute("data-js-after")?"after":null,E=a.getAttribute("data-js-before")||a.getAttribute("data-js-after")||null,S=a.getAttribute("data-js-as-module")||a.getAttribute("data-as-module")||!1,C=a.getAttribute("data-dispatch");Promise.all(y.map(I=>o(I,m,O,E,S))).then(()=>{C&&window.dispatchEvent(t(`${C}-js`))}).catch(console.error)})}var ao=$a;function Wa(){return!0}function Ua({component:e,argument:t}){return new Promise(r=>{if(t)window.addEventListener(t,()=>r(),{once:!0});else{let n=i=>{i.detail.id===e.id&&(window.removeEventListener("async-alpine:load",n),r())};window.addEventListener("async-alpine:load",n)}})}function Va(){return new Promise(e=>{"requestIdleCallback"in window?window.requestIdleCallback(e):setTimeout(e,200)})}function za({argument:e}){return new Promise(t=>{if(!e)return console.log("Async Alpine: media strategy requires a media query. Treating as 'eager'"),t();let r=window.matchMedia(`(${e})`);r.matches?t():r.addEventListener("change",t,{once:!0})})}function Ya({component:e,argument:t}){return new Promise(r=>{let n=t||"0px 0px 0px 0px",i=new IntersectionObserver(o=>{o[0].isIntersecting&&(i.disconnect(),r())},{rootMargin:n});i.observe(e.el)})}var so={eager:Wa,event:Ua,idle:Va,media:za,visible:Ya};async function Xa(e){let t=qa(e.strategy);await oi(e,t)}async function oi(e,t){if(t.type==="expression"){if(t.operator==="&&")return Promise.all(t.parameters.map(r=>oi(e,r)));if(t.operator==="||")return Promise.any(t.parameters.map(r=>oi(e,r)))}return so[t.method]?so[t.method]({component:e,argument:t.argument}):!1}function qa(e){let t=Ga(e),r=fo(t);return r.type==="method"?{type:"expression",operator:"&&",parameters:[r]}:r}function Ga(e){let t=/\s*([()])\s*|\s*(\|\||&&|\|)\s*|\s*((?:[^()&|]+\([^()]+\))|[^()&|]+)\s*/g,r=[],n;for(;(n=t.exec(e))!==null;){let[i,o,a,d]=n;if(o!==void 0)r.push({type:"parenthesis",value:o});else if(a!==void 0)r.push({type:"operator",value:a==="|"?"&&":a});else{let f={type:"method",method:d.trim()};d.includes("(")&&(f.method=d.substring(0,d.indexOf("(")).trim(),f.argument=d.substring(d.indexOf("(")+1,d.indexOf(")"))),d.method==="immediate"&&(d.method="eager"),r.push(f)}}return r}function fo(e){let t=lo(e);for(;e.length>0&&(e[0].value==="&&"||e[0].value==="|"||e[0].value==="||");){let r=e.shift().value,n=lo(e);t.type==="expression"&&t.operator===r?t.parameters.push(n):t={type:"expression",operator:r,parameters:[t,n]}}return t}function lo(e){if(e[0].value==="("){e.shift();let t=fo(e);return e[0].value===")"&&e.shift(),t}else return e.shift()}function co(e){let t="load",r=e.prefixed("load-src"),n=e.prefixed("ignore"),i={defaultStrategy:"eager",keepRelativeURLs:!1},o=!1,a={},d=0;function f(){return d++}e.asyncOptions=A=>{i={...i,...A}},e.asyncData=(A,k=!1)=>{a[A]={loaded:!1,download:k}},e.asyncUrl=(A,k)=>{!A||!k||a[A]||(a[A]={loaded:!1,download:()=>import($(k))})},e.asyncAlias=A=>{o=A};let u=A=>{e.skipDuringClone(()=>{A._x_async||(A._x_async="init",A._x_ignore=!0,A.setAttribute(n,""))})()},y=async A=>{e.skipDuringClone(async()=>{if(A._x_async!=="init")return;A._x_async="await";let{name:k,strategy:Y}=m(A);await Xa({name:k,strategy:Y,el:A,id:A.id||f()}),A.isConnected&&(await O(k),A.isConnected&&(S(A),A._x_async="loaded"))})()};y.inline=u,e.directive(t,y).before("ignore");function m(A){let k=I(A.getAttribute(e.prefixed("data"))),Y=A.getAttribute(e.prefixed(t))||i.defaultStrategy,ne=A.getAttribute(r);return ne&&e.asyncUrl(k,ne),{name:k,strategy:Y}}async function O(A){if(A.startsWith("_x_async_")||(C(A),!a[A]||a[A].loaded))return;let k=await E(A);e.data(A,k),a[A].loaded=!0}async function E(A){if(!a[A])return;let k=await a[A].download(A);return typeof k=="function"?k:k[A]||k.default||Object.values(k)[0]||!1}function S(A){e.destroyTree(A),A._x_ignore=!1,A.removeAttribute(n),!A.closest(`[${n}]`)&&e.initTree(A)}function C(A){if(!(!o||a[A])){if(typeof o=="function"){e.asyncData(A,o);return}e.asyncUrl(A,o.replaceAll("[name]",A))}}function I(A){return(A||"").split(/[({]/g)[0]||`_x_async_${f()}`}function $(A){return i.keepRelativeURLs||new RegExp("^(?:[a-z+]+:)?//","i").test(A)?A:new URL(A,document.baseURI).href}}var Xo=ea(ho(),1);function vo(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Mt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?vo(Object(r),!0).forEach(function(n){Ka(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vo(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Sr(e){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Sr=function(t){return typeof t}:Sr=function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Sr(e)}function Ka(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function $t(){return $t=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},$t.apply(this,arguments)}function Ja(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,o;for(o=0;o<n.length;o++)i=n[o],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}function Qa(e,t){if(e==null)return{};var r=Ja(e,t),n,i;if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(i=0;i<o.length;i++)n=o[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}var Za="1.15.6";function Ht(e){if(typeof window<"u"&&window.navigator)return!!navigator.userAgent.match(e)}var Wt=Ht(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),er=Ht(/Edge/i),mo=Ht(/firefox/i),Gn=Ht(/safari/i)&&!Ht(/chrome/i)&&!Ht(/android/i),yi=Ht(/iP(ad|od|hone)/i),So=Ht(/chrome/i)&&Ht(/android/i),Ao={capture:!1,passive:!1};function Oe(e,t,r){e.addEventListener(t,r,!Wt&&Ao)}function Ee(e,t,r){e.removeEventListener(t,r,!Wt&&Ao)}function Tr(e,t){if(t){if(t[0]===">"&&(t=t.substring(1)),e)try{if(e.matches)return e.matches(t);if(e.msMatchesSelector)return e.msMatchesSelector(t);if(e.webkitMatchesSelector)return e.webkitMatchesSelector(t)}catch{return!1}return!1}}function Do(e){return e.host&&e!==document&&e.host.nodeType?e.host:e.parentNode}function St(e,t,r,n){if(e){r=r||document;do{if(t!=null&&(t[0]===">"?e.parentNode===r&&Tr(e,t):Tr(e,t))||n&&e===r)return e;if(e===r)break}while(e=Do(e))}return null}var go=/\s+/g;function ct(e,t,r){if(e&&t)if(e.classList)e.classList[r?"add":"remove"](t);else{var n=(" "+e.className+" ").replace(go," ").replace(" "+t+" "," ");e.className=(n+(r?" "+t:"")).replace(go," ")}}function ae(e,t,r){var n=e&&e.style;if(n){if(r===void 0)return document.defaultView&&document.defaultView.getComputedStyle?r=document.defaultView.getComputedStyle(e,""):e.currentStyle&&(r=e.currentStyle),t===void 0?r:r[t];!(t in n)&&t.indexOf("webkit")===-1&&(t="-webkit-"+t),n[t]=r+(typeof r=="string"?"":"px")}}function Fn(e,t){var r="";if(typeof e=="string")r=e;else do{var n=ae(e,"transform");n&&n!=="none"&&(r=n+" "+r)}while(!t&&(e=e.parentNode));var i=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return i&&new i(r)}function _o(e,t,r){if(e){var n=e.getElementsByTagName(t),i=0,o=n.length;if(r)for(;i<o;i++)r(n[i],i);return n}return[]}function Pt(){var e=document.scrollingElement;return e||document.documentElement}function qe(e,t,r,n,i){if(!(!e.getBoundingClientRect&&e!==window)){var o,a,d,f,u,y,m;if(e!==window&&e.parentNode&&e!==Pt()?(o=e.getBoundingClientRect(),a=o.top,d=o.left,f=o.bottom,u=o.right,y=o.height,m=o.width):(a=0,d=0,f=window.innerHeight,u=window.innerWidth,y=window.innerHeight,m=window.innerWidth),(t||r)&&e!==window&&(i=i||e.parentNode,!Wt))do if(i&&i.getBoundingClientRect&&(ae(i,"transform")!=="none"||r&&ae(i,"position")!=="static")){var O=i.getBoundingClientRect();a-=O.top+parseInt(ae(i,"border-top-width")),d-=O.left+parseInt(ae(i,"border-left-width")),f=a+o.height,u=d+o.width;break}while(i=i.parentNode);if(n&&e!==window){var E=Fn(i||e),S=E&&E.a,C=E&&E.d;E&&(a/=C,d/=S,m/=S,y/=C,f=a+y,u=d+m)}return{top:a,left:d,bottom:f,right:u,width:m,height:y}}}function bo(e,t,r){for(var n=sn(e,!0),i=qe(e)[t];n;){var o=qe(n)[r],a=void 0;if(r==="top"||r==="left"?a=i>=o:a=i<=o,!a)return n;if(n===Pt())break;n=sn(n,!1)}return!1}function Nn(e,t,r,n){for(var i=0,o=0,a=e.children;o<a.length;){if(a[o].style.display!=="none"&&a[o]!==se.ghost&&(n||a[o]!==se.dragged)&&St(a[o],r.draggable,e,!1)){if(i===t)return a[o];i++}o++}return null}function wi(e,t){for(var r=e.lastElementChild;r&&(r===se.ghost||ae(r,"display")==="none"||t&&!Tr(r,t));)r=r.previousElementSibling;return r||null}function vt(e,t){var r=0;if(!e||!e.parentNode)return-1;for(;e=e.previousElementSibling;)e.nodeName.toUpperCase()!=="TEMPLATE"&&e!==se.clone&&(!t||Tr(e,t))&&r++;return r}function yo(e){var t=0,r=0,n=Pt();if(e)do{var i=Fn(e),o=i.a,a=i.d;t+=e.scrollLeft*o,r+=e.scrollTop*a}while(e!==n&&(e=e.parentNode));return[t,r]}function es(e,t){for(var r in e)if(e.hasOwnProperty(r)){for(var n in t)if(t.hasOwnProperty(n)&&t[n]===e[r][n])return Number(r)}return-1}function sn(e,t){if(!e||!e.getBoundingClientRect)return Pt();var r=e,n=!1;do if(r.clientWidth<r.scrollWidth||r.clientHeight<r.scrollHeight){var i=ae(r);if(r.clientWidth<r.scrollWidth&&(i.overflowX=="auto"||i.overflowX=="scroll")||r.clientHeight<r.scrollHeight&&(i.overflowY=="auto"||i.overflowY=="scroll")){if(!r.getBoundingClientRect||r===document.body)return Pt();if(n||t)return r;n=!0}}while(r=r.parentNode);return Pt()}function ts(e,t){if(e&&t)for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r]);return e}function ai(e,t){return Math.round(e.top)===Math.round(t.top)&&Math.round(e.left)===Math.round(t.left)&&Math.round(e.height)===Math.round(t.height)&&Math.round(e.width)===Math.round(t.width)}var Kn;function Co(e,t){return function(){if(!Kn){var r=arguments,n=this;r.length===1?e.call(n,r[0]):e.apply(n,r),Kn=setTimeout(function(){Kn=void 0},t)}}}function ns(){clearTimeout(Kn),Kn=void 0}function To(e,t,r){e.scrollLeft+=t,e.scrollTop+=r}function Po(e){var t=window.Polymer,r=window.jQuery||window.Zepto;return t&&t.dom?t.dom(e).cloneNode(!0):r?r(e).clone(!0)[0]:e.cloneNode(!0)}function Mo(e,t,r){var n={};return Array.from(e.children).forEach(function(i){var o,a,d,f;if(!(!St(i,t.draggable,e,!1)||i.animated||i===r)){var u=qe(i);n.left=Math.min((o=n.left)!==null&&o!==void 0?o:1/0,u.left),n.top=Math.min((a=n.top)!==null&&a!==void 0?a:1/0,u.top),n.right=Math.max((d=n.right)!==null&&d!==void 0?d:-1/0,u.right),n.bottom=Math.max((f=n.bottom)!==null&&f!==void 0?f:-1/0,u.bottom)}}),n.width=n.right-n.left,n.height=n.bottom-n.top,n.x=n.left,n.y=n.top,n}var st="Sortable"+new Date().getTime();function rs(){var e=[],t;return{captureAnimationState:function(){if(e=[],!!this.options.animation){var n=[].slice.call(this.el.children);n.forEach(function(i){if(!(ae(i,"display")==="none"||i===se.ghost)){e.push({target:i,rect:qe(i)});var o=Mt({},e[e.length-1].rect);if(i.thisAnimationDuration){var a=Fn(i,!0);a&&(o.top-=a.f,o.left-=a.e)}i.fromRect=o}})}},addAnimationState:function(n){e.push(n)},removeAnimationState:function(n){e.splice(es(e,{target:n}),1)},animateAll:function(n){var i=this;if(!this.options.animation){clearTimeout(t),typeof n=="function"&&n();return}var o=!1,a=0;e.forEach(function(d){var f=0,u=d.target,y=u.fromRect,m=qe(u),O=u.prevFromRect,E=u.prevToRect,S=d.rect,C=Fn(u,!0);C&&(m.top-=C.f,m.left-=C.e),u.toRect=m,u.thisAnimationDuration&&ai(O,m)&&!ai(y,m)&&(S.top-m.top)/(S.left-m.left)===(y.top-m.top)/(y.left-m.left)&&(f=os(S,O,E,i.options)),ai(m,y)||(u.prevFromRect=y,u.prevToRect=m,f||(f=i.options.animation),i.animate(u,S,m,f)),f&&(o=!0,a=Math.max(a,f),clearTimeout(u.animationResetTimer),u.animationResetTimer=setTimeout(function(){u.animationTime=0,u.prevFromRect=null,u.fromRect=null,u.prevToRect=null,u.thisAnimationDuration=null},f),u.thisAnimationDuration=f)}),clearTimeout(t),o?t=setTimeout(function(){typeof n=="function"&&n()},a):typeof n=="function"&&n(),e=[]},animate:function(n,i,o,a){if(a){ae(n,"transition",""),ae(n,"transform","");var d=Fn(this.el),f=d&&d.a,u=d&&d.d,y=(i.left-o.left)/(f||1),m=(i.top-o.top)/(u||1);n.animatingX=!!y,n.animatingY=!!m,ae(n,"transform","translate3d("+y+"px,"+m+"px,0)"),this.forRepaintDummy=is(n),ae(n,"transition","transform "+a+"ms"+(this.options.easing?" "+this.options.easing:"")),ae(n,"transform","translate3d(0,0,0)"),typeof n.animated=="number"&&clearTimeout(n.animated),n.animated=setTimeout(function(){ae(n,"transition",""),ae(n,"transform",""),n.animated=!1,n.animatingX=!1,n.animatingY=!1},a)}}}}function is(e){return e.offsetWidth}function os(e,t,r,n){return Math.sqrt(Math.pow(t.top-e.top,2)+Math.pow(t.left-e.left,2))/Math.sqrt(Math.pow(t.top-r.top,2)+Math.pow(t.left-r.left,2))*n.animation}var Mn=[],si={initializeByDefault:!0},tr={mount:function(t){for(var r in si)si.hasOwnProperty(r)&&!(r in t)&&(t[r]=si[r]);Mn.forEach(function(n){if(n.pluginName===t.pluginName)throw"Sortable: Cannot mount plugin ".concat(t.pluginName," more than once")}),Mn.push(t)},pluginEvent:function(t,r,n){var i=this;this.eventCanceled=!1,n.cancel=function(){i.eventCanceled=!0};var o=t+"Global";Mn.forEach(function(a){r[a.pluginName]&&(r[a.pluginName][o]&&r[a.pluginName][o](Mt({sortable:r},n)),r.options[a.pluginName]&&r[a.pluginName][t]&&r[a.pluginName][t](Mt({sortable:r},n)))})},initializePlugins:function(t,r,n,i){Mn.forEach(function(d){var f=d.pluginName;if(!(!t.options[f]&&!d.initializeByDefault)){var u=new d(t,r,t.options);u.sortable=t,u.options=t.options,t[f]=u,$t(n,u.defaults)}});for(var o in t.options)if(t.options.hasOwnProperty(o)){var a=this.modifyOption(t,o,t.options[o]);typeof a<"u"&&(t.options[o]=a)}},getEventProperties:function(t,r){var n={};return Mn.forEach(function(i){typeof i.eventProperties=="function"&&$t(n,i.eventProperties.call(r[i.pluginName],t))}),n},modifyOption:function(t,r,n){var i;return Mn.forEach(function(o){t[o.pluginName]&&o.optionListeners&&typeof o.optionListeners[r]=="function"&&(i=o.optionListeners[r].call(t[o.pluginName],n))}),i}};function as(e){var t=e.sortable,r=e.rootEl,n=e.name,i=e.targetEl,o=e.cloneEl,a=e.toEl,d=e.fromEl,f=e.oldIndex,u=e.newIndex,y=e.oldDraggableIndex,m=e.newDraggableIndex,O=e.originalEvent,E=e.putSortable,S=e.extraEventProperties;if(t=t||r&&r[st],!!t){var C,I=t.options,$="on"+n.charAt(0).toUpperCase()+n.substr(1);window.CustomEvent&&!Wt&&!er?C=new CustomEvent(n,{bubbles:!0,cancelable:!0}):(C=document.createEvent("Event"),C.initEvent(n,!0,!0)),C.to=a||r,C.from=d||r,C.item=i||r,C.clone=o,C.oldIndex=f,C.newIndex=u,C.oldDraggableIndex=y,C.newDraggableIndex=m,C.originalEvent=O,C.pullMode=E?E.lastPutMode:void 0;var A=Mt(Mt({},S),tr.getEventProperties(n,t));for(var k in A)C[k]=A[k];r&&r.dispatchEvent(C),I[$]&&I[$].call(t,C)}}var ss=["evt"],at=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},i=n.evt,o=Qa(n,ss);tr.pluginEvent.bind(se)(t,r,Mt({dragEl:N,parentEl:Ve,ghostEl:ue,rootEl:ke,nextEl:bn,lastDownEl:Ar,cloneEl:We,cloneHidden:an,dragStarted:Yn,putSortable:Qe,activeSortable:se.active,originalEvent:i,oldIndex:Ln,oldDraggableIndex:Jn,newIndex:ut,newDraggableIndex:on,hideGhostForTarget:Fo,unhideGhostForTarget:No,cloneNowHidden:function(){an=!0},cloneNowShown:function(){an=!1},dispatchSortableEvent:function(d){it({sortable:r,name:d,originalEvent:i})}},o))};function it(e){as(Mt({putSortable:Qe,cloneEl:We,targetEl:N,rootEl:ke,oldIndex:Ln,oldDraggableIndex:Jn,newIndex:ut,newDraggableIndex:on},e))}var N,Ve,ue,ke,bn,Ar,We,an,Ln,ut,Jn,on,wr,Qe,In=!1,Pr=!1,Mr=[],mn,Ot,li,fi,wo,xo,Yn,Rn,Qn,Zn=!1,xr=!1,Dr,nt,ci=[],vi=!1,Rr=[],Lr=typeof document<"u",Er=yi,Eo=er||Wt?"cssFloat":"float",ls=Lr&&!So&&!yi&&"draggable"in document.createElement("div"),Ro=function(){if(Lr){if(Wt)return!1;var e=document.createElement("x");return e.style.cssText="pointer-events:auto",e.style.pointerEvents==="auto"}}(),Io=function(t,r){var n=ae(t),i=parseInt(n.width)-parseInt(n.paddingLeft)-parseInt(n.paddingRight)-parseInt(n.borderLeftWidth)-parseInt(n.borderRightWidth),o=Nn(t,0,r),a=Nn(t,1,r),d=o&&ae(o),f=a&&ae(a),u=d&&parseInt(d.marginLeft)+parseInt(d.marginRight)+qe(o).width,y=f&&parseInt(f.marginLeft)+parseInt(f.marginRight)+qe(a).width;if(n.display==="flex")return n.flexDirection==="column"||n.flexDirection==="column-reverse"?"vertical":"horizontal";if(n.display==="grid")return n.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(o&&d.float&&d.float!=="none"){var m=d.float==="left"?"left":"right";return a&&(f.clear==="both"||f.clear===m)?"vertical":"horizontal"}return o&&(d.display==="block"||d.display==="flex"||d.display==="table"||d.display==="grid"||u>=i&&n[Eo]==="none"||a&&n[Eo]==="none"&&u+y>i)?"vertical":"horizontal"},fs=function(t,r,n){var i=n?t.left:t.top,o=n?t.right:t.bottom,a=n?t.width:t.height,d=n?r.left:r.top,f=n?r.right:r.bottom,u=n?r.width:r.height;return i===d||o===f||i+a/2===d+u/2},cs=function(t,r){var n;return Mr.some(function(i){var o=i[st].options.emptyInsertThreshold;if(!(!o||wi(i))){var a=qe(i),d=t>=a.left-o&&t<=a.right+o,f=r>=a.top-o&&r<=a.bottom+o;if(d&&f)return n=i}}),n},Lo=function(t){function r(o,a){return function(d,f,u,y){var m=d.options.group.name&&f.options.group.name&&d.options.group.name===f.options.group.name;if(o==null&&(a||m))return!0;if(o==null||o===!1)return!1;if(a&&o==="clone")return o;if(typeof o=="function")return r(o(d,f,u,y),a)(d,f,u,y);var O=(a?d:f).options.group.name;return o===!0||typeof o=="string"&&o===O||o.join&&o.indexOf(O)>-1}}var n={},i=t.group;(!i||Sr(i)!="object")&&(i={name:i}),n.name=i.name,n.checkPull=r(i.pull,!0),n.checkPut=r(i.put),n.revertClone=i.revertClone,t.group=n},Fo=function(){!Ro&&ue&&ae(ue,"display","none")},No=function(){!Ro&&ue&&ae(ue,"display","")};Lr&&!So&&document.addEventListener("click",function(e){if(Pr)return e.preventDefault(),e.stopPropagation&&e.stopPropagation(),e.stopImmediatePropagation&&e.stopImmediatePropagation(),Pr=!1,!1},!0);var gn=function(t){if(N){t=t.touches?t.touches[0]:t;var r=cs(t.clientX,t.clientY);if(r){var n={};for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i]);n.target=n.rootEl=r,n.preventDefault=void 0,n.stopPropagation=void 0,r[st]._onDragOver(n)}}},us=function(t){N&&N.parentNode[st]._isOutsideThisEl(t.target)};function se(e,t){if(!(e&&e.nodeType&&e.nodeType===1))throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(e));this.el=e,this.options=t=$t({},t),e[st]=this;var r={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(e.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return Io(e,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(a,d){a.setData("Text",d.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:se.supportPointer!==!1&&"PointerEvent"in window&&(!Gn||yi),emptyInsertThreshold:5};tr.initializePlugins(this,e,r);for(var n in r)!(n in t)&&(t[n]=r[n]);Lo(t);for(var i in this)i.charAt(0)==="_"&&typeof this[i]=="function"&&(this[i]=this[i].bind(this));this.nativeDraggable=t.forceFallback?!1:ls,this.nativeDraggable&&(this.options.touchStartThreshold=1),t.supportPointer?Oe(e,"pointerdown",this._onTapStart):(Oe(e,"mousedown",this._onTapStart),Oe(e,"touchstart",this._onTapStart)),this.nativeDraggable&&(Oe(e,"dragover",this),Oe(e,"dragenter",this)),Mr.push(this.el),t.store&&t.store.get&&this.sort(t.store.get(this)||[]),$t(this,rs())}se.prototype={constructor:se,_isOutsideThisEl:function(t){!this.el.contains(t)&&t!==this.el&&(Rn=null)},_getDirection:function(t,r){return typeof this.options.direction=="function"?this.options.direction.call(this,t,r,N):this.options.direction},_onTapStart:function(t){if(t.cancelable){var r=this,n=this.el,i=this.options,o=i.preventOnFilter,a=t.type,d=t.touches&&t.touches[0]||t.pointerType&&t.pointerType==="touch"&&t,f=(d||t).target,u=t.target.shadowRoot&&(t.path&&t.path[0]||t.composedPath&&t.composedPath()[0])||f,y=i.filter;if(ys(n),!N&&!(/mousedown|pointerdown/.test(a)&&t.button!==0||i.disabled)&&!u.isContentEditable&&!(!this.nativeDraggable&&Gn&&f&&f.tagName.toUpperCase()==="SELECT")&&(f=St(f,i.draggable,n,!1),!(f&&f.animated)&&Ar!==f)){if(Ln=vt(f),Jn=vt(f,i.draggable),typeof y=="function"){if(y.call(this,t,f,this)){it({sortable:r,rootEl:u,name:"filter",targetEl:f,toEl:n,fromEl:n}),at("filter",r,{evt:t}),o&&t.preventDefault();return}}else if(y&&(y=y.split(",").some(function(m){if(m=St(u,m.trim(),n,!1),m)return it({sortable:r,rootEl:m,name:"filter",targetEl:f,fromEl:n,toEl:n}),at("filter",r,{evt:t}),!0}),y)){o&&t.preventDefault();return}i.handle&&!St(u,i.handle,n,!1)||this._prepareDragStart(t,d,f)}}},_prepareDragStart:function(t,r,n){var i=this,o=i.el,a=i.options,d=o.ownerDocument,f;if(n&&!N&&n.parentNode===o){var u=qe(n);if(ke=o,N=n,Ve=N.parentNode,bn=N.nextSibling,Ar=n,wr=a.group,se.dragged=N,mn={target:N,clientX:(r||t).clientX,clientY:(r||t).clientY},wo=mn.clientX-u.left,xo=mn.clientY-u.top,this._lastX=(r||t).clientX,this._lastY=(r||t).clientY,N.style["will-change"]="all",f=function(){if(at("delayEnded",i,{evt:t}),se.eventCanceled){i._onDrop();return}i._disableDelayedDragEvents(),!mo&&i.nativeDraggable&&(N.draggable=!0),i._triggerDragStart(t,r),it({sortable:i,name:"choose",originalEvent:t}),ct(N,a.chosenClass,!0)},a.ignore.split(",").forEach(function(y){_o(N,y.trim(),ui)}),Oe(d,"dragover",gn),Oe(d,"mousemove",gn),Oe(d,"touchmove",gn),a.supportPointer?(Oe(d,"pointerup",i._onDrop),!this.nativeDraggable&&Oe(d,"pointercancel",i._onDrop)):(Oe(d,"mouseup",i._onDrop),Oe(d,"touchend",i._onDrop),Oe(d,"touchcancel",i._onDrop)),mo&&this.nativeDraggable&&(this.options.touchStartThreshold=4,N.draggable=!0),at("delayStart",this,{evt:t}),a.delay&&(!a.delayOnTouchOnly||r)&&(!this.nativeDraggable||!(er||Wt))){if(se.eventCanceled){this._onDrop();return}a.supportPointer?(Oe(d,"pointerup",i._disableDelayedDrag),Oe(d,"pointercancel",i._disableDelayedDrag)):(Oe(d,"mouseup",i._disableDelayedDrag),Oe(d,"touchend",i._disableDelayedDrag),Oe(d,"touchcancel",i._disableDelayedDrag)),Oe(d,"mousemove",i._delayedDragTouchMoveHandler),Oe(d,"touchmove",i._delayedDragTouchMoveHandler),a.supportPointer&&Oe(d,"pointermove",i._delayedDragTouchMoveHandler),i._dragStartTimer=setTimeout(f,a.delay)}else f()}},_delayedDragTouchMoveHandler:function(t){var r=t.touches?t.touches[0]:t;Math.max(Math.abs(r.clientX-this._lastX),Math.abs(r.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){N&&ui(N),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var t=this.el.ownerDocument;Ee(t,"mouseup",this._disableDelayedDrag),Ee(t,"touchend",this._disableDelayedDrag),Ee(t,"touchcancel",this._disableDelayedDrag),Ee(t,"pointerup",this._disableDelayedDrag),Ee(t,"pointercancel",this._disableDelayedDrag),Ee(t,"mousemove",this._delayedDragTouchMoveHandler),Ee(t,"touchmove",this._delayedDragTouchMoveHandler),Ee(t,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(t,r){r=r||t.pointerType=="touch"&&t,!this.nativeDraggable||r?this.options.supportPointer?Oe(document,"pointermove",this._onTouchMove):r?Oe(document,"touchmove",this._onTouchMove):Oe(document,"mousemove",this._onTouchMove):(Oe(N,"dragend",this),Oe(ke,"dragstart",this._onDragStart));try{document.selection?_r(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch{}},_dragStarted:function(t,r){if(In=!1,ke&&N){at("dragStarted",this,{evt:r}),this.nativeDraggable&&Oe(document,"dragover",us);var n=this.options;!t&&ct(N,n.dragClass,!1),ct(N,n.ghostClass,!0),se.active=this,t&&this._appendGhost(),it({sortable:this,name:"start",originalEvent:r})}else this._nulling()},_emulateDragOver:function(){if(Ot){this._lastX=Ot.clientX,this._lastY=Ot.clientY,Fo();for(var t=document.elementFromPoint(Ot.clientX,Ot.clientY),r=t;t&&t.shadowRoot&&(t=t.shadowRoot.elementFromPoint(Ot.clientX,Ot.clientY),t!==r);)r=t;if(N.parentNode[st]._isOutsideThisEl(t),r)do{if(r[st]){var n=void 0;if(n=r[st]._onDragOver({clientX:Ot.clientX,clientY:Ot.clientY,target:t,rootEl:r}),n&&!this.options.dragoverBubble)break}t=r}while(r=Do(r));No()}},_onTouchMove:function(t){if(mn){var r=this.options,n=r.fallbackTolerance,i=r.fallbackOffset,o=t.touches?t.touches[0]:t,a=ue&&Fn(ue,!0),d=ue&&a&&a.a,f=ue&&a&&a.d,u=Er&&nt&&yo(nt),y=(o.clientX-mn.clientX+i.x)/(d||1)+(u?u[0]-ci[0]:0)/(d||1),m=(o.clientY-mn.clientY+i.y)/(f||1)+(u?u[1]-ci[1]:0)/(f||1);if(!se.active&&!In){if(n&&Math.max(Math.abs(o.clientX-this._lastX),Math.abs(o.clientY-this._lastY))<n)return;this._onDragStart(t,!0)}if(ue){a?(a.e+=y-(li||0),a.f+=m-(fi||0)):a={a:1,b:0,c:0,d:1,e:y,f:m};var O="matrix(".concat(a.a,",").concat(a.b,",").concat(a.c,",").concat(a.d,",").concat(a.e,",").concat(a.f,")");ae(ue,"webkitTransform",O),ae(ue,"mozTransform",O),ae(ue,"msTransform",O),ae(ue,"transform",O),li=y,fi=m,Ot=o}t.cancelable&&t.preventDefault()}},_appendGhost:function(){if(!ue){var t=this.options.fallbackOnBody?document.body:ke,r=qe(N,!0,Er,!0,t),n=this.options;if(Er){for(nt=t;ae(nt,"position")==="static"&&ae(nt,"transform")==="none"&&nt!==document;)nt=nt.parentNode;nt!==document.body&&nt!==document.documentElement?(nt===document&&(nt=Pt()),r.top+=nt.scrollTop,r.left+=nt.scrollLeft):nt=Pt(),ci=yo(nt)}ue=N.cloneNode(!0),ct(ue,n.ghostClass,!1),ct(ue,n.fallbackClass,!0),ct(ue,n.dragClass,!0),ae(ue,"transition",""),ae(ue,"transform",""),ae(ue,"box-sizing","border-box"),ae(ue,"margin",0),ae(ue,"top",r.top),ae(ue,"left",r.left),ae(ue,"width",r.width),ae(ue,"height",r.height),ae(ue,"opacity","0.8"),ae(ue,"position",Er?"absolute":"fixed"),ae(ue,"zIndex","100000"),ae(ue,"pointerEvents","none"),se.ghost=ue,t.appendChild(ue),ae(ue,"transform-origin",wo/parseInt(ue.style.width)*100+"% "+xo/parseInt(ue.style.height)*100+"%")}},_onDragStart:function(t,r){var n=this,i=t.dataTransfer,o=n.options;if(at("dragStart",this,{evt:t}),se.eventCanceled){this._onDrop();return}at("setupClone",this),se.eventCanceled||(We=Po(N),We.removeAttribute("id"),We.draggable=!1,We.style["will-change"]="",this._hideClone(),ct(We,this.options.chosenClass,!1),se.clone=We),n.cloneId=_r(function(){at("clone",n),!se.eventCanceled&&(n.options.removeCloneOnHide||ke.insertBefore(We,N),n._hideClone(),it({sortable:n,name:"clone"}))}),!r&&ct(N,o.dragClass,!0),r?(Pr=!0,n._loopId=setInterval(n._emulateDragOver,50)):(Ee(document,"mouseup",n._onDrop),Ee(document,"touchend",n._onDrop),Ee(document,"touchcancel",n._onDrop),i&&(i.effectAllowed="move",o.setData&&o.setData.call(n,i,N)),Oe(document,"drop",n),ae(N,"transform","translateZ(0)")),In=!0,n._dragStartId=_r(n._dragStarted.bind(n,r,t)),Oe(document,"selectstart",n),Yn=!0,window.getSelection().removeAllRanges(),Gn&&ae(document.body,"user-select","none")},_onDragOver:function(t){var r=this.el,n=t.target,i,o,a,d=this.options,f=d.group,u=se.active,y=wr===f,m=d.sort,O=Qe||u,E,S=this,C=!1;if(vi)return;function I(R,Z){at(R,S,Mt({evt:t,isOwner:y,axis:E?"vertical":"horizontal",revert:a,dragRect:i,targetRect:o,canSort:m,fromSortable:O,target:n,completed:A,onMove:function(Rt,Ut){return Or(ke,r,N,i,Rt,qe(Rt),t,Ut)},changed:k},Z))}function $(){I("dragOverAnimationCapture"),S.captureAnimationState(),S!==O&&O.captureAnimationState()}function A(R){return I("dragOverCompleted",{insertion:R}),R&&(y?u._hideClone():u._showClone(S),S!==O&&(ct(N,Qe?Qe.options.ghostClass:u.options.ghostClass,!1),ct(N,d.ghostClass,!0)),Qe!==S&&S!==se.active?Qe=S:S===se.active&&Qe&&(Qe=null),O===S&&(S._ignoreWhileAnimating=n),S.animateAll(function(){I("dragOverAnimationComplete"),S._ignoreWhileAnimating=null}),S!==O&&(O.animateAll(),O._ignoreWhileAnimating=null)),(n===N&&!N.animated||n===r&&!n.animated)&&(Rn=null),!d.dragoverBubble&&!t.rootEl&&n!==document&&(N.parentNode[st]._isOutsideThisEl(t.target),!R&&gn(t)),!d.dragoverBubble&&t.stopPropagation&&t.stopPropagation(),C=!0}function k(){ut=vt(N),on=vt(N,d.draggable),it({sortable:S,name:"change",toEl:r,newIndex:ut,newDraggableIndex:on,originalEvent:t})}if(t.preventDefault!==void 0&&t.cancelable&&t.preventDefault(),n=St(n,d.draggable,r,!0),I("dragOver"),se.eventCanceled)return C;if(N.contains(t.target)||n.animated&&n.animatingX&&n.animatingY||S._ignoreWhileAnimating===n)return A(!1);if(Pr=!1,u&&!d.disabled&&(y?m||(a=Ve!==ke):Qe===this||(this.lastPutMode=wr.checkPull(this,u,N,t))&&f.checkPut(this,u,N,t))){if(E=this._getDirection(t,n)==="vertical",i=qe(N),I("dragOverValid"),se.eventCanceled)return C;if(a)return Ve=ke,$(),this._hideClone(),I("revert"),se.eventCanceled||(bn?ke.insertBefore(N,bn):ke.appendChild(N)),A(!0);var Y=wi(r,d.draggable);if(!Y||vs(t,E,this)&&!Y.animated){if(Y===N)return A(!1);if(Y&&r===t.target&&(n=Y),n&&(o=qe(n)),Or(ke,r,N,i,n,o,t,!!n)!==!1)return $(),Y&&Y.nextSibling?r.insertBefore(N,Y.nextSibling):r.appendChild(N),Ve=r,k(),A(!0)}else if(Y&&hs(t,E,this)){var ne=Nn(r,0,d,!0);if(ne===N)return A(!1);if(n=ne,o=qe(n),Or(ke,r,N,i,n,o,t,!1)!==!1)return $(),r.insertBefore(N,ne),Ve=r,k(),A(!0)}else if(n.parentNode===r){o=qe(n);var J=0,V,de=N.parentNode!==r,X=!fs(N.animated&&N.toRect||i,n.animated&&n.toRect||o,E),Q=E?"top":"left",me=bo(n,"top","top")||bo(N,"top","top"),l=me?me.scrollTop:void 0;Rn!==n&&(V=o[Q],Zn=!1,xr=!X&&d.invertSwap||de),J=ms(t,n,o,E,X?1:d.swapThreshold,d.invertedSwapThreshold==null?d.swapThreshold:d.invertedSwapThreshold,xr,Rn===n);var h;if(J!==0){var v=vt(N);do v-=J,h=Ve.children[v];while(h&&(ae(h,"display")==="none"||h===ue))}if(J===0||h===n)return A(!1);Rn=n,Qn=J;var p=n.nextElementSibling,j=!1;j=J===1;var M=Or(ke,r,N,i,n,o,t,j);if(M!==!1)return(M===1||M===-1)&&(j=M===1),vi=!0,setTimeout(ps,30),$(),j&&!p?r.appendChild(N):n.parentNode.insertBefore(N,j?p:n),me&&To(me,0,l-me.scrollTop),Ve=N.parentNode,V!==void 0&&!xr&&(Dr=Math.abs(V-qe(n)[Q])),k(),A(!0)}if(r.contains(N))return A(!1)}return!1},_ignoreWhileAnimating:null,_offMoveEvents:function(){Ee(document,"mousemove",this._onTouchMove),Ee(document,"touchmove",this._onTouchMove),Ee(document,"pointermove",this._onTouchMove),Ee(document,"dragover",gn),Ee(document,"mousemove",gn),Ee(document,"touchmove",gn)},_offUpEvents:function(){var t=this.el.ownerDocument;Ee(t,"mouseup",this._onDrop),Ee(t,"touchend",this._onDrop),Ee(t,"pointerup",this._onDrop),Ee(t,"pointercancel",this._onDrop),Ee(t,"touchcancel",this._onDrop),Ee(document,"selectstart",this)},_onDrop:function(t){var r=this.el,n=this.options;if(ut=vt(N),on=vt(N,n.draggable),at("drop",this,{evt:t}),Ve=N&&N.parentNode,ut=vt(N),on=vt(N,n.draggable),se.eventCanceled){this._nulling();return}In=!1,xr=!1,Zn=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),mi(this.cloneId),mi(this._dragStartId),this.nativeDraggable&&(Ee(document,"drop",this),Ee(r,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),Gn&&ae(document.body,"user-select",""),ae(N,"transform",""),t&&(Yn&&(t.cancelable&&t.preventDefault(),!n.dropBubble&&t.stopPropagation()),ue&&ue.parentNode&&ue.parentNode.removeChild(ue),(ke===Ve||Qe&&Qe.lastPutMode!=="clone")&&We&&We.parentNode&&We.parentNode.removeChild(We),N&&(this.nativeDraggable&&Ee(N,"dragend",this),ui(N),N.style["will-change"]="",Yn&&!In&&ct(N,Qe?Qe.options.ghostClass:this.options.ghostClass,!1),ct(N,this.options.chosenClass,!1),it({sortable:this,name:"unchoose",toEl:Ve,newIndex:null,newDraggableIndex:null,originalEvent:t}),ke!==Ve?(ut>=0&&(it({rootEl:Ve,name:"add",toEl:Ve,fromEl:ke,originalEvent:t}),it({sortable:this,name:"remove",toEl:Ve,originalEvent:t}),it({rootEl:Ve,name:"sort",toEl:Ve,fromEl:ke,originalEvent:t}),it({sortable:this,name:"sort",toEl:Ve,originalEvent:t})),Qe&&Qe.save()):ut!==Ln&&ut>=0&&(it({sortable:this,name:"update",toEl:Ve,originalEvent:t}),it({sortable:this,name:"sort",toEl:Ve,originalEvent:t})),se.active&&((ut==null||ut===-1)&&(ut=Ln,on=Jn),it({sortable:this,name:"end",toEl:Ve,originalEvent:t}),this.save()))),this._nulling()},_nulling:function(){at("nulling",this),ke=N=Ve=ue=bn=We=Ar=an=mn=Ot=Yn=ut=on=Ln=Jn=Rn=Qn=Qe=wr=se.dragged=se.ghost=se.clone=se.active=null,Rr.forEach(function(t){t.checked=!0}),Rr.length=li=fi=0},handleEvent:function(t){switch(t.type){case"drop":case"dragend":this._onDrop(t);break;case"dragenter":case"dragover":N&&(this._onDragOver(t),ds(t));break;case"selectstart":t.preventDefault();break}},toArray:function(){for(var t=[],r,n=this.el.children,i=0,o=n.length,a=this.options;i<o;i++)r=n[i],St(r,a.draggable,this.el,!1)&&t.push(r.getAttribute(a.dataIdAttr)||bs(r));return t},sort:function(t,r){var n={},i=this.el;this.toArray().forEach(function(o,a){var d=i.children[a];St(d,this.options.draggable,i,!1)&&(n[o]=d)},this),r&&this.captureAnimationState(),t.forEach(function(o){n[o]&&(i.removeChild(n[o]),i.appendChild(n[o]))}),r&&this.animateAll()},save:function(){var t=this.options.store;t&&t.set&&t.set(this)},closest:function(t,r){return St(t,r||this.options.draggable,this.el,!1)},option:function(t,r){var n=this.options;if(r===void 0)return n[t];var i=tr.modifyOption(this,t,r);typeof i<"u"?n[t]=i:n[t]=r,t==="group"&&Lo(n)},destroy:function(){at("destroy",this);var t=this.el;t[st]=null,Ee(t,"mousedown",this._onTapStart),Ee(t,"touchstart",this._onTapStart),Ee(t,"pointerdown",this._onTapStart),this.nativeDraggable&&(Ee(t,"dragover",this),Ee(t,"dragenter",this)),Array.prototype.forEach.call(t.querySelectorAll("[draggable]"),function(r){r.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),Mr.splice(Mr.indexOf(this.el),1),this.el=t=null},_hideClone:function(){if(!an){if(at("hideClone",this),se.eventCanceled)return;ae(We,"display","none"),this.options.removeCloneOnHide&&We.parentNode&&We.parentNode.removeChild(We),an=!0}},_showClone:function(t){if(t.lastPutMode!=="clone"){this._hideClone();return}if(an){if(at("showClone",this),se.eventCanceled)return;N.parentNode==ke&&!this.options.group.revertClone?ke.insertBefore(We,N):bn?ke.insertBefore(We,bn):ke.appendChild(We),this.options.group.revertClone&&this.animate(N,We),ae(We,"display",""),an=!1}}};function ds(e){e.dataTransfer&&(e.dataTransfer.dropEffect="move"),e.cancelable&&e.preventDefault()}function Or(e,t,r,n,i,o,a,d){var f,u=e[st],y=u.options.onMove,m;return window.CustomEvent&&!Wt&&!er?f=new CustomEvent("move",{bubbles:!0,cancelable:!0}):(f=document.createEvent("Event"),f.initEvent("move",!0,!0)),f.to=t,f.from=e,f.dragged=r,f.draggedRect=n,f.related=i||t,f.relatedRect=o||qe(t),f.willInsertAfter=d,f.originalEvent=a,e.dispatchEvent(f),y&&(m=y.call(u,f,a)),m}function ui(e){e.draggable=!1}function ps(){vi=!1}function hs(e,t,r){var n=qe(Nn(r.el,0,r.options,!0)),i=Mo(r.el,r.options,ue),o=10;return t?e.clientX<i.left-o||e.clientY<n.top&&e.clientX<n.right:e.clientY<i.top-o||e.clientY<n.bottom&&e.clientX<n.left}function vs(e,t,r){var n=qe(wi(r.el,r.options.draggable)),i=Mo(r.el,r.options,ue),o=10;return t?e.clientX>i.right+o||e.clientY>n.bottom&&e.clientX>n.left:e.clientY>i.bottom+o||e.clientX>n.right&&e.clientY>n.top}function ms(e,t,r,n,i,o,a,d){var f=n?e.clientY:e.clientX,u=n?r.height:r.width,y=n?r.top:r.left,m=n?r.bottom:r.right,O=!1;if(!a){if(d&&Dr<u*i){if(!Zn&&(Qn===1?f>y+u*o/2:f<m-u*o/2)&&(Zn=!0),Zn)O=!0;else if(Qn===1?f<y+Dr:f>m-Dr)return-Qn}else if(f>y+u*(1-i)/2&&f<m-u*(1-i)/2)return gs(t)}return O=O||a,O&&(f<y+u*o/2||f>m-u*o/2)?f>y+u/2?1:-1:0}function gs(e){return vt(N)<vt(e)?1:-1}function bs(e){for(var t=e.tagName+e.className+e.src+e.href+e.textContent,r=t.length,n=0;r--;)n+=t.charCodeAt(r);return n.toString(36)}function ys(e){Rr.length=0;for(var t=e.getElementsByTagName("input"),r=t.length;r--;){var n=t[r];n.checked&&Rr.push(n)}}function _r(e){return setTimeout(e,0)}function mi(e){return clearTimeout(e)}Lr&&Oe(document,"touchmove",function(e){(se.active||In)&&e.cancelable&&e.preventDefault()});se.utils={on:Oe,off:Ee,css:ae,find:_o,is:function(t,r){return!!St(t,r,t,!1)},extend:ts,throttle:Co,closest:St,toggleClass:ct,clone:Po,index:vt,nextTick:_r,cancelNextTick:mi,detectDirection:Io,getChild:Nn,expando:st};se.get=function(e){return e[st]};se.mount=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];t[0].constructor===Array&&(t=t[0]),t.forEach(function(n){if(!n.prototype||!n.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(n));n.utils&&(se.utils=Mt(Mt({},se.utils),n.utils)),tr.mount(n)})};se.create=function(e,t){return new se(e,t)};se.version=Za;var Xe=[],Xn,gi,bi=!1,di,pi,Ir,qn;function ws(){function e(){this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0};for(var t in this)t.charAt(0)==="_"&&typeof this[t]=="function"&&(this[t]=this[t].bind(this))}return e.prototype={dragStarted:function(r){var n=r.originalEvent;this.sortable.nativeDraggable?Oe(document,"dragover",this._handleAutoScroll):this.options.supportPointer?Oe(document,"pointermove",this._handleFallbackAutoScroll):n.touches?Oe(document,"touchmove",this._handleFallbackAutoScroll):Oe(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(r){var n=r.originalEvent;!this.options.dragOverBubble&&!n.rootEl&&this._handleAutoScroll(n)},drop:function(){this.sortable.nativeDraggable?Ee(document,"dragover",this._handleAutoScroll):(Ee(document,"pointermove",this._handleFallbackAutoScroll),Ee(document,"touchmove",this._handleFallbackAutoScroll),Ee(document,"mousemove",this._handleFallbackAutoScroll)),Oo(),Cr(),ns()},nulling:function(){Ir=gi=Xn=bi=qn=di=pi=null,Xe.length=0},_handleFallbackAutoScroll:function(r){this._handleAutoScroll(r,!0)},_handleAutoScroll:function(r,n){var i=this,o=(r.touches?r.touches[0]:r).clientX,a=(r.touches?r.touches[0]:r).clientY,d=document.elementFromPoint(o,a);if(Ir=r,n||this.options.forceAutoScrollFallback||er||Wt||Gn){hi(r,this.options,d,n);var f=sn(d,!0);bi&&(!qn||o!==di||a!==pi)&&(qn&&Oo(),qn=setInterval(function(){var u=sn(document.elementFromPoint(o,a),!0);u!==f&&(f=u,Cr()),hi(r,i.options,u,n)},10),di=o,pi=a)}else{if(!this.options.bubbleScroll||sn(d,!0)===Pt()){Cr();return}hi(r,this.options,sn(d,!1),!1)}}},$t(e,{pluginName:"scroll",initializeByDefault:!0})}function Cr(){Xe.forEach(function(e){clearInterval(e.pid)}),Xe=[]}function Oo(){clearInterval(qn)}var hi=Co(function(e,t,r,n){if(t.scroll){var i=(e.touches?e.touches[0]:e).clientX,o=(e.touches?e.touches[0]:e).clientY,a=t.scrollSensitivity,d=t.scrollSpeed,f=Pt(),u=!1,y;gi!==r&&(gi=r,Cr(),Xn=t.scroll,y=t.scrollFn,Xn===!0&&(Xn=sn(r,!0)));var m=0,O=Xn;do{var E=O,S=qe(E),C=S.top,I=S.bottom,$=S.left,A=S.right,k=S.width,Y=S.height,ne=void 0,J=void 0,V=E.scrollWidth,de=E.scrollHeight,X=ae(E),Q=E.scrollLeft,me=E.scrollTop;E===f?(ne=k<V&&(X.overflowX==="auto"||X.overflowX==="scroll"||X.overflowX==="visible"),J=Y<de&&(X.overflowY==="auto"||X.overflowY==="scroll"||X.overflowY==="visible")):(ne=k<V&&(X.overflowX==="auto"||X.overflowX==="scroll"),J=Y<de&&(X.overflowY==="auto"||X.overflowY==="scroll"));var l=ne&&(Math.abs(A-i)<=a&&Q+k<V)-(Math.abs($-i)<=a&&!!Q),h=J&&(Math.abs(I-o)<=a&&me+Y<de)-(Math.abs(C-o)<=a&&!!me);if(!Xe[m])for(var v=0;v<=m;v++)Xe[v]||(Xe[v]={});(Xe[m].vx!=l||Xe[m].vy!=h||Xe[m].el!==E)&&(Xe[m].el=E,Xe[m].vx=l,Xe[m].vy=h,clearInterval(Xe[m].pid),(l!=0||h!=0)&&(u=!0,Xe[m].pid=setInterval(function(){n&&this.layer===0&&se.active._onTouchMove(Ir);var p=Xe[this.layer].vy?Xe[this.layer].vy*d:0,j=Xe[this.layer].vx?Xe[this.layer].vx*d:0;typeof y=="function"&&y.call(se.dragged.parentNode[st],j,p,e,Ir,Xe[this.layer].el)!=="continue"||To(Xe[this.layer].el,j,p)}.bind({layer:m}),24))),m++}while(t.bubbleScroll&&O!==f&&(O=sn(O,!1)));bi=u}},30),ko=function(t){var r=t.originalEvent,n=t.putSortable,i=t.dragEl,o=t.activeSortable,a=t.dispatchSortableEvent,d=t.hideGhostForTarget,f=t.unhideGhostForTarget;if(r){var u=n||o;d();var y=r.changedTouches&&r.changedTouches.length?r.changedTouches[0]:r,m=document.elementFromPoint(y.clientX,y.clientY);f(),u&&!u.el.contains(m)&&(a("spill"),this.onSpill({dragEl:i,putSortable:n}))}};function xi(){}xi.prototype={startIndex:null,dragStart:function(t){var r=t.oldDraggableIndex;this.startIndex=r},onSpill:function(t){var r=t.dragEl,n=t.putSortable;this.sortable.captureAnimationState(),n&&n.captureAnimationState();var i=Nn(this.sortable.el,this.startIndex,this.options);i?this.sortable.el.insertBefore(r,i):this.sortable.el.appendChild(r),this.sortable.animateAll(),n&&n.animateAll()},drop:ko};$t(xi,{pluginName:"revertOnSpill"});function Ei(){}Ei.prototype={onSpill:function(t){var r=t.dragEl,n=t.putSortable,i=n||this.sortable;i.captureAnimationState(),r.parentNode&&r.parentNode.removeChild(r),i.animateAll()},drop:ko};$t(Ei,{pluginName:"removeOnSpill"});se.mount(new ws);se.mount(Ei,xi);var Oi=se;window.Sortable=Oi;var jo=e=>{e.directive("sortable",t=>{let r=parseInt(t.dataset?.sortableAnimationDuration);r!==0&&!r&&(r=300),t.sortable=Oi.create(t,{group:t.getAttribute("x-sortable-group"),draggable:"[x-sortable-item]",handle:"[x-sortable-handle]",dataIdAttr:"x-sortable-item",animation:r,ghostClass:"fi-sortable-ghost"})})};var xs=Object.create,Di=Object.defineProperty,Es=Object.getPrototypeOf,Os=Object.prototype.hasOwnProperty,Ss=Object.getOwnPropertyNames,As=Object.getOwnPropertyDescriptor,Ds=e=>Di(e,"__esModule",{value:!0}),Bo=(e,t)=>()=>(t||(t={exports:{}},e(t.exports,t)),t.exports),_s=(e,t,r)=>{if(t&&typeof t=="object"||typeof t=="function")for(let n of Ss(t))!Os.call(e,n)&&n!=="default"&&Di(e,n,{get:()=>t[n],enumerable:!(r=As(t,n))||r.enumerable});return e},Ho=e=>_s(Ds(Di(e!=null?xs(Es(e)):{},"default",e&&e.__esModule&&"default"in e?{get:()=>e.default,enumerable:!0}:{value:e,enumerable:!0})),e),Cs=Bo(e=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});function t(c){var s=c.getBoundingClientRect();return{width:s.width,height:s.height,top:s.top,right:s.right,bottom:s.bottom,left:s.left,x:s.left,y:s.top}}function r(c){if(c==null)return window;if(c.toString()!=="[object Window]"){var s=c.ownerDocument;return s&&s.defaultView||window}return c}function n(c){var s=r(c),b=s.pageXOffset,T=s.pageYOffset;return{scrollLeft:b,scrollTop:T}}function i(c){var s=r(c).Element;return c instanceof s||c instanceof Element}function o(c){var s=r(c).HTMLElement;return c instanceof s||c instanceof HTMLElement}function a(c){if(typeof ShadowRoot>"u")return!1;var s=r(c).ShadowRoot;return c instanceof s||c instanceof ShadowRoot}function d(c){return{scrollLeft:c.scrollLeft,scrollTop:c.scrollTop}}function f(c){return c===r(c)||!o(c)?n(c):d(c)}function u(c){return c?(c.nodeName||"").toLowerCase():null}function y(c){return((i(c)?c.ownerDocument:c.document)||window.document).documentElement}function m(c){return t(y(c)).left+n(c).scrollLeft}function O(c){return r(c).getComputedStyle(c)}function E(c){var s=O(c),b=s.overflow,T=s.overflowX,P=s.overflowY;return/auto|scroll|overlay|hidden/.test(b+P+T)}function S(c,s,b){b===void 0&&(b=!1);var T=y(s),P=t(c),F=o(s),U={scrollLeft:0,scrollTop:0},H={x:0,y:0};return(F||!F&&!b)&&((u(s)!=="body"||E(T))&&(U=f(s)),o(s)?(H=t(s),H.x+=s.clientLeft,H.y+=s.clientTop):T&&(H.x=m(T))),{x:P.left+U.scrollLeft-H.x,y:P.top+U.scrollTop-H.y,width:P.width,height:P.height}}function C(c){var s=t(c),b=c.offsetWidth,T=c.offsetHeight;return Math.abs(s.width-b)<=1&&(b=s.width),Math.abs(s.height-T)<=1&&(T=s.height),{x:c.offsetLeft,y:c.offsetTop,width:b,height:T}}function I(c){return u(c)==="html"?c:c.assignedSlot||c.parentNode||(a(c)?c.host:null)||y(c)}function $(c){return["html","body","#document"].indexOf(u(c))>=0?c.ownerDocument.body:o(c)&&E(c)?c:$(I(c))}function A(c,s){var b;s===void 0&&(s=[]);var T=$(c),P=T===((b=c.ownerDocument)==null?void 0:b.body),F=r(T),U=P?[F].concat(F.visualViewport||[],E(T)?T:[]):T,H=s.concat(U);return P?H:H.concat(A(I(U)))}function k(c){return["table","td","th"].indexOf(u(c))>=0}function Y(c){return!o(c)||O(c).position==="fixed"?null:c.offsetParent}function ne(c){var s=navigator.userAgent.toLowerCase().indexOf("firefox")!==-1,b=navigator.userAgent.indexOf("Trident")!==-1;if(b&&o(c)){var T=O(c);if(T.position==="fixed")return null}for(var P=I(c);o(P)&&["html","body"].indexOf(u(P))<0;){var F=O(P);if(F.transform!=="none"||F.perspective!=="none"||F.contain==="paint"||["transform","perspective"].indexOf(F.willChange)!==-1||s&&F.willChange==="filter"||s&&F.filter&&F.filter!=="none")return P;P=P.parentNode}return null}function J(c){for(var s=r(c),b=Y(c);b&&k(b)&&O(b).position==="static";)b=Y(b);return b&&(u(b)==="html"||u(b)==="body"&&O(b).position==="static")?s:b||ne(c)||s}var V="top",de="bottom",X="right",Q="left",me="auto",l=[V,de,X,Q],h="start",v="end",p="clippingParents",j="viewport",M="popper",R="reference",Z=l.reduce(function(c,s){return c.concat([s+"-"+h,s+"-"+v])},[]),ze=[].concat(l,[me]).reduce(function(c,s){return c.concat([s,s+"-"+h,s+"-"+v])},[]),Rt="beforeRead",Ut="read",Fr="afterRead",Nr="beforeMain",kr="main",Vt="afterMain",nr="beforeWrite",jr="write",rr="afterWrite",It=[Rt,Ut,Fr,Nr,kr,Vt,nr,jr,rr];function Br(c){var s=new Map,b=new Set,T=[];c.forEach(function(F){s.set(F.name,F)});function P(F){b.add(F.name);var U=[].concat(F.requires||[],F.requiresIfExists||[]);U.forEach(function(H){if(!b.has(H)){var G=s.get(H);G&&P(G)}}),T.push(F)}return c.forEach(function(F){b.has(F.name)||P(F)}),T}function mt(c){var s=Br(c);return It.reduce(function(b,T){return b.concat(s.filter(function(P){return P.phase===T}))},[])}function zt(c){var s;return function(){return s||(s=new Promise(function(b){Promise.resolve().then(function(){s=void 0,b(c())})})),s}}function At(c){for(var s=arguments.length,b=new Array(s>1?s-1:0),T=1;T<s;T++)b[T-1]=arguments[T];return[].concat(b).reduce(function(P,F){return P.replace(/%s/,F)},c)}var Dt='Popper: modifier "%s" provided an invalid %s property, expected %s but got %s',Hr='Popper: modifier "%s" requires "%s", but "%s" modifier is not available',Ze=["name","enabled","phase","fn","effect","requires","options"];function $r(c){c.forEach(function(s){Object.keys(s).forEach(function(b){switch(b){case"name":typeof s.name!="string"&&console.error(At(Dt,String(s.name),'"name"','"string"','"'+String(s.name)+'"'));break;case"enabled":typeof s.enabled!="boolean"&&console.error(At(Dt,s.name,'"enabled"','"boolean"','"'+String(s.enabled)+'"'));case"phase":It.indexOf(s.phase)<0&&console.error(At(Dt,s.name,'"phase"',"either "+It.join(", "),'"'+String(s.phase)+'"'));break;case"fn":typeof s.fn!="function"&&console.error(At(Dt,s.name,'"fn"','"function"','"'+String(s.fn)+'"'));break;case"effect":typeof s.effect!="function"&&console.error(At(Dt,s.name,'"effect"','"function"','"'+String(s.fn)+'"'));break;case"requires":Array.isArray(s.requires)||console.error(At(Dt,s.name,'"requires"','"array"','"'+String(s.requires)+'"'));break;case"requiresIfExists":Array.isArray(s.requiresIfExists)||console.error(At(Dt,s.name,'"requiresIfExists"','"array"','"'+String(s.requiresIfExists)+'"'));break;case"options":case"data":break;default:console.error('PopperJS: an invalid property has been provided to the "'+s.name+'" modifier, valid properties are '+Ze.map(function(T){return'"'+T+'"'}).join(", ")+'; but "'+b+'" was provided.')}s.requires&&s.requires.forEach(function(T){c.find(function(P){return P.name===T})==null&&console.error(At(Hr,String(s.name),T,T))})})})}function Wr(c,s){var b=new Set;return c.filter(function(T){var P=s(T);if(!b.has(P))return b.add(P),!0})}function ot(c){return c.split("-")[0]}function Ur(c){var s=c.reduce(function(b,T){var P=b[T.name];return b[T.name]=P?Object.assign({},P,T,{options:Object.assign({},P.options,T.options),data:Object.assign({},P.data,T.data)}):T,b},{});return Object.keys(s).map(function(b){return s[b]})}function ir(c){var s=r(c),b=y(c),T=s.visualViewport,P=b.clientWidth,F=b.clientHeight,U=0,H=0;return T&&(P=T.width,F=T.height,/^((?!chrome|android).)*safari/i.test(navigator.userAgent)||(U=T.offsetLeft,H=T.offsetTop)),{width:P,height:F,x:U+m(c),y:H}}var gt=Math.max,ln=Math.min,Yt=Math.round;function or(c){var s,b=y(c),T=n(c),P=(s=c.ownerDocument)==null?void 0:s.body,F=gt(b.scrollWidth,b.clientWidth,P?P.scrollWidth:0,P?P.clientWidth:0),U=gt(b.scrollHeight,b.clientHeight,P?P.scrollHeight:0,P?P.clientHeight:0),H=-T.scrollLeft+m(c),G=-T.scrollTop;return O(P||b).direction==="rtl"&&(H+=gt(b.clientWidth,P?P.clientWidth:0)-F),{width:F,height:U,x:H,y:G}}function kn(c,s){var b=s.getRootNode&&s.getRootNode();if(c.contains(s))return!0;if(b&&a(b)){var T=s;do{if(T&&c.isSameNode(T))return!0;T=T.parentNode||T.host}while(T)}return!1}function Xt(c){return Object.assign({},c,{left:c.x,top:c.y,right:c.x+c.width,bottom:c.y+c.height})}function ar(c){var s=t(c);return s.top=s.top+c.clientTop,s.left=s.left+c.clientLeft,s.bottom=s.top+c.clientHeight,s.right=s.left+c.clientWidth,s.width=c.clientWidth,s.height=c.clientHeight,s.x=s.left,s.y=s.top,s}function sr(c,s){return s===j?Xt(ir(c)):o(s)?ar(s):Xt(or(y(c)))}function yn(c){var s=A(I(c)),b=["absolute","fixed"].indexOf(O(c).position)>=0,T=b&&o(c)?J(c):c;return i(T)?s.filter(function(P){return i(P)&&kn(P,T)&&u(P)!=="body"}):[]}function wn(c,s,b){var T=s==="clippingParents"?yn(c):[].concat(s),P=[].concat(T,[b]),F=P[0],U=P.reduce(function(H,G){var oe=sr(c,G);return H.top=gt(oe.top,H.top),H.right=ln(oe.right,H.right),H.bottom=ln(oe.bottom,H.bottom),H.left=gt(oe.left,H.left),H},sr(c,F));return U.width=U.right-U.left,U.height=U.bottom-U.top,U.x=U.left,U.y=U.top,U}function fn(c){return c.split("-")[1]}function dt(c){return["top","bottom"].indexOf(c)>=0?"x":"y"}function lr(c){var s=c.reference,b=c.element,T=c.placement,P=T?ot(T):null,F=T?fn(T):null,U=s.x+s.width/2-b.width/2,H=s.y+s.height/2-b.height/2,G;switch(P){case V:G={x:U,y:s.y-b.height};break;case de:G={x:U,y:s.y+s.height};break;case X:G={x:s.x+s.width,y:H};break;case Q:G={x:s.x-b.width,y:H};break;default:G={x:s.x,y:s.y}}var oe=P?dt(P):null;if(oe!=null){var z=oe==="y"?"height":"width";switch(F){case h:G[oe]=G[oe]-(s[z]/2-b[z]/2);break;case v:G[oe]=G[oe]+(s[z]/2-b[z]/2);break}}return G}function fr(){return{top:0,right:0,bottom:0,left:0}}function cr(c){return Object.assign({},fr(),c)}function ur(c,s){return s.reduce(function(b,T){return b[T]=c,b},{})}function qt(c,s){s===void 0&&(s={});var b=s,T=b.placement,P=T===void 0?c.placement:T,F=b.boundary,U=F===void 0?p:F,H=b.rootBoundary,G=H===void 0?j:H,oe=b.elementContext,z=oe===void 0?M:oe,_e=b.altBoundary,Fe=_e===void 0?!1:_e,De=b.padding,xe=De===void 0?0:De,Me=cr(typeof xe!="number"?xe:ur(xe,l)),Se=z===M?R:M,Be=c.elements.reference,Re=c.rects.popper,He=c.elements[Fe?Se:z],fe=wn(i(He)?He:He.contextElement||y(c.elements.popper),U,G),Pe=t(Be),Ce=lr({reference:Pe,element:Re,strategy:"absolute",placement:P}),Ne=Xt(Object.assign({},Re,Ce)),Le=z===M?Ne:Pe,Ye={top:fe.top-Le.top+Me.top,bottom:Le.bottom-fe.bottom+Me.bottom,left:fe.left-Le.left+Me.left,right:Le.right-fe.right+Me.right},$e=c.modifiersData.offset;if(z===M&&$e){var Ue=$e[P];Object.keys(Ye).forEach(function(wt){var et=[X,de].indexOf(wt)>=0?1:-1,Ft=[V,de].indexOf(wt)>=0?"y":"x";Ye[wt]+=Ue[Ft]*et})}return Ye}var dr="Popper: Invalid reference or popper argument provided. They must be either a DOM element or virtual element.",Vr="Popper: An infinite loop in the modifiers cycle has been detected! The cycle has been interrupted to prevent a browser crash.",xn={placement:"bottom",modifiers:[],strategy:"absolute"};function cn(){for(var c=arguments.length,s=new Array(c),b=0;b<c;b++)s[b]=arguments[b];return!s.some(function(T){return!(T&&typeof T.getBoundingClientRect=="function")})}function En(c){c===void 0&&(c={});var s=c,b=s.defaultModifiers,T=b===void 0?[]:b,P=s.defaultOptions,F=P===void 0?xn:P;return function(H,G,oe){oe===void 0&&(oe=F);var z={placement:"bottom",orderedModifiers:[],options:Object.assign({},xn,F),modifiersData:{},elements:{reference:H,popper:G},attributes:{},styles:{}},_e=[],Fe=!1,De={state:z,setOptions:function(Be){Me(),z.options=Object.assign({},F,z.options,Be),z.scrollParents={reference:i(H)?A(H):H.contextElement?A(H.contextElement):[],popper:A(G)};var Re=mt(Ur([].concat(T,z.options.modifiers)));z.orderedModifiers=Re.filter(function($e){return $e.enabled});var He=Wr([].concat(Re,z.options.modifiers),function($e){var Ue=$e.name;return Ue});if($r(He),ot(z.options.placement)===me){var fe=z.orderedModifiers.find(function($e){var Ue=$e.name;return Ue==="flip"});fe||console.error(['Popper: "auto" placements require the "flip" modifier be',"present and enabled to work."].join(" "))}var Pe=O(G),Ce=Pe.marginTop,Ne=Pe.marginRight,Le=Pe.marginBottom,Ye=Pe.marginLeft;return[Ce,Ne,Le,Ye].some(function($e){return parseFloat($e)})&&console.warn(['Popper: CSS "margin" styles cannot be used to apply padding',"between the popper and its reference element or boundary.","To replicate margin, use the `offset` modifier, as well as","the `padding` option in the `preventOverflow` and `flip`","modifiers."].join(" ")),xe(),De.update()},forceUpdate:function(){if(!Fe){var Be=z.elements,Re=Be.reference,He=Be.popper;if(!cn(Re,He)){console.error(dr);return}z.rects={reference:S(Re,J(He),z.options.strategy==="fixed"),popper:C(He)},z.reset=!1,z.placement=z.options.placement,z.orderedModifiers.forEach(function(Ue){return z.modifiersData[Ue.name]=Object.assign({},Ue.data)});for(var fe=0,Pe=0;Pe<z.orderedModifiers.length;Pe++){if(fe+=1,fe>100){console.error(Vr);break}if(z.reset===!0){z.reset=!1,Pe=-1;continue}var Ce=z.orderedModifiers[Pe],Ne=Ce.fn,Le=Ce.options,Ye=Le===void 0?{}:Le,$e=Ce.name;typeof Ne=="function"&&(z=Ne({state:z,options:Ye,name:$e,instance:De})||z)}}},update:zt(function(){return new Promise(function(Se){De.forceUpdate(),Se(z)})}),destroy:function(){Me(),Fe=!0}};if(!cn(H,G))return console.error(dr),De;De.setOptions(oe).then(function(Se){!Fe&&oe.onFirstUpdate&&oe.onFirstUpdate(Se)});function xe(){z.orderedModifiers.forEach(function(Se){var Be=Se.name,Re=Se.options,He=Re===void 0?{}:Re,fe=Se.effect;if(typeof fe=="function"){var Pe=fe({state:z,name:Be,instance:De,options:He}),Ce=function(){};_e.push(Pe||Ce)}})}function Me(){_e.forEach(function(Se){return Se()}),_e=[]}return De}}var On={passive:!0};function zr(c){var s=c.state,b=c.instance,T=c.options,P=T.scroll,F=P===void 0?!0:P,U=T.resize,H=U===void 0?!0:U,G=r(s.elements.popper),oe=[].concat(s.scrollParents.reference,s.scrollParents.popper);return F&&oe.forEach(function(z){z.addEventListener("scroll",b.update,On)}),H&&G.addEventListener("resize",b.update,On),function(){F&&oe.forEach(function(z){z.removeEventListener("scroll",b.update,On)}),H&&G.removeEventListener("resize",b.update,On)}}var jn={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:zr,data:{}};function Yr(c){var s=c.state,b=c.name;s.modifiersData[b]=lr({reference:s.rects.reference,element:s.rects.popper,strategy:"absolute",placement:s.placement})}var Bn={name:"popperOffsets",enabled:!0,phase:"read",fn:Yr,data:{}},Xr={top:"auto",right:"auto",bottom:"auto",left:"auto"};function qr(c){var s=c.x,b=c.y,T=window,P=T.devicePixelRatio||1;return{x:Yt(Yt(s*P)/P)||0,y:Yt(Yt(b*P)/P)||0}}function Hn(c){var s,b=c.popper,T=c.popperRect,P=c.placement,F=c.offsets,U=c.position,H=c.gpuAcceleration,G=c.adaptive,oe=c.roundOffsets,z=oe===!0?qr(F):typeof oe=="function"?oe(F):F,_e=z.x,Fe=_e===void 0?0:_e,De=z.y,xe=De===void 0?0:De,Me=F.hasOwnProperty("x"),Se=F.hasOwnProperty("y"),Be=Q,Re=V,He=window;if(G){var fe=J(b),Pe="clientHeight",Ce="clientWidth";fe===r(b)&&(fe=y(b),O(fe).position!=="static"&&(Pe="scrollHeight",Ce="scrollWidth")),fe=fe,P===V&&(Re=de,xe-=fe[Pe]-T.height,xe*=H?1:-1),P===Q&&(Be=X,Fe-=fe[Ce]-T.width,Fe*=H?1:-1)}var Ne=Object.assign({position:U},G&&Xr);if(H){var Le;return Object.assign({},Ne,(Le={},Le[Re]=Se?"0":"",Le[Be]=Me?"0":"",Le.transform=(He.devicePixelRatio||1)<2?"translate("+Fe+"px, "+xe+"px)":"translate3d("+Fe+"px, "+xe+"px, 0)",Le))}return Object.assign({},Ne,(s={},s[Re]=Se?xe+"px":"",s[Be]=Me?Fe+"px":"",s.transform="",s))}function g(c){var s=c.state,b=c.options,T=b.gpuAcceleration,P=T===void 0?!0:T,F=b.adaptive,U=F===void 0?!0:F,H=b.roundOffsets,G=H===void 0?!0:H,oe=O(s.elements.popper).transitionProperty||"";U&&["transform","top","right","bottom","left"].some(function(_e){return oe.indexOf(_e)>=0})&&console.warn(["Popper: Detected CSS transitions on at least one of the following",'CSS properties: "transform", "top", "right", "bottom", "left".',`

`,'Disable the "computeStyles" modifier\'s `adaptive` option to allow',"for smooth transitions, or remove these properties from the CSS","transition declaration on the popper element if only transitioning","opacity or background-color for example.",`

`,"We recommend using the popper element as a wrapper around an inner","element that can have any CSS property transitioned for animations."].join(" "));var z={placement:ot(s.placement),popper:s.elements.popper,popperRect:s.rects.popper,gpuAcceleration:P};s.modifiersData.popperOffsets!=null&&(s.styles.popper=Object.assign({},s.styles.popper,Hn(Object.assign({},z,{offsets:s.modifiersData.popperOffsets,position:s.options.strategy,adaptive:U,roundOffsets:G})))),s.modifiersData.arrow!=null&&(s.styles.arrow=Object.assign({},s.styles.arrow,Hn(Object.assign({},z,{offsets:s.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:G})))),s.attributes.popper=Object.assign({},s.attributes.popper,{"data-popper-placement":s.placement})}var w={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:g,data:{}};function D(c){var s=c.state;Object.keys(s.elements).forEach(function(b){var T=s.styles[b]||{},P=s.attributes[b]||{},F=s.elements[b];!o(F)||!u(F)||(Object.assign(F.style,T),Object.keys(P).forEach(function(U){var H=P[U];H===!1?F.removeAttribute(U):F.setAttribute(U,H===!0?"":H)}))})}function L(c){var s=c.state,b={popper:{position:s.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(s.elements.popper.style,b.popper),s.styles=b,s.elements.arrow&&Object.assign(s.elements.arrow.style,b.arrow),function(){Object.keys(s.elements).forEach(function(T){var P=s.elements[T],F=s.attributes[T]||{},U=Object.keys(s.styles.hasOwnProperty(T)?s.styles[T]:b[T]),H=U.reduce(function(G,oe){return G[oe]="",G},{});!o(P)||!u(P)||(Object.assign(P.style,H),Object.keys(F).forEach(function(G){P.removeAttribute(G)}))})}}var q={name:"applyStyles",enabled:!0,phase:"write",fn:D,effect:L,requires:["computeStyles"]};function W(c,s,b){var T=ot(c),P=[Q,V].indexOf(T)>=0?-1:1,F=typeof b=="function"?b(Object.assign({},s,{placement:c})):b,U=F[0],H=F[1];return U=U||0,H=(H||0)*P,[Q,X].indexOf(T)>=0?{x:H,y:U}:{x:U,y:H}}function B(c){var s=c.state,b=c.options,T=c.name,P=b.offset,F=P===void 0?[0,0]:P,U=ze.reduce(function(z,_e){return z[_e]=W(_e,s.rects,F),z},{}),H=U[s.placement],G=H.x,oe=H.y;s.modifiersData.popperOffsets!=null&&(s.modifiersData.popperOffsets.x+=G,s.modifiersData.popperOffsets.y+=oe),s.modifiersData[T]=U}var be={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:B},le={left:"right",right:"left",bottom:"top",top:"bottom"};function pe(c){return c.replace(/left|right|bottom|top/g,function(s){return le[s]})}var ye={start:"end",end:"start"};function Te(c){return c.replace(/start|end/g,function(s){return ye[s]})}function je(c,s){s===void 0&&(s={});var b=s,T=b.placement,P=b.boundary,F=b.rootBoundary,U=b.padding,H=b.flipVariations,G=b.allowedAutoPlacements,oe=G===void 0?ze:G,z=fn(T),_e=z?H?Z:Z.filter(function(xe){return fn(xe)===z}):l,Fe=_e.filter(function(xe){return oe.indexOf(xe)>=0});Fe.length===0&&(Fe=_e,console.error(["Popper: The `allowedAutoPlacements` option did not allow any","placements. Ensure the `placement` option matches the variation","of the allowed placements.",'For example, "auto" cannot be used to allow "bottom-start".','Use "auto-start" instead.'].join(" ")));var De=Fe.reduce(function(xe,Me){return xe[Me]=qt(c,{placement:Me,boundary:P,rootBoundary:F,padding:U})[ot(Me)],xe},{});return Object.keys(De).sort(function(xe,Me){return De[xe]-De[Me]})}function Ae(c){if(ot(c)===me)return[];var s=pe(c);return[Te(c),s,Te(s)]}function Ie(c){var s=c.state,b=c.options,T=c.name;if(!s.modifiersData[T]._skip){for(var P=b.mainAxis,F=P===void 0?!0:P,U=b.altAxis,H=U===void 0?!0:U,G=b.fallbackPlacements,oe=b.padding,z=b.boundary,_e=b.rootBoundary,Fe=b.altBoundary,De=b.flipVariations,xe=De===void 0?!0:De,Me=b.allowedAutoPlacements,Se=s.options.placement,Be=ot(Se),Re=Be===Se,He=G||(Re||!xe?[pe(Se)]:Ae(Se)),fe=[Se].concat(He).reduce(function(te,ge){return te.concat(ot(ge)===me?je(s,{placement:ge,boundary:z,rootBoundary:_e,padding:oe,flipVariations:xe,allowedAutoPlacements:Me}):ge)},[]),Pe=s.rects.reference,Ce=s.rects.popper,Ne=new Map,Le=!0,Ye=fe[0],$e=0;$e<fe.length;$e++){var Ue=fe[$e],wt=ot(Ue),et=fn(Ue)===h,Ft=[V,de].indexOf(wt)>=0,dn=Ft?"width":"height",Qt=qt(s,{placement:Ue,boundary:z,rootBoundary:_e,altBoundary:Fe,padding:oe}),Nt=Ft?et?X:Q:et?de:V;Pe[dn]>Ce[dn]&&(Nt=pe(Nt));var $n=pe(Nt),Zt=[];if(F&&Zt.push(Qt[wt]<=0),H&&Zt.push(Qt[Nt]<=0,Qt[$n]<=0),Zt.every(function(te){return te})){Ye=Ue,Le=!1;break}Ne.set(Ue,Zt)}if(Le)for(var Sn=xe?3:1,Wn=function(ge){var we=fe.find(function(Ke){var Je=Ne.get(Ke);if(Je)return Je.slice(0,ge).every(function(_t){return _t})});if(we)return Ye=we,"break"},_=Sn;_>0;_--){var K=Wn(_);if(K==="break")break}s.placement!==Ye&&(s.modifiersData[T]._skip=!0,s.placement=Ye,s.reset=!0)}}var re={name:"flip",enabled:!0,phase:"main",fn:Ie,requiresIfExists:["offset"],data:{_skip:!1}};function he(c){return c==="x"?"y":"x"}function ve(c,s,b){return gt(c,ln(s,b))}function ee(c){var s=c.state,b=c.options,T=c.name,P=b.mainAxis,F=P===void 0?!0:P,U=b.altAxis,H=U===void 0?!1:U,G=b.boundary,oe=b.rootBoundary,z=b.altBoundary,_e=b.padding,Fe=b.tether,De=Fe===void 0?!0:Fe,xe=b.tetherOffset,Me=xe===void 0?0:xe,Se=qt(s,{boundary:G,rootBoundary:oe,padding:_e,altBoundary:z}),Be=ot(s.placement),Re=fn(s.placement),He=!Re,fe=dt(Be),Pe=he(fe),Ce=s.modifiersData.popperOffsets,Ne=s.rects.reference,Le=s.rects.popper,Ye=typeof Me=="function"?Me(Object.assign({},s.rects,{placement:s.placement})):Me,$e={x:0,y:0};if(Ce){if(F||H){var Ue=fe==="y"?V:Q,wt=fe==="y"?de:X,et=fe==="y"?"height":"width",Ft=Ce[fe],dn=Ce[fe]+Se[Ue],Qt=Ce[fe]-Se[wt],Nt=De?-Le[et]/2:0,$n=Re===h?Ne[et]:Le[et],Zt=Re===h?-Le[et]:-Ne[et],Sn=s.elements.arrow,Wn=De&&Sn?C(Sn):{width:0,height:0},_=s.modifiersData["arrow#persistent"]?s.modifiersData["arrow#persistent"].padding:fr(),K=_[Ue],te=_[wt],ge=ve(0,Ne[et],Wn[et]),we=He?Ne[et]/2-Nt-ge-K-Ye:$n-ge-K-Ye,Ke=He?-Ne[et]/2+Nt+ge+te+Ye:Zt+ge+te+Ye,Je=s.elements.arrow&&J(s.elements.arrow),_t=Je?fe==="y"?Je.clientTop||0:Je.clientLeft||0:0,Un=s.modifiersData.offset?s.modifiersData.offset[s.placement][fe]:0,Ct=Ce[fe]+we-Un-_t,An=Ce[fe]+Ke-Un;if(F){var pn=ve(De?ln(dn,Ct):dn,Ft,De?gt(Qt,An):Qt);Ce[fe]=pn,$e[fe]=pn-Ft}if(H){var en=fe==="x"?V:Q,Gr=fe==="x"?de:X,tn=Ce[Pe],hn=tn+Se[en],_i=tn-Se[Gr],Ci=ve(De?ln(hn,Ct):hn,tn,De?gt(_i,An):_i);Ce[Pe]=Ci,$e[Pe]=Ci-tn}}s.modifiersData[T]=$e}}var ie={name:"preventOverflow",enabled:!0,phase:"main",fn:ee,requiresIfExists:["offset"]},x=function(s,b){return s=typeof s=="function"?s(Object.assign({},b.rects,{placement:b.placement})):s,cr(typeof s!="number"?s:ur(s,l))};function Ge(c){var s,b=c.state,T=c.name,P=c.options,F=b.elements.arrow,U=b.modifiersData.popperOffsets,H=ot(b.placement),G=dt(H),oe=[Q,X].indexOf(H)>=0,z=oe?"height":"width";if(!(!F||!U)){var _e=x(P.padding,b),Fe=C(F),De=G==="y"?V:Q,xe=G==="y"?de:X,Me=b.rects.reference[z]+b.rects.reference[G]-U[G]-b.rects.popper[z],Se=U[G]-b.rects.reference[G],Be=J(F),Re=Be?G==="y"?Be.clientHeight||0:Be.clientWidth||0:0,He=Me/2-Se/2,fe=_e[De],Pe=Re-Fe[z]-_e[xe],Ce=Re/2-Fe[z]/2+He,Ne=ve(fe,Ce,Pe),Le=G;b.modifiersData[T]=(s={},s[Le]=Ne,s.centerOffset=Ne-Ce,s)}}function ce(c){var s=c.state,b=c.options,T=b.element,P=T===void 0?"[data-popper-arrow]":T;if(P!=null&&!(typeof P=="string"&&(P=s.elements.popper.querySelector(P),!P))){if(o(P)||console.error(['Popper: "arrow" element must be an HTMLElement (not an SVGElement).',"To use an SVG arrow, wrap it in an HTMLElement that will be used as","the arrow."].join(" ")),!kn(s.elements.popper,P)){console.error(['Popper: "arrow" modifier\'s `element` must be a child of the popper',"element."].join(" "));return}s.elements.arrow=P}}var Lt={name:"arrow",enabled:!0,phase:"main",fn:Ge,effect:ce,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function bt(c,s,b){return b===void 0&&(b={x:0,y:0}),{top:c.top-s.height-b.y,right:c.right-s.width+b.x,bottom:c.bottom-s.height+b.y,left:c.left-s.width-b.x}}function Gt(c){return[V,X,de,Q].some(function(s){return c[s]>=0})}function Kt(c){var s=c.state,b=c.name,T=s.rects.reference,P=s.rects.popper,F=s.modifiersData.preventOverflow,U=qt(s,{elementContext:"reference"}),H=qt(s,{altBoundary:!0}),G=bt(U,T),oe=bt(H,P,F),z=Gt(G),_e=Gt(oe);s.modifiersData[b]={referenceClippingOffsets:G,popperEscapeOffsets:oe,isReferenceHidden:z,hasPopperEscaped:_e},s.attributes.popper=Object.assign({},s.attributes.popper,{"data-popper-reference-hidden":z,"data-popper-escaped":_e})}var Jt={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:Kt},rt=[jn,Bn,w,q],lt=En({defaultModifiers:rt}),yt=[jn,Bn,w,q,be,re,ie,Lt,Jt],un=En({defaultModifiers:yt});e.applyStyles=q,e.arrow=Lt,e.computeStyles=w,e.createPopper=un,e.createPopperLite=lt,e.defaultModifiers=yt,e.detectOverflow=qt,e.eventListeners=jn,e.flip=re,e.hide=Jt,e.offset=be,e.popperGenerator=En,e.popperOffsets=Bn,e.preventOverflow=ie}),$o=Bo(e=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var t=Cs(),r='<svg width="16" height="6" xmlns="http://www.w3.org/2000/svg"><path d="M0 6s1.796-.013 4.67-3.615C5.851.9 6.93.006 8 0c1.07-.006 2.148.887 3.343 2.385C14.233 6.005 16 6 16 6H0z"></svg>',n="tippy-box",i="tippy-content",o="tippy-backdrop",a="tippy-arrow",d="tippy-svg-arrow",f={passive:!0,capture:!0};function u(g,w){return{}.hasOwnProperty.call(g,w)}function y(g,w,D){if(Array.isArray(g)){var L=g[w];return L??(Array.isArray(D)?D[w]:D)}return g}function m(g,w){var D={}.toString.call(g);return D.indexOf("[object")===0&&D.indexOf(w+"]")>-1}function O(g,w){return typeof g=="function"?g.apply(void 0,w):g}function E(g,w){if(w===0)return g;var D;return function(L){clearTimeout(D),D=setTimeout(function(){g(L)},w)}}function S(g,w){var D=Object.assign({},g);return w.forEach(function(L){delete D[L]}),D}function C(g){return g.split(/\s+/).filter(Boolean)}function I(g){return[].concat(g)}function $(g,w){g.indexOf(w)===-1&&g.push(w)}function A(g){return g.filter(function(w,D){return g.indexOf(w)===D})}function k(g){return g.split("-")[0]}function Y(g){return[].slice.call(g)}function ne(g){return Object.keys(g).reduce(function(w,D){return g[D]!==void 0&&(w[D]=g[D]),w},{})}function J(){return document.createElement("div")}function V(g){return["Element","Fragment"].some(function(w){return m(g,w)})}function de(g){return m(g,"NodeList")}function X(g){return m(g,"MouseEvent")}function Q(g){return!!(g&&g._tippy&&g._tippy.reference===g)}function me(g){return V(g)?[g]:de(g)?Y(g):Array.isArray(g)?g:Y(document.querySelectorAll(g))}function l(g,w){g.forEach(function(D){D&&(D.style.transitionDuration=w+"ms")})}function h(g,w){g.forEach(function(D){D&&D.setAttribute("data-state",w)})}function v(g){var w,D=I(g),L=D[0];return!(L==null||(w=L.ownerDocument)==null)&&w.body?L.ownerDocument:document}function p(g,w){var D=w.clientX,L=w.clientY;return g.every(function(q){var W=q.popperRect,B=q.popperState,be=q.props,le=be.interactiveBorder,pe=k(B.placement),ye=B.modifiersData.offset;if(!ye)return!0;var Te=pe==="bottom"?ye.top.y:0,je=pe==="top"?ye.bottom.y:0,Ae=pe==="right"?ye.left.x:0,Ie=pe==="left"?ye.right.x:0,re=W.top-L+Te>le,he=L-W.bottom-je>le,ve=W.left-D+Ae>le,ee=D-W.right-Ie>le;return re||he||ve||ee})}function j(g,w,D){var L=w+"EventListener";["transitionend","webkitTransitionEnd"].forEach(function(q){g[L](q,D)})}var M={isTouch:!1},R=0;function Z(){M.isTouch||(M.isTouch=!0,window.performance&&document.addEventListener("mousemove",ze))}function ze(){var g=performance.now();g-R<20&&(M.isTouch=!1,document.removeEventListener("mousemove",ze)),R=g}function Rt(){var g=document.activeElement;if(Q(g)){var w=g._tippy;g.blur&&!w.state.isVisible&&g.blur()}}function Ut(){document.addEventListener("touchstart",Z,f),window.addEventListener("blur",Rt)}var Fr=typeof window<"u"&&typeof document<"u",Nr=Fr?navigator.userAgent:"",kr=/MSIE |Trident\//.test(Nr);function Vt(g){var w=g==="destroy"?"n already-":" ";return[g+"() was called on a"+w+"destroyed instance. This is a no-op but","indicates a potential memory leak."].join(" ")}function nr(g){var w=/[ \t]{2,}/g,D=/^[ \t]*/gm;return g.replace(w," ").replace(D,"").trim()}function jr(g){return nr(`
  %ctippy.js

  %c`+nr(g)+`

  %c\u{1F477}\u200D This is a development-only message. It will be removed in production.
  `)}function rr(g){return[jr(g),"color: #00C584; font-size: 1.3em; font-weight: bold;","line-height: 1.5","color: #a6a095;"]}var It;Br();function Br(){It=new Set}function mt(g,w){if(g&&!It.has(w)){var D;It.add(w),(D=console).warn.apply(D,rr(w))}}function zt(g,w){if(g&&!It.has(w)){var D;It.add(w),(D=console).error.apply(D,rr(w))}}function At(g){var w=!g,D=Object.prototype.toString.call(g)==="[object Object]"&&!g.addEventListener;zt(w,["tippy() was passed","`"+String(g)+"`","as its targets (first) argument. Valid types are: String, Element,","Element[], or NodeList."].join(" ")),zt(D,["tippy() was passed a plain object which is not supported as an argument","for virtual positioning. Use props.getReferenceClientRect instead."].join(" "))}var Dt={animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},Hr={allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999},Ze=Object.assign({appendTo:function(){return document.body},aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},Dt,{},Hr),$r=Object.keys(Ze),Wr=function(w){gt(w,[]);var D=Object.keys(w);D.forEach(function(L){Ze[L]=w[L]})};function ot(g){var w=g.plugins||[],D=w.reduce(function(L,q){var W=q.name,B=q.defaultValue;return W&&(L[W]=g[W]!==void 0?g[W]:B),L},{});return Object.assign({},g,{},D)}function Ur(g,w){var D=w?Object.keys(ot(Object.assign({},Ze,{plugins:w}))):$r,L=D.reduce(function(q,W){var B=(g.getAttribute("data-tippy-"+W)||"").trim();if(!B)return q;if(W==="content")q[W]=B;else try{q[W]=JSON.parse(B)}catch{q[W]=B}return q},{});return L}function ir(g,w){var D=Object.assign({},w,{content:O(w.content,[g])},w.ignoreAttributes?{}:Ur(g,w.plugins));return D.aria=Object.assign({},Ze.aria,{},D.aria),D.aria={expanded:D.aria.expanded==="auto"?w.interactive:D.aria.expanded,content:D.aria.content==="auto"?w.interactive?null:"describedby":D.aria.content},D}function gt(g,w){g===void 0&&(g={}),w===void 0&&(w=[]);var D=Object.keys(g);D.forEach(function(L){var q=S(Ze,Object.keys(Dt)),W=!u(q,L);W&&(W=w.filter(function(B){return B.name===L}).length===0),mt(W,["`"+L+"`","is not a valid prop. You may have spelled it incorrectly, or if it's","a plugin, forgot to pass it in an array as props.plugins.",`

`,`All props: https://atomiks.github.io/tippyjs/v6/all-props/
`,"Plugins: https://atomiks.github.io/tippyjs/v6/plugins/"].join(" "))})}var ln=function(){return"innerHTML"};function Yt(g,w){g[ln()]=w}function or(g){var w=J();return g===!0?w.className=a:(w.className=d,V(g)?w.appendChild(g):Yt(w,g)),w}function kn(g,w){V(w.content)?(Yt(g,""),g.appendChild(w.content)):typeof w.content!="function"&&(w.allowHTML?Yt(g,w.content):g.textContent=w.content)}function Xt(g){var w=g.firstElementChild,D=Y(w.children);return{box:w,content:D.find(function(L){return L.classList.contains(i)}),arrow:D.find(function(L){return L.classList.contains(a)||L.classList.contains(d)}),backdrop:D.find(function(L){return L.classList.contains(o)})}}function ar(g){var w=J(),D=J();D.className=n,D.setAttribute("data-state","hidden"),D.setAttribute("tabindex","-1");var L=J();L.className=i,L.setAttribute("data-state","hidden"),kn(L,g.props),w.appendChild(D),D.appendChild(L),q(g.props,g.props);function q(W,B){var be=Xt(w),le=be.box,pe=be.content,ye=be.arrow;B.theme?le.setAttribute("data-theme",B.theme):le.removeAttribute("data-theme"),typeof B.animation=="string"?le.setAttribute("data-animation",B.animation):le.removeAttribute("data-animation"),B.inertia?le.setAttribute("data-inertia",""):le.removeAttribute("data-inertia"),le.style.maxWidth=typeof B.maxWidth=="number"?B.maxWidth+"px":B.maxWidth,B.role?le.setAttribute("role",B.role):le.removeAttribute("role"),(W.content!==B.content||W.allowHTML!==B.allowHTML)&&kn(pe,g.props),B.arrow?ye?W.arrow!==B.arrow&&(le.removeChild(ye),le.appendChild(or(B.arrow))):le.appendChild(or(B.arrow)):ye&&le.removeChild(ye)}return{popper:w,onUpdate:q}}ar.$$tippy=!0;var sr=1,yn=[],wn=[];function fn(g,w){var D=ir(g,Object.assign({},Ze,{},ot(ne(w)))),L,q,W,B=!1,be=!1,le=!1,pe=!1,ye,Te,je,Ae=[],Ie=E(Re,D.interactiveDebounce),re,he=sr++,ve=null,ee=A(D.plugins),ie={isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},x={id:he,reference:g,popper:J(),popperInstance:ve,props:D,state:ie,plugins:ee,clearDelayTimeouts:Ft,setProps:dn,setContent:Qt,show:Nt,hide:$n,hideWithInteractivity:Zt,enable:wt,disable:et,unmount:Sn,destroy:Wn};if(!D.render)return zt(!0,"render() function has not been supplied."),x;var Ge=D.render(x),ce=Ge.popper,Lt=Ge.onUpdate;ce.setAttribute("data-tippy-root",""),ce.id="tippy-"+x.id,x.popper=ce,g._tippy=x,ce._tippy=x;var bt=ee.map(function(_){return _.fn(x)}),Gt=g.hasAttribute("aria-expanded");return Me(),P(),s(),b("onCreate",[x]),D.showOnCreate&&$e(),ce.addEventListener("mouseenter",function(){x.props.interactive&&x.state.isVisible&&x.clearDelayTimeouts()}),ce.addEventListener("mouseleave",function(_){x.props.interactive&&x.props.trigger.indexOf("mouseenter")>=0&&(yt().addEventListener("mousemove",Ie),Ie(_))}),x;function Kt(){var _=x.props.touch;return Array.isArray(_)?_:[_,0]}function Jt(){return Kt()[0]==="hold"}function rt(){var _;return!!((_=x.props.render)!=null&&_.$$tippy)}function lt(){return re||g}function yt(){var _=lt().parentNode;return _?v(_):document}function un(){return Xt(ce)}function c(_){return x.state.isMounted&&!x.state.isVisible||M.isTouch||ye&&ye.type==="focus"?0:y(x.props.delay,_?0:1,Ze.delay)}function s(){ce.style.pointerEvents=x.props.interactive&&x.state.isVisible?"":"none",ce.style.zIndex=""+x.props.zIndex}function b(_,K,te){if(te===void 0&&(te=!0),bt.forEach(function(we){we[_]&&we[_].apply(void 0,K)}),te){var ge;(ge=x.props)[_].apply(ge,K)}}function T(){var _=x.props.aria;if(_.content){var K="aria-"+_.content,te=ce.id,ge=I(x.props.triggerTarget||g);ge.forEach(function(we){var Ke=we.getAttribute(K);if(x.state.isVisible)we.setAttribute(K,Ke?Ke+" "+te:te);else{var Je=Ke&&Ke.replace(te,"").trim();Je?we.setAttribute(K,Je):we.removeAttribute(K)}})}}function P(){if(!(Gt||!x.props.aria.expanded)){var _=I(x.props.triggerTarget||g);_.forEach(function(K){x.props.interactive?K.setAttribute("aria-expanded",x.state.isVisible&&K===lt()?"true":"false"):K.removeAttribute("aria-expanded")})}}function F(){yt().removeEventListener("mousemove",Ie),yn=yn.filter(function(_){return _!==Ie})}function U(_){if(!(M.isTouch&&(le||_.type==="mousedown"))&&!(x.props.interactive&&ce.contains(_.target))){if(lt().contains(_.target)){if(M.isTouch||x.state.isVisible&&x.props.trigger.indexOf("click")>=0)return}else b("onClickOutside",[x,_]);x.props.hideOnClick===!0&&(x.clearDelayTimeouts(),x.hide(),be=!0,setTimeout(function(){be=!1}),x.state.isMounted||z())}}function H(){le=!0}function G(){le=!1}function oe(){var _=yt();_.addEventListener("mousedown",U,!0),_.addEventListener("touchend",U,f),_.addEventListener("touchstart",G,f),_.addEventListener("touchmove",H,f)}function z(){var _=yt();_.removeEventListener("mousedown",U,!0),_.removeEventListener("touchend",U,f),_.removeEventListener("touchstart",G,f),_.removeEventListener("touchmove",H,f)}function _e(_,K){De(_,function(){!x.state.isVisible&&ce.parentNode&&ce.parentNode.contains(ce)&&K()})}function Fe(_,K){De(_,K)}function De(_,K){var te=un().box;function ge(we){we.target===te&&(j(te,"remove",ge),K())}if(_===0)return K();j(te,"remove",Te),j(te,"add",ge),Te=ge}function xe(_,K,te){te===void 0&&(te=!1);var ge=I(x.props.triggerTarget||g);ge.forEach(function(we){we.addEventListener(_,K,te),Ae.push({node:we,eventType:_,handler:K,options:te})})}function Me(){Jt()&&(xe("touchstart",Be,{passive:!0}),xe("touchend",He,{passive:!0})),C(x.props.trigger).forEach(function(_){if(_!=="manual")switch(xe(_,Be),_){case"mouseenter":xe("mouseleave",He);break;case"focus":xe(kr?"focusout":"blur",fe);break;case"focusin":xe("focusout",fe);break}})}function Se(){Ae.forEach(function(_){var K=_.node,te=_.eventType,ge=_.handler,we=_.options;K.removeEventListener(te,ge,we)}),Ae=[]}function Be(_){var K,te=!1;if(!(!x.state.isEnabled||Pe(_)||be)){var ge=((K=ye)==null?void 0:K.type)==="focus";ye=_,re=_.currentTarget,P(),!x.state.isVisible&&X(_)&&yn.forEach(function(we){return we(_)}),_.type==="click"&&(x.props.trigger.indexOf("mouseenter")<0||B)&&x.props.hideOnClick!==!1&&x.state.isVisible?te=!0:$e(_),_.type==="click"&&(B=!te),te&&!ge&&Ue(_)}}function Re(_){var K=_.target,te=lt().contains(K)||ce.contains(K);if(!(_.type==="mousemove"&&te)){var ge=Ye().concat(ce).map(function(we){var Ke,Je=we._tippy,_t=(Ke=Je.popperInstance)==null?void 0:Ke.state;return _t?{popperRect:we.getBoundingClientRect(),popperState:_t,props:D}:null}).filter(Boolean);p(ge,_)&&(F(),Ue(_))}}function He(_){var K=Pe(_)||x.props.trigger.indexOf("click")>=0&&B;if(!K){if(x.props.interactive){x.hideWithInteractivity(_);return}Ue(_)}}function fe(_){x.props.trigger.indexOf("focusin")<0&&_.target!==lt()||x.props.interactive&&_.relatedTarget&&ce.contains(_.relatedTarget)||Ue(_)}function Pe(_){return M.isTouch?Jt()!==_.type.indexOf("touch")>=0:!1}function Ce(){Ne();var _=x.props,K=_.popperOptions,te=_.placement,ge=_.offset,we=_.getReferenceClientRect,Ke=_.moveTransition,Je=rt()?Xt(ce).arrow:null,_t=we?{getBoundingClientRect:we,contextElement:we.contextElement||lt()}:g,Un={name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(pn){var en=pn.state;if(rt()){var Gr=un(),tn=Gr.box;["placement","reference-hidden","escaped"].forEach(function(hn){hn==="placement"?tn.setAttribute("data-placement",en.placement):en.attributes.popper["data-popper-"+hn]?tn.setAttribute("data-"+hn,""):tn.removeAttribute("data-"+hn)}),en.attributes.popper={}}}},Ct=[{name:"offset",options:{offset:ge}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!Ke}},Un];rt()&&Je&&Ct.push({name:"arrow",options:{element:Je,padding:3}}),Ct.push.apply(Ct,K?.modifiers||[]),x.popperInstance=t.createPopper(_t,ce,Object.assign({},K,{placement:te,onFirstUpdate:je,modifiers:Ct}))}function Ne(){x.popperInstance&&(x.popperInstance.destroy(),x.popperInstance=null)}function Le(){var _=x.props.appendTo,K,te=lt();x.props.interactive&&_===Ze.appendTo||_==="parent"?K=te.parentNode:K=O(_,[te]),K.contains(ce)||K.appendChild(ce),Ce(),mt(x.props.interactive&&_===Ze.appendTo&&te.nextElementSibling!==ce,["Interactive tippy element may not be accessible via keyboard","navigation because it is not directly after the reference element","in the DOM source order.",`

`,"Using a wrapper <div> or <span> tag around the reference element","solves this by creating a new parentNode context.",`

`,"Specifying `appendTo: document.body` silences this warning, but it","assumes you are using a focus management solution to handle","keyboard navigation.",`

`,"See: https://atomiks.github.io/tippyjs/v6/accessibility/#interactivity"].join(" "))}function Ye(){return Y(ce.querySelectorAll("[data-tippy-root]"))}function $e(_){x.clearDelayTimeouts(),_&&b("onTrigger",[x,_]),oe();var K=c(!0),te=Kt(),ge=te[0],we=te[1];M.isTouch&&ge==="hold"&&we&&(K=we),K?L=setTimeout(function(){x.show()},K):x.show()}function Ue(_){if(x.clearDelayTimeouts(),b("onUntrigger",[x,_]),!x.state.isVisible){z();return}if(!(x.props.trigger.indexOf("mouseenter")>=0&&x.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(_.type)>=0&&B)){var K=c(!1);K?q=setTimeout(function(){x.state.isVisible&&x.hide()},K):W=requestAnimationFrame(function(){x.hide()})}}function wt(){x.state.isEnabled=!0}function et(){x.hide(),x.state.isEnabled=!1}function Ft(){clearTimeout(L),clearTimeout(q),cancelAnimationFrame(W)}function dn(_){if(mt(x.state.isDestroyed,Vt("setProps")),!x.state.isDestroyed){b("onBeforeUpdate",[x,_]),Se();var K=x.props,te=ir(g,Object.assign({},x.props,{},_,{ignoreAttributes:!0}));x.props=te,Me(),K.interactiveDebounce!==te.interactiveDebounce&&(F(),Ie=E(Re,te.interactiveDebounce)),K.triggerTarget&&!te.triggerTarget?I(K.triggerTarget).forEach(function(ge){ge.removeAttribute("aria-expanded")}):te.triggerTarget&&g.removeAttribute("aria-expanded"),P(),s(),Lt&&Lt(K,te),x.popperInstance&&(Ce(),Ye().forEach(function(ge){requestAnimationFrame(ge._tippy.popperInstance.forceUpdate)})),b("onAfterUpdate",[x,_])}}function Qt(_){x.setProps({content:_})}function Nt(){mt(x.state.isDestroyed,Vt("show"));var _=x.state.isVisible,K=x.state.isDestroyed,te=!x.state.isEnabled,ge=M.isTouch&&!x.props.touch,we=y(x.props.duration,0,Ze.duration);if(!(_||K||te||ge)&&!lt().hasAttribute("disabled")&&(b("onShow",[x],!1),x.props.onShow(x)!==!1)){if(x.state.isVisible=!0,rt()&&(ce.style.visibility="visible"),s(),oe(),x.state.isMounted||(ce.style.transition="none"),rt()){var Ke=un(),Je=Ke.box,_t=Ke.content;l([Je,_t],0)}je=function(){var Ct;if(!(!x.state.isVisible||pe)){if(pe=!0,ce.offsetHeight,ce.style.transition=x.props.moveTransition,rt()&&x.props.animation){var An=un(),pn=An.box,en=An.content;l([pn,en],we),h([pn,en],"visible")}T(),P(),$(wn,x),(Ct=x.popperInstance)==null||Ct.forceUpdate(),x.state.isMounted=!0,b("onMount",[x]),x.props.animation&&rt()&&Fe(we,function(){x.state.isShown=!0,b("onShown",[x])})}},Le()}}function $n(){mt(x.state.isDestroyed,Vt("hide"));var _=!x.state.isVisible,K=x.state.isDestroyed,te=!x.state.isEnabled,ge=y(x.props.duration,1,Ze.duration);if(!(_||K||te)&&(b("onHide",[x],!1),x.props.onHide(x)!==!1)){if(x.state.isVisible=!1,x.state.isShown=!1,pe=!1,B=!1,rt()&&(ce.style.visibility="hidden"),F(),z(),s(),rt()){var we=un(),Ke=we.box,Je=we.content;x.props.animation&&(l([Ke,Je],ge),h([Ke,Je],"hidden"))}T(),P(),x.props.animation?rt()&&_e(ge,x.unmount):x.unmount()}}function Zt(_){mt(x.state.isDestroyed,Vt("hideWithInteractivity")),yt().addEventListener("mousemove",Ie),$(yn,Ie),Ie(_)}function Sn(){mt(x.state.isDestroyed,Vt("unmount")),x.state.isVisible&&x.hide(),x.state.isMounted&&(Ne(),Ye().forEach(function(_){_._tippy.unmount()}),ce.parentNode&&ce.parentNode.removeChild(ce),wn=wn.filter(function(_){return _!==x}),x.state.isMounted=!1,b("onHidden",[x]))}function Wn(){mt(x.state.isDestroyed,Vt("destroy")),!x.state.isDestroyed&&(x.clearDelayTimeouts(),x.unmount(),Se(),delete g._tippy,x.state.isDestroyed=!0,b("onDestroy",[x]))}}function dt(g,w){w===void 0&&(w={});var D=Ze.plugins.concat(w.plugins||[]);At(g),gt(w,D),Ut();var L=Object.assign({},w,{plugins:D}),q=me(g),W=V(L.content),B=q.length>1;mt(W&&B,["tippy() was passed an Element as the `content` prop, but more than","one tippy instance was created by this invocation. This means the","content element will only be appended to the last tippy instance.",`

`,"Instead, pass the .innerHTML of the element, or use a function that","returns a cloned version of the element instead.",`

`,`1) content: element.innerHTML
`,"2) content: () => element.cloneNode(true)"].join(" "));var be=q.reduce(function(le,pe){var ye=pe&&fn(pe,L);return ye&&le.push(ye),le},[]);return V(g)?be[0]:be}dt.defaultProps=Ze,dt.setDefaultProps=Wr,dt.currentInput=M;var lr=function(w){var D=w===void 0?{}:w,L=D.exclude,q=D.duration;wn.forEach(function(W){var B=!1;if(L&&(B=Q(L)?W.reference===L:W.popper===L.popper),!B){var be=W.props.duration;W.setProps({duration:q}),W.hide(),W.state.isDestroyed||W.setProps({duration:be})}})},fr=Object.assign({},t.applyStyles,{effect:function(w){var D=w.state,L={popper:{position:D.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(D.elements.popper.style,L.popper),D.styles=L,D.elements.arrow&&Object.assign(D.elements.arrow.style,L.arrow)}}),cr=function(w,D){var L;D===void 0&&(D={}),zt(!Array.isArray(w),["The first argument passed to createSingleton() must be an array of","tippy instances. The passed value was",String(w)].join(" "));var q=w,W=[],B,be=D.overrides,le=[],pe=!1;function ye(){W=q.map(function(ee){return ee.reference})}function Te(ee){q.forEach(function(ie){ee?ie.enable():ie.disable()})}function je(ee){return q.map(function(ie){var x=ie.setProps;return ie.setProps=function(Ge){x(Ge),ie.reference===B&&ee.setProps(Ge)},function(){ie.setProps=x}})}function Ae(ee,ie){var x=W.indexOf(ie);if(ie!==B){B=ie;var Ge=(be||[]).concat("content").reduce(function(ce,Lt){return ce[Lt]=q[x].props[Lt],ce},{});ee.setProps(Object.assign({},Ge,{getReferenceClientRect:typeof Ge.getReferenceClientRect=="function"?Ge.getReferenceClientRect:function(){return ie.getBoundingClientRect()}}))}}Te(!1),ye();var Ie={fn:function(){return{onDestroy:function(){Te(!0)},onHidden:function(){B=null},onClickOutside:function(x){x.props.showOnCreate&&!pe&&(pe=!0,B=null)},onShow:function(x){x.props.showOnCreate&&!pe&&(pe=!0,Ae(x,W[0]))},onTrigger:function(x,Ge){Ae(x,Ge.currentTarget)}}}},re=dt(J(),Object.assign({},S(D,["overrides"]),{plugins:[Ie].concat(D.plugins||[]),triggerTarget:W,popperOptions:Object.assign({},D.popperOptions,{modifiers:[].concat(((L=D.popperOptions)==null?void 0:L.modifiers)||[],[fr])})})),he=re.show;re.show=function(ee){if(he(),!B&&ee==null)return Ae(re,W[0]);if(!(B&&ee==null)){if(typeof ee=="number")return W[ee]&&Ae(re,W[ee]);if(q.includes(ee)){var ie=ee.reference;return Ae(re,ie)}if(W.includes(ee))return Ae(re,ee)}},re.showNext=function(){var ee=W[0];if(!B)return re.show(0);var ie=W.indexOf(B);re.show(W[ie+1]||ee)},re.showPrevious=function(){var ee=W[W.length-1];if(!B)return re.show(ee);var ie=W.indexOf(B),x=W[ie-1]||ee;re.show(x)};var ve=re.setProps;return re.setProps=function(ee){be=ee.overrides||be,ve(ee)},re.setInstances=function(ee){Te(!0),le.forEach(function(ie){return ie()}),q=ee,Te(!1),ye(),je(re),re.setProps({triggerTarget:W})},le=je(re),re},ur={mouseover:"mouseenter",focusin:"focus",click:"click"};function qt(g,w){zt(!(w&&w.target),["You must specity a `target` prop indicating a CSS selector string matching","the target elements that should receive a tippy."].join(" "));var D=[],L=[],q=!1,W=w.target,B=S(w,["target"]),be=Object.assign({},B,{trigger:"manual",touch:!1}),le=Object.assign({},B,{showOnCreate:!0}),pe=dt(g,be),ye=I(pe);function Te(he){if(!(!he.target||q)){var ve=he.target.closest(W);if(ve){var ee=ve.getAttribute("data-tippy-trigger")||w.trigger||Ze.trigger;if(!ve._tippy&&!(he.type==="touchstart"&&typeof le.touch=="boolean")&&!(he.type!=="touchstart"&&ee.indexOf(ur[he.type])<0)){var ie=dt(ve,le);ie&&(L=L.concat(ie))}}}}function je(he,ve,ee,ie){ie===void 0&&(ie=!1),he.addEventListener(ve,ee,ie),D.push({node:he,eventType:ve,handler:ee,options:ie})}function Ae(he){var ve=he.reference;je(ve,"touchstart",Te,f),je(ve,"mouseover",Te),je(ve,"focusin",Te),je(ve,"click",Te)}function Ie(){D.forEach(function(he){var ve=he.node,ee=he.eventType,ie=he.handler,x=he.options;ve.removeEventListener(ee,ie,x)}),D=[]}function re(he){var ve=he.destroy,ee=he.enable,ie=he.disable;he.destroy=function(x){x===void 0&&(x=!0),x&&L.forEach(function(Ge){Ge.destroy()}),L=[],Ie(),ve()},he.enable=function(){ee(),L.forEach(function(x){return x.enable()}),q=!1},he.disable=function(){ie(),L.forEach(function(x){return x.disable()}),q=!0},Ae(he)}return ye.forEach(re),pe}var dr={name:"animateFill",defaultValue:!1,fn:function(w){var D;if(!((D=w.props.render)!=null&&D.$$tippy))return zt(w.props.animateFill,"The `animateFill` plugin requires the default render function."),{};var L=Xt(w.popper),q=L.box,W=L.content,B=w.props.animateFill?Vr():null;return{onCreate:function(){B&&(q.insertBefore(B,q.firstElementChild),q.setAttribute("data-animatefill",""),q.style.overflow="hidden",w.setProps({arrow:!1,animation:"shift-away"}))},onMount:function(){if(B){var le=q.style.transitionDuration,pe=Number(le.replace("ms",""));W.style.transitionDelay=Math.round(pe/10)+"ms",B.style.transitionDuration=le,h([B],"visible")}},onShow:function(){B&&(B.style.transitionDuration="0ms")},onHide:function(){B&&h([B],"hidden")}}}};function Vr(){var g=J();return g.className=o,h([g],"hidden"),g}var xn={clientX:0,clientY:0},cn=[];function En(g){var w=g.clientX,D=g.clientY;xn={clientX:w,clientY:D}}function On(g){g.addEventListener("mousemove",En)}function zr(g){g.removeEventListener("mousemove",En)}var jn={name:"followCursor",defaultValue:!1,fn:function(w){var D=w.reference,L=v(w.props.triggerTarget||D),q=!1,W=!1,B=!0,be=w.props;function le(){return w.props.followCursor==="initial"&&w.state.isVisible}function pe(){L.addEventListener("mousemove",je)}function ye(){L.removeEventListener("mousemove",je)}function Te(){q=!0,w.setProps({getReferenceClientRect:null}),q=!1}function je(re){var he=re.target?D.contains(re.target):!0,ve=w.props.followCursor,ee=re.clientX,ie=re.clientY,x=D.getBoundingClientRect(),Ge=ee-x.left,ce=ie-x.top;(he||!w.props.interactive)&&w.setProps({getReferenceClientRect:function(){var bt=D.getBoundingClientRect(),Gt=ee,Kt=ie;ve==="initial"&&(Gt=bt.left+Ge,Kt=bt.top+ce);var Jt=ve==="horizontal"?bt.top:Kt,rt=ve==="vertical"?bt.right:Gt,lt=ve==="horizontal"?bt.bottom:Kt,yt=ve==="vertical"?bt.left:Gt;return{width:rt-yt,height:lt-Jt,top:Jt,right:rt,bottom:lt,left:yt}}})}function Ae(){w.props.followCursor&&(cn.push({instance:w,doc:L}),On(L))}function Ie(){cn=cn.filter(function(re){return re.instance!==w}),cn.filter(function(re){return re.doc===L}).length===0&&zr(L)}return{onCreate:Ae,onDestroy:Ie,onBeforeUpdate:function(){be=w.props},onAfterUpdate:function(he,ve){var ee=ve.followCursor;q||ee!==void 0&&be.followCursor!==ee&&(Ie(),ee?(Ae(),w.state.isMounted&&!W&&!le()&&pe()):(ye(),Te()))},onMount:function(){w.props.followCursor&&!W&&(B&&(je(xn),B=!1),le()||pe())},onTrigger:function(he,ve){X(ve)&&(xn={clientX:ve.clientX,clientY:ve.clientY}),W=ve.type==="focus"},onHidden:function(){w.props.followCursor&&(Te(),ye(),B=!0)}}}};function Yr(g,w){var D;return{popperOptions:Object.assign({},g.popperOptions,{modifiers:[].concat((((D=g.popperOptions)==null?void 0:D.modifiers)||[]).filter(function(L){var q=L.name;return q!==w.name}),[w])})}}var Bn={name:"inlinePositioning",defaultValue:!1,fn:function(w){var D=w.reference;function L(){return!!w.props.inlinePositioning}var q,W=-1,B=!1,be={name:"tippyInlinePositioning",enabled:!0,phase:"afterWrite",fn:function(je){var Ae=je.state;L()&&(q!==Ae.placement&&w.setProps({getReferenceClientRect:function(){return le(Ae.placement)}}),q=Ae.placement)}};function le(Te){return Xr(k(Te),D.getBoundingClientRect(),Y(D.getClientRects()),W)}function pe(Te){B=!0,w.setProps(Te),B=!1}function ye(){B||pe(Yr(w.props,be))}return{onCreate:ye,onAfterUpdate:ye,onTrigger:function(je,Ae){if(X(Ae)){var Ie=Y(w.reference.getClientRects()),re=Ie.find(function(he){return he.left-2<=Ae.clientX&&he.right+2>=Ae.clientX&&he.top-2<=Ae.clientY&&he.bottom+2>=Ae.clientY});W=Ie.indexOf(re)}},onUntrigger:function(){W=-1}}}};function Xr(g,w,D,L){if(D.length<2||g===null)return w;if(D.length===2&&L>=0&&D[0].left>D[1].right)return D[L]||w;switch(g){case"top":case"bottom":{var q=D[0],W=D[D.length-1],B=g==="top",be=q.top,le=W.bottom,pe=B?q.left:W.left,ye=B?q.right:W.right,Te=ye-pe,je=le-be;return{top:be,bottom:le,left:pe,right:ye,width:Te,height:je}}case"left":case"right":{var Ae=Math.min.apply(Math,D.map(function(ce){return ce.left})),Ie=Math.max.apply(Math,D.map(function(ce){return ce.right})),re=D.filter(function(ce){return g==="left"?ce.left===Ae:ce.right===Ie}),he=re[0].top,ve=re[re.length-1].bottom,ee=Ae,ie=Ie,x=ie-ee,Ge=ve-he;return{top:he,bottom:ve,left:ee,right:ie,width:x,height:Ge}}default:return w}}var qr={name:"sticky",defaultValue:!1,fn:function(w){var D=w.reference,L=w.popper;function q(){return w.popperInstance?w.popperInstance.state.elements.reference:D}function W(pe){return w.props.sticky===!0||w.props.sticky===pe}var B=null,be=null;function le(){var pe=W("reference")?q().getBoundingClientRect():null,ye=W("popper")?L.getBoundingClientRect():null;(pe&&Hn(B,pe)||ye&&Hn(be,ye))&&w.popperInstance&&w.popperInstance.update(),B=pe,be=ye,w.state.isMounted&&requestAnimationFrame(le)}return{onMount:function(){w.props.sticky&&le()}}}};function Hn(g,w){return g&&w?g.top!==w.top||g.right!==w.right||g.bottom!==w.bottom||g.left!==w.left:!0}dt.setDefaultProps({render:ar}),e.animateFill=dr,e.createSingleton=cr,e.default=dt,e.delegate=qt,e.followCursor=jn,e.hideAll=lr,e.inlinePositioning=Bn,e.roundArrow=r,e.sticky=qr}),Si=Ho($o()),Ts=Ho($o()),Ps=e=>{let t={plugins:[]},r=i=>e[e.indexOf(i)+1];if(e.includes("animation")&&(t.animation=r("animation")),e.includes("duration")&&(t.duration=parseInt(r("duration"))),e.includes("delay")){let i=r("delay");t.delay=i.includes("-")?i.split("-").map(o=>parseInt(o)):parseInt(i)}if(e.includes("cursor")){t.plugins.push(Ts.followCursor);let i=r("cursor");["x","initial"].includes(i)?t.followCursor=i==="x"?"horizontal":"initial":t.followCursor=!0}e.includes("on")&&(t.trigger=r("on")),e.includes("arrowless")&&(t.arrow=!1),e.includes("html")&&(t.allowHTML=!0),e.includes("interactive")&&(t.interactive=!0),e.includes("border")&&t.interactive&&(t.interactiveBorder=parseInt(r("border"))),e.includes("debounce")&&t.interactive&&(t.interactiveDebounce=parseInt(r("debounce"))),e.includes("max-width")&&(t.maxWidth=parseInt(r("max-width"))),e.includes("theme")&&(t.theme=r("theme")),e.includes("placement")&&(t.placement=r("placement"));let n={};return e.includes("no-flip")&&(n.modifiers||(n.modifiers=[]),n.modifiers.push({name:"flip",enabled:!1})),t.popperOptions=n,t};function Ai(e){e.magic("tooltip",t=>(r,n={})=>{let i=n.timeout;delete n.timeout;let o=(0,Si.default)(t,{content:r,trigger:"manual",...n});o.show(),setTimeout(()=>{o.hide(),setTimeout(()=>o.destroy(),n.duration||300)},i||2e3)}),e.directive("tooltip",(t,{modifiers:r,expression:n},{evaluateLater:i,effect:o,cleanup:a})=>{let d=r.length>0?Ps(r):{};t.__x_tippy||(t.__x_tippy=(0,Si.default)(t,d)),a(()=>{t.__x_tippy&&(t.__x_tippy.destroy(),delete t.__x_tippy)});let f=()=>t.__x_tippy.enable(),u=()=>t.__x_tippy.disable(),y=m=>{m?(f(),t.__x_tippy.setContent(m)):u()};if(r.includes("raw"))y(n);else{let m=i(n);o(()=>{m(O=>{typeof O=="object"?(t.__x_tippy.setProps(O),f()):y(O)})})}})}Ai.defaultProps=e=>(Si.default.setDefaultProps(e),Ai);var Ms=Ai,Wo=Ms;var Uo=()=>({toggle(e){this.$refs.panel?.toggle(e)},open(e){this.$refs.panel?.open(e)},close(e){this.$refs.panel?.close(e)}});var Vo=()=>({form:null,isProcessing:!1,processingMessage:null,init(){let e=this.$el.closest("form");e?.addEventListener("form-processing-started",t=>{this.isProcessing=!0,this.processingMessage=t.detail.message}),e?.addEventListener("form-processing-finished",()=>{this.isProcessing=!1})}});var zo=({id:e})=>({isOpen:!1,isWindowVisible:!1,livewire:null,init(){this.$nextTick(()=>{this.isWindowVisible=this.isOpen,this.$watch("isOpen",()=>this.isWindowVisible=this.isOpen)})},close(){this.closeQuietly(),this.$dispatch("modal-closed",{id:e})},closeQuietly(){this.isOpen=!1},open(){this.$nextTick(()=>{this.isOpen=!0,this.$dispatch("x-modal-opened")})}});document.addEventListener("livewire:init",()=>{let e=t=>{let r=Alpine.findClosest(t,n=>n.__livewire);if(!r)throw"Could not find Livewire component in DOM tree.";return r.__livewire};Livewire.hook("commit",({component:t,commit:r,respond:n,succeed:i,fail:o})=>{n(()=>{queueMicrotask(()=>{if(!t.effects.html)for(let[f,u]of Object.entries(t.effects.partials??{})){let y=Array.from(t.el.querySelectorAll(`[wire\\:partial="${f}"]`)).filter(C=>e(C)===t);if(!y.length)continue;if(y.length>1)throw`Multiple elements found for partial [${f}].`;let m=y[0],O=m.parentElement?m.parentElement.tagName.toLowerCase():"div",E=document.createElement(O);E.innerHTML=u,E.__livewire=t;let S=E.firstElementChild;S.__livewire=t,window.Alpine.morph(m,S,{updating:(C,I,$,A)=>{if(!a(C)){if(C.__livewire_replace===!0&&(C.innerHTML=I.innerHTML),C.__livewire_replace_self===!0)return C.outerHTML=I.outerHTML,A();if(C.__livewire_ignore===!0||(C.__livewire_ignore_self===!0&&$(),d(C)&&C.getAttribute("wire:id")!==t.id))return A();d(C)&&(I.__livewire=t)}},key:C=>{if(!a(C))return C.hasAttribute("wire:key")?C.getAttribute("wire:key"):C.hasAttribute("wire:id")?C.getAttribute("wire:id"):C.id},lookahead:!1})}})});function a(f){return typeof f.hasAttribute!="function"}function d(f){return f.hasAttribute("wire:id")}})});var Yo=(e,t,r)=>{let n=(y,m)=>{for(let O of y){let E=i(O,m);if(E!==null)return E}},i=(y,m)=>{let O=y.match(/^[\{\[]([^\[\]\{\}]*)[\}\]](.*)/s);if(O===null||O.length!==3)return null;let E=O[1],S=O[2];if(E.includes(",")){let[C,I]=E.split(",",2);if(I==="*"&&m>=C)return S;if(C==="*"&&m<=I)return S;if(m>=C&&m<=I)return S}return E==m?S:null},o=y=>y.toString().charAt(0).toUpperCase()+y.toString().slice(1),a=(y,m)=>{if(m.length===0)return y;let O={};for(let[E,S]of Object.entries(m))O[":"+o(E??"")]=o(S??""),O[":"+E.toUpperCase()]=S.toString().toUpperCase(),O[":"+E]=S;return Object.entries(O).forEach(([E,S])=>{y=y.replaceAll(E,S)}),y},d=y=>y.map(m=>m.replace(/^[\{\[]([^\[\]\{\}]*)[\}\]]/,"")),f=e.split("|"),u=n(f,t);return u!=null?a(u.trim(),r):(f=d(f),a(f.length>1&&t>1?f[1]:f[0],r))};document.addEventListener("alpine:init",()=>{window.Alpine.plugin(oo),window.Alpine.plugin(ao),window.Alpine.plugin(co),window.Alpine.plugin(jo),window.Alpine.plugin(Wo),window.Alpine.data("filamentDropdown",Uo),window.Alpine.data("filamentFormButton",Vo),window.Alpine.data("filamentModal",zo)});window.jsMd5=Xo.md5;window.pluralize=Yo;})();
/*! Bundled license information:

js-md5/src/md5.js:
  (**
   * [js-md5]{@link https://github.com/emn178/js-md5}
   *
   * @namespace md5
   * @version 0.8.3
   * <AUTHOR> Yi-Cyuan [<EMAIL>]
   * @copyright Chen, Yi-Cyuan 2014-2023
   * @license MIT
   *)

sortablejs/modular/sortable.esm.js:
  (**!
   * Sortable 1.15.6
   * <AUTHOR>   <<EMAIL>>
   * <AUTHOR>    <<EMAIL>>
   * @license MIT
   *)
*/
