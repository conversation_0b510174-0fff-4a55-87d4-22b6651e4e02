var I;(function(r){r.Range="range",r.Steps="steps",r.Positions="positions",r.Count="count",r.Values="values"})(I||(I={}));var O;(function(r){r[r.None=-1]="None",r[r.NoValue=0]="NoValue",r[r.LargeValue=1]="LargeValue",r[r.SmallValue=2]="SmallValue"})(O||(O={}));function we(r){return rt(r)&&typeof r.from=="function"}function rt(r){return typeof r=="object"&&typeof r.to=="function"}function zt(r){r.parentElement.removeChild(r)}function St(r){return r!=null}function Ft(r){r.preventDefault()}function Ce(r){return r.filter(function(t){return this[t]?!1:this[t]=!0},{})}function Ee(r,t){return Math.round(r/t)*t}function Ae(r,t){var s=r.getBoundingClientRect(),f=r.ownerDocument,u=f.documentElement,d=Bt(f);return/webkit.*Chrome.*Mobile/i.test(navigator.userAgent)&&(d.x=0),t?s.top+d.y-u.clientTop:s.left+d.x-u.clientLeft}function R(r){return typeof r=="number"&&!isNaN(r)&&isFinite(r)}function Rt(r,t,s){s>0&&(L(r,t),setTimeout(function(){et(r,t)},s))}function jt(r){return Math.max(Math.min(r,100),0)}function it(r){return Array.isArray(r)?r:[r]}function Pe(r){r=String(r);var t=r.split(".");return t.length>1?t[1].length:0}function L(r,t){r.classList&&!/\s/.test(t)?r.classList.add(t):r.className+=" "+t}function et(r,t){r.classList&&!/\s/.test(t)?r.classList.remove(t):r.className=r.className.replace(new RegExp("(^|\\b)"+t.split(" ").join("|")+"(\\b|$)","gi")," ")}function Ve(r,t){return r.classList?r.classList.contains(t):new RegExp("\\b"+t+"\\b").test(r.className)}function Bt(r){var t=window.pageXOffset!==void 0,s=(r.compatMode||"")==="CSS1Compat",f=t?window.pageXOffset:s?r.documentElement.scrollLeft:r.body.scrollLeft,u=t?window.pageYOffset:s?r.documentElement.scrollTop:r.body.scrollTop;return{x:f,y:u}}function De(){return window.navigator.pointerEnabled?{start:"pointerdown",move:"pointermove",end:"pointerup"}:window.navigator.msPointerEnabled?{start:"MSPointerDown",move:"MSPointerMove",end:"MSPointerUp"}:{start:"mousedown touchstart",move:"mousemove touchmove",end:"mouseup touchend"}}function ye(){var r=!1;try{var t=Object.defineProperty({},"passive",{get:function(){r=!0}});window.addEventListener("test",null,t)}catch{}return r}function ke(){return window.CSS&&CSS.supports&&CSS.supports("touch-action","none")}function bt(r,t){return 100/(t-r)}function xt(r,t,s){return t*100/(r[s+1]-r[s])}function Ue(r,t){return xt(r,r[0]<0?t+Math.abs(r[0]):t-r[0],0)}function Me(r,t){return t*(r[1]-r[0])/100+r[0]}function G(r,t){for(var s=1;r>=t[s];)s+=1;return s}function _e(r,t,s){if(s>=r.slice(-1)[0])return 100;var f=G(s,r),u=r[f-1],d=r[f],v=t[f-1],w=t[f];return v+Ue([u,d],s)/bt(v,w)}function Le(r,t,s){if(s>=100)return r.slice(-1)[0];var f=G(s,t),u=r[f-1],d=r[f],v=t[f-1],w=t[f];return Me([u,d],(s-v)*bt(v,w))}function Oe(r,t,s,f){if(f===100)return f;var u=G(f,r),d=r[u-1],v=r[u];return s?f-d>(v-d)/2?v:d:t[u-1]?r[u-1]+Ee(f-r[u-1],t[u-1]):f}var Kt=function(){function r(t,s,f){this.xPct=[],this.xVal=[],this.xSteps=[],this.xNumSteps=[],this.xHighestCompleteStep=[],this.xSteps=[f||!1],this.xNumSteps=[!1],this.snap=s;var u,d=[];for(Object.keys(t).forEach(function(v){d.push([it(t[v]),v])}),d.sort(function(v,w){return v[0][0]-w[0][0]}),u=0;u<d.length;u++)this.handleEntryPoint(d[u][1],d[u][0]);for(this.xNumSteps=this.xSteps.slice(0),u=0;u<this.xNumSteps.length;u++)this.handleStepPoint(u,this.xNumSteps[u])}return r.prototype.getDistance=function(t){for(var s=[],f=0;f<this.xNumSteps.length-1;f++)s[f]=xt(this.xVal,t,f);return s},r.prototype.getAbsoluteDistance=function(t,s,f){var u=0;if(t<this.xPct[this.xPct.length-1])for(;t>this.xPct[u+1];)u++;else t===this.xPct[this.xPct.length-1]&&(u=this.xPct.length-2);!f&&t===this.xPct[u+1]&&u++,s===null&&(s=[]);var d,v=1,w=s[u],C=0,p=0,D=0,y=0;for(f?d=(t-this.xPct[u])/(this.xPct[u+1]-this.xPct[u]):d=(this.xPct[u+1]-t)/(this.xPct[u+1]-this.xPct[u]);w>0;)C=this.xPct[u+1+y]-this.xPct[u+y],s[u+y]*v+100-d*100>100?(p=C*d,v=(w-100*d)/s[u+y],d=1):(p=s[u+y]*C/100*v,v=0),f?(D=D-p,this.xPct.length+y>=1&&y--):(D=D+p,this.xPct.length-y>=1&&y++),w=s[u+y]*v;return t+D},r.prototype.toStepping=function(t){return t=_e(this.xVal,this.xPct,t),t},r.prototype.fromStepping=function(t){return Le(this.xVal,this.xPct,t)},r.prototype.getStep=function(t){return t=Oe(this.xPct,this.xSteps,this.snap,t),t},r.prototype.getDefaultStep=function(t,s,f){var u=G(t,this.xPct);return(t===100||s&&t===this.xPct[u-1])&&(u=Math.max(u-1,1)),(this.xVal[u]-this.xVal[u-1])/f},r.prototype.getNearbySteps=function(t){var s=G(t,this.xPct);return{stepBefore:{startValue:this.xVal[s-2],step:this.xNumSteps[s-2],highestStep:this.xHighestCompleteStep[s-2]},thisStep:{startValue:this.xVal[s-1],step:this.xNumSteps[s-1],highestStep:this.xHighestCompleteStep[s-1]},stepAfter:{startValue:this.xVal[s],step:this.xNumSteps[s],highestStep:this.xHighestCompleteStep[s]}}},r.prototype.countStepDecimals=function(){var t=this.xNumSteps.map(Pe);return Math.max.apply(null,t)},r.prototype.hasNoSize=function(){return this.xVal[0]===this.xVal[this.xVal.length-1]},r.prototype.convert=function(t){return this.getStep(this.toStepping(t))},r.prototype.handleEntryPoint=function(t,s){var f;if(t==="min"?f=0:t==="max"?f=100:f=parseFloat(t),!R(f)||!R(s[0]))throw new Error("noUiSlider: 'range' value isn't numeric.");this.xPct.push(f),this.xVal.push(s[0]);var u=Number(s[1]);f?this.xSteps.push(isNaN(u)?!1:u):isNaN(u)||(this.xSteps[0]=u),this.xHighestCompleteStep.push(0)},r.prototype.handleStepPoint=function(t,s){if(s){if(this.xVal[t]===this.xVal[t+1]){this.xSteps[t]=this.xHighestCompleteStep[t]=this.xVal[t];return}this.xSteps[t]=xt([this.xVal[t],this.xVal[t+1]],s,0)/bt(this.xPct[t],this.xPct[t+1]);var f=(this.xVal[t+1]-this.xVal[t])/this.xNumSteps[t],u=Math.ceil(Number(f.toFixed(3))-1),d=this.xVal[t]+this.xNumSteps[t]*u;this.xHighestCompleteStep[t]=d}},r}(),Nt={to:function(r){return r===void 0?"":r.toFixed(2)},from:Number},It={target:"target",base:"base",origin:"origin",handle:"handle",handleLower:"handle-lower",handleUpper:"handle-upper",touchArea:"touch-area",horizontal:"horizontal",vertical:"vertical",background:"background",connect:"connect",connects:"connects",ltr:"ltr",rtl:"rtl",textDirectionLtr:"txt-dir-ltr",textDirectionRtl:"txt-dir-rtl",draggable:"draggable",drag:"state-drag",tap:"state-tap",active:"active",tooltip:"tooltip",pips:"pips",pipsHorizontal:"pips-horizontal",pipsVertical:"pips-vertical",marker:"marker",markerHorizontal:"marker-horizontal",markerVertical:"marker-vertical",markerNormal:"marker-normal",markerLarge:"marker-large",markerSub:"marker-sub",value:"value",valueHorizontal:"value-horizontal",valueVertical:"value-vertical",valueNormal:"value-normal",valueLarge:"value-large",valueSub:"value-sub"},K={tooltips:".__tooltips",aria:".__aria"};function He(r,t){if(!R(t))throw new Error("noUiSlider: 'step' is not numeric.");r.singleStep=t}function ze(r,t){if(!R(t))throw new Error("noUiSlider: 'keyboardPageMultiplier' is not numeric.");r.keyboardPageMultiplier=t}function Fe(r,t){if(!R(t))throw new Error("noUiSlider: 'keyboardMultiplier' is not numeric.");r.keyboardMultiplier=t}function Re(r,t){if(!R(t))throw new Error("noUiSlider: 'keyboardDefaultStep' is not numeric.");r.keyboardDefaultStep=t}function je(r,t){if(typeof t!="object"||Array.isArray(t))throw new Error("noUiSlider: 'range' is not an object.");if(t.min===void 0||t.max===void 0)throw new Error("noUiSlider: Missing 'min' or 'max' in 'range'.");r.spectrum=new Kt(t,r.snap||!1,r.singleStep)}function Ne(r,t){if(t=it(t),!Array.isArray(t)||!t.length)throw new Error("noUiSlider: 'start' option is incorrect.");r.handles=t.length,r.start=t}function Be(r,t){if(typeof t!="boolean")throw new Error("noUiSlider: 'snap' option must be a boolean.");r.snap=t}function Ke(r,t){if(typeof t!="boolean")throw new Error("noUiSlider: 'animate' option must be a boolean.");r.animate=t}function Ie(r,t){if(typeof t!="number")throw new Error("noUiSlider: 'animationDuration' option must be a number.");r.animationDuration=t}function qt(r,t){var s=[!1],f;if(t==="lower"?t=[!0,!1]:t==="upper"&&(t=[!1,!0]),t===!0||t===!1){for(f=1;f<r.handles;f++)s.push(t);s.push(!1)}else{if(!Array.isArray(t)||!t.length||t.length!==r.handles+1)throw new Error("noUiSlider: 'connect' option doesn't match handle count.");s=t}r.connect=s}function qe(r,t){switch(t){case"horizontal":r.ort=0;break;case"vertical":r.ort=1;break;default:throw new Error("noUiSlider: 'orientation' option is invalid.")}}function Tt(r,t){if(!R(t))throw new Error("noUiSlider: 'margin' option must be numeric.");t!==0&&(r.margin=r.spectrum.getDistance(t))}function Te(r,t){if(!R(t))throw new Error("noUiSlider: 'limit' option must be numeric.");if(r.limit=r.spectrum.getDistance(t),!r.limit||r.handles<2)throw new Error("noUiSlider: 'limit' option is only supported on linear sliders with 2 or more handles.")}function Xe(r,t){var s;if(!R(t)&&!Array.isArray(t))throw new Error("noUiSlider: 'padding' option must be numeric or array of exactly 2 numbers.");if(Array.isArray(t)&&!(t.length===2||R(t[0])||R(t[1])))throw new Error("noUiSlider: 'padding' option must be numeric or array of exactly 2 numbers.");if(t!==0){for(Array.isArray(t)||(t=[t,t]),r.padding=[r.spectrum.getDistance(t[0]),r.spectrum.getDistance(t[1])],s=0;s<r.spectrum.xNumSteps.length-1;s++)if(r.padding[0][s]<0||r.padding[1][s]<0)throw new Error("noUiSlider: 'padding' option must be a positive number(s).");var f=t[0]+t[1],u=r.spectrum.xVal[0],d=r.spectrum.xVal[r.spectrum.xVal.length-1];if(f/(d-u)>1)throw new Error("noUiSlider: 'padding' option must not exceed 100% of the range.")}}function Ye(r,t){switch(t){case"ltr":r.dir=0;break;case"rtl":r.dir=1;break;default:throw new Error("noUiSlider: 'direction' option was not recognized.")}}function We(r,t){if(typeof t!="string")throw new Error("noUiSlider: 'behaviour' must be a string containing options.");var s=t.indexOf("tap")>=0,f=t.indexOf("drag")>=0,u=t.indexOf("fixed")>=0,d=t.indexOf("snap")>=0,v=t.indexOf("hover")>=0,w=t.indexOf("unconstrained")>=0,C=t.indexOf("invert-connects")>=0,p=t.indexOf("drag-all")>=0,D=t.indexOf("smooth-steps")>=0;if(u){if(r.handles!==2)throw new Error("noUiSlider: 'fixed' behaviour must be used with 2 handles");Tt(r,r.start[1]-r.start[0])}if(C&&r.handles!==2)throw new Error("noUiSlider: 'invert-connects' behaviour must be used with 2 handles");if(w&&(r.margin||r.limit))throw new Error("noUiSlider: 'unconstrained' behaviour cannot be used with margin or limit");r.events={tap:s||d,drag:f,dragAll:p,smoothSteps:D,fixed:u,snap:d,hover:v,unconstrained:w,invertConnects:C}}function $e(r,t){if(t!==!1)if(t===!0||rt(t)){r.tooltips=[];for(var s=0;s<r.handles;s++)r.tooltips.push(t)}else{if(t=it(t),t.length!==r.handles)throw new Error("noUiSlider: must pass a formatter for all handles.");t.forEach(function(f){if(typeof f!="boolean"&&!rt(f))throw new Error("noUiSlider: 'tooltips' must be passed a formatter or 'false'.")}),r.tooltips=t}}function Ge(r,t){if(t.length!==r.handles)throw new Error("noUiSlider: must pass a attributes for all handles.");r.handleAttributes=t}function Je(r,t){if(!rt(t))throw new Error("noUiSlider: 'ariaFormat' requires 'to' method.");r.ariaFormat=t}function Ze(r,t){if(!we(t))throw new Error("noUiSlider: 'format' requires 'to' and 'from' methods.");r.format=t}function Qe(r,t){if(typeof t!="boolean")throw new Error("noUiSlider: 'keyboardSupport' option must be a boolean.");r.keyboardSupport=t}function tr(r,t){r.documentElement=t}function er(r,t){if(typeof t!="string"&&t!==!1)throw new Error("noUiSlider: 'cssPrefix' must be a string or `false`.");r.cssPrefix=t}function rr(r,t){if(typeof t!="object")throw new Error("noUiSlider: 'cssClasses' must be an object.");typeof r.cssPrefix=="string"?(r.cssClasses={},Object.keys(t).forEach(function(s){r.cssClasses[s]=r.cssPrefix+t[s]})):r.cssClasses=t}function Xt(r){var t={margin:null,limit:null,padding:null,animate:!0,animationDuration:300,ariaFormat:Nt,format:Nt},s={step:{r:!1,t:He},keyboardPageMultiplier:{r:!1,t:ze},keyboardMultiplier:{r:!1,t:Fe},keyboardDefaultStep:{r:!1,t:Re},start:{r:!0,t:Ne},connect:{r:!0,t:qt},direction:{r:!0,t:Ye},snap:{r:!1,t:Be},animate:{r:!1,t:Ke},animationDuration:{r:!1,t:Ie},range:{r:!0,t:je},orientation:{r:!1,t:qe},margin:{r:!1,t:Tt},limit:{r:!1,t:Te},padding:{r:!1,t:Xe},behaviour:{r:!0,t:We},ariaFormat:{r:!1,t:Je},format:{r:!1,t:Ze},tooltips:{r:!1,t:$e},keyboardSupport:{r:!0,t:Qe},documentElement:{r:!1,t:tr},cssPrefix:{r:!0,t:er},cssClasses:{r:!0,t:rr},handleAttributes:{r:!1,t:Ge}},f={connect:!1,direction:"ltr",behaviour:"tap",orientation:"horizontal",keyboardSupport:!0,cssPrefix:"noUi-",cssClasses:It,keyboardPageMultiplier:5,keyboardMultiplier:1,keyboardDefaultStep:10};r.format&&!r.ariaFormat&&(r.ariaFormat=r.format),Object.keys(s).forEach(function(C){if(!St(r[C])&&f[C]===void 0){if(s[C].r)throw new Error("noUiSlider: '"+C+"' is required.");return}s[C].t(t,St(r[C])?r[C]:f[C])}),t.pips=r.pips;var u=document.createElement("div"),d=u.style.msTransform!==void 0,v=u.style.transform!==void 0;t.transformRule=v?"transform":d?"msTransform":"webkitTransform";var w=[["left","top"],["right","bottom"]];return t.style=w[t.dir][t.ort],t}function ir(r,t,s){var f=De(),u=ke(),d=u&&ye(),v=r,w,C,p,D,y,j,m=t.spectrum,z=[],b=[],M=[],Y=0,F={},q=!1,B=r.ownerDocument,H=t.documentElement||B.documentElement,J=B.body,Wt=B.dir==="rtl"||t.ort===1?0:100;function N(e,i){var a=B.createElement("div");return i&&L(a,i),e.appendChild(a),a}function $t(e,i){var a=N(e,t.cssClasses.origin),n=N(a,t.cssClasses.handle);if(N(n,t.cssClasses.touchArea),n.setAttribute("data-handle",String(i)),t.keyboardSupport&&(n.setAttribute("tabindex","0"),n.addEventListener("keydown",function(l){return ce(l,i)})),t.handleAttributes!==void 0){var o=t.handleAttributes[i];Object.keys(o).forEach(function(l){n.setAttribute(l,o[l])})}return n.setAttribute("role","slider"),n.setAttribute("aria-orientation",t.ort?"vertical":"horizontal"),i===0?L(n,t.cssClasses.handleLower):i===t.handles-1&&L(n,t.cssClasses.handleUpper),a.handle=n,a}function at(e,i){return i?N(e,t.cssClasses.connect):!1}function Gt(e,i){C=N(i,t.cssClasses.connects),p=[],D=[],D.push(at(C,e[0]));for(var a=0;a<t.handles;a++)p.push($t(i,a)),M[a]=a,D.push(at(C,e[a+1]))}function Jt(e){L(e,t.cssClasses.target),t.dir===0?L(e,t.cssClasses.ltr):L(e,t.cssClasses.rtl),t.ort===0?L(e,t.cssClasses.horizontal):L(e,t.cssClasses.vertical);var i=getComputedStyle(e).direction;return i==="rtl"?L(e,t.cssClasses.textDirectionRtl):L(e,t.cssClasses.textDirectionLtr),N(e,t.cssClasses.base)}function Zt(e,i){return!t.tooltips||!t.tooltips[i]?!1:N(e.firstChild,t.cssClasses.tooltip)}function wt(){return v.hasAttribute("disabled")}function nt(e){var i=p[e];return i.hasAttribute("disabled")}function Qt(e){e!=null?(p[e].setAttribute("disabled",""),p[e].handle.removeAttribute("tabindex")):(v.setAttribute("disabled",""),p.forEach(function(i){i.handle.removeAttribute("tabindex")}))}function te(e){e!=null?(p[e].removeAttribute("disabled"),p[e].handle.setAttribute("tabindex","0")):(v.removeAttribute("disabled"),p.forEach(function(i){i.removeAttribute("disabled"),i.handle.setAttribute("tabindex","0")}))}function st(){j&&(W("update"+K.tooltips),j.forEach(function(e){e&&zt(e)}),j=null)}function Ct(){st(),j=p.map(Zt),ct("update"+K.tooltips,function(e,i,a){if(!(!j||!t.tooltips)&&j[i]!==!1){var n=e[i];t.tooltips[i]!==!0&&(n=t.tooltips[i].to(a[i])),j[i].innerHTML=n}})}function ee(){W("update"+K.aria),ct("update"+K.aria,function(e,i,a,n,o){M.forEach(function(l){var h=p[l],c=Z(b,l,0,!0,!0,!0),S=Z(b,l,100,!0,!0,!0),x=o[l],E=String(t.ariaFormat.to(a[l]));c=m.fromStepping(c).toFixed(1),S=m.fromStepping(S).toFixed(1),x=m.fromStepping(x).toFixed(1),h.children[0].setAttribute("aria-valuemin",c),h.children[0].setAttribute("aria-valuemax",S),h.children[0].setAttribute("aria-valuenow",x),h.children[0].setAttribute("aria-valuetext",E)})})}function re(e){if(e.mode===I.Range||e.mode===I.Steps)return m.xVal;if(e.mode===I.Count){if(e.values<2)throw new Error("noUiSlider: 'values' (>= 2) required for mode 'count'.");for(var i=e.values-1,a=100/i,n=[];i--;)n[i]=i*a;return n.push(100),Et(n,e.stepped)}return e.mode===I.Positions?Et(e.values,e.stepped):e.mode===I.Values?e.stepped?e.values.map(function(o){return m.fromStepping(m.getStep(m.toStepping(o)))}):e.values:[]}function Et(e,i){return e.map(function(a){return m.fromStepping(i?m.getStep(a):a)})}function ie(e){function i(x,E){return Number((x+E).toFixed(7))}var a=re(e),n={},o=m.xVal[0],l=m.xVal[m.xVal.length-1],h=!1,c=!1,S=0;return a=Ce(a.slice().sort(function(x,E){return x-E})),a[0]!==o&&(a.unshift(o),h=!0),a[a.length-1]!==l&&(a.push(l),c=!0),a.forEach(function(x,E){var A,g,V,_=x,k=a[E+1],U,dt,pt,mt,Lt,gt,Ot,Ht=e.mode===I.Steps;for(Ht&&(A=m.xNumSteps[E]),A||(A=k-_),k===void 0&&(k=_),A=Math.max(A,1e-7),g=_;g<=k;g=i(g,A)){for(U=m.toStepping(g),dt=U-S,Lt=dt/(e.density||1),gt=Math.round(Lt),Ot=dt/gt,V=1;V<=gt;V+=1)pt=S+V*Ot,n[pt.toFixed(5)]=[m.fromStepping(pt),0];mt=a.indexOf(g)>-1?O.LargeValue:Ht?O.SmallValue:O.NoValue,!E&&h&&g!==k&&(mt=0),g===k&&c||(n[U.toFixed(5)]=[g,mt]),S=U}}),n}function ae(e,i,a){var n,o,l=B.createElement("div"),h=(n={},n[O.None]="",n[O.NoValue]=t.cssClasses.valueNormal,n[O.LargeValue]=t.cssClasses.valueLarge,n[O.SmallValue]=t.cssClasses.valueSub,n),c=(o={},o[O.None]="",o[O.NoValue]=t.cssClasses.markerNormal,o[O.LargeValue]=t.cssClasses.markerLarge,o[O.SmallValue]=t.cssClasses.markerSub,o),S=[t.cssClasses.valueHorizontal,t.cssClasses.valueVertical],x=[t.cssClasses.markerHorizontal,t.cssClasses.markerVertical];L(l,t.cssClasses.pips),L(l,t.ort===0?t.cssClasses.pipsHorizontal:t.cssClasses.pipsVertical);function E(g,V){var _=V===t.cssClasses.value,k=_?S:x,U=_?h:c;return V+" "+k[t.ort]+" "+U[g]}function A(g,V,_){if(_=i?i(V,_):_,_!==O.None){var k=N(l,!1);k.className=E(_,t.cssClasses.marker),k.style[t.style]=g+"%",_>O.NoValue&&(k=N(l,!1),k.className=E(_,t.cssClasses.value),k.setAttribute("data-value",String(V)),k.style[t.style]=g+"%",k.innerHTML=String(a.to(V)))}}return Object.keys(e).forEach(function(g){A(g,e[g][0],e[g][1])}),l}function ot(){y&&(zt(y),y=null)}function lt(e){ot();var i=ie(e),a=e.filter,n=e.format||{to:function(o){return String(Math.round(o))}};return y=v.appendChild(ae(i,a,n)),y}function At(){var e=w.getBoundingClientRect(),i="offset"+["Width","Height"][t.ort];return t.ort===0?e.width||w[i]:e.height||w[i]}function T(e,i,a,n){var o=function(h){var c=ne(h,n.pageOffset,n.target||i);if(!c||wt()&&!n.doNotReject||Ve(v,t.cssClasses.tap)&&!n.doNotReject||e===f.start&&c.buttons!==void 0&&c.buttons>1||n.hover&&c.buttons)return!1;d||c.preventDefault(),c.calcPoint=c.points[t.ort],a(c,n)},l=[];return e.split(" ").forEach(function(h){i.addEventListener(h,o,d?{passive:!0}:!1),l.push([h,o])}),l}function ne(e,i,a){var n=e.type.indexOf("touch")===0,o=e.type.indexOf("mouse")===0,l=e.type.indexOf("pointer")===0,h=0,c=0;if(e.type.indexOf("MSPointer")===0&&(l=!0),e.type==="mousedown"&&!e.buttons&&!e.touches)return!1;if(n){var S=function(A){var g=A.target;return g===a||a.contains(g)||e.composed&&e.composedPath().shift()===a};if(e.type==="touchstart"){var x=Array.prototype.filter.call(e.touches,S);if(x.length>1)return!1;h=x[0].pageX,c=x[0].pageY}else{var E=Array.prototype.find.call(e.changedTouches,S);if(!E)return!1;h=E.pageX,c=E.pageY}}return i=i||Bt(B),(o||l)&&(h=e.clientX+i.x,c=e.clientY+i.y),e.pageOffset=i,e.points=[h,c],e.cursor=o||l,e}function Pt(e){var i=e-Ae(w,t.ort),a=i*100/At();return a=jt(a),t.dir?100-a:a}function se(e){var i=100,a=!1;return p.forEach(function(n,o){if(!nt(o)){var l=b[o],h=Math.abs(l-e),c=h===100&&i===100,S=h<i,x=h<=i&&e>l;(S||x||c)&&(a=o,i=h)}}),a}function oe(e,i){e.type==="mouseout"&&e.target.nodeName==="HTML"&&e.relatedTarget===null&&ft(e,i)}function le(e,i){if(navigator.appVersion.indexOf("MSIE 9")===-1&&e.buttons===0&&i.buttonsProperty!==0)return ft(e,i);var a=(t.dir?-1:1)*(e.calcPoint-i.startCalcPoint),n=a*100/i.baseSize;Dt(a>0,n,i.locations,i.handleNumbers,i.connect)}function ft(e,i){i.handle&&(et(i.handle,t.cssClasses.active),Y-=1),i.listeners.forEach(function(a){H.removeEventListener(a[0],a[1])}),Y===0&&(et(v,t.cssClasses.drag),vt(),e.cursor&&(J.style.cursor="",J.removeEventListener("selectstart",Ft))),t.events.smoothSteps&&(i.handleNumbers.forEach(function(a){X(a,b[a],!0,!0,!1,!1)}),i.handleNumbers.forEach(function(a){P("update",a)})),i.handleNumbers.forEach(function(a){P("change",a),P("set",a),P("end",a)})}function ut(e,i){if(!i.handleNumbers.some(nt)){var a;if(i.handleNumbers.length===1){var n=p[i.handleNumbers[0]];a=n.children[0],Y+=1,L(a,t.cssClasses.active)}e.stopPropagation();var o=[],l=T(f.move,H,le,{target:e.target,handle:a,connect:i.connect,listeners:o,startCalcPoint:e.calcPoint,baseSize:At(),pageOffset:e.pageOffset,handleNumbers:i.handleNumbers,buttonsProperty:e.buttons,locations:b.slice()}),h=T(f.end,H,ft,{target:e.target,handle:a,listeners:o,doNotReject:!0,handleNumbers:i.handleNumbers}),c=T("mouseout",H,oe,{target:e.target,handle:a,listeners:o,doNotReject:!0,handleNumbers:i.handleNumbers});o.push.apply(o,l.concat(h,c)),e.cursor&&(J.style.cursor=getComputedStyle(e.target).cursor,p.length>1&&L(v,t.cssClasses.drag),J.addEventListener("selectstart",Ft,!1)),i.handleNumbers.forEach(function(S){P("start",S)})}}function fe(e){e.stopPropagation();var i=Pt(e.calcPoint),a=se(i);a!==!1&&(t.events.snap||Rt(v,t.cssClasses.tap,t.animationDuration),X(a,i,!0,!0),vt(),P("slide",a,!0),P("update",a,!0),t.events.snap?ut(e,{handleNumbers:[a]}):(P("change",a,!0),P("set",a,!0)))}function ue(e){var i=Pt(e.calcPoint),a=m.getStep(i),n=m.fromStepping(a);Object.keys(F).forEach(function(o){o.split(".")[0]==="hover"&&F[o].forEach(function(l){l.call(tt,n)})})}function ce(e,i){if(wt()||nt(i))return!1;var a=["Left","Right"],n=["Down","Up"],o=["PageDown","PageUp"],l=["Home","End"];t.dir&&!t.ort?a.reverse():t.ort&&!t.dir&&(n.reverse(),o.reverse());var h=e.key.replace("Arrow",""),c=h===o[0],S=h===o[1],x=h===n[0]||h===a[0]||c,E=h===n[1]||h===a[1]||S,A=h===l[0],g=h===l[1];if(!x&&!E&&!A&&!g)return!0;e.preventDefault();var V;if(E||x){var _=x?0:1,k=Mt(i),U=k[_];if(U===null)return!1;U===!1&&(U=m.getDefaultStep(b[i],x,t.keyboardDefaultStep)),S||c?U*=t.keyboardPageMultiplier:U*=t.keyboardMultiplier,U=Math.max(U,1e-7),U=(x?-1:1)*U,V=z[i]+U}else g?V=t.spectrum.xVal[t.spectrum.xVal.length-1]:V=t.spectrum.xVal[0];return X(i,m.toStepping(V),!0,!0),P("slide",i),P("update",i),P("change",i),P("set",i),!1}function Vt(e){e.fixed||p.forEach(function(i,a){T(f.start,i.children[0],ut,{handleNumbers:[a]})}),e.tap&&T(f.start,w,fe,{}),e.hover&&T(f.move,w,ue,{hover:!0}),e.drag&&D.forEach(function(i,a){if(!(i===!1||a===0||a===D.length-1)){var n=p[a-1],o=p[a],l=[i],h=[n,o],c=[a-1,a];L(i,t.cssClasses.draggable),e.fixed&&(l.push(n.children[0]),l.push(o.children[0])),e.dragAll&&(h=p,c=M),l.forEach(function(S){T(f.start,S,ut,{handles:h,handleNumbers:c,connect:i})})}})}function ct(e,i){F[e]=F[e]||[],F[e].push(i),e.split(".")[0]==="update"&&p.forEach(function(a,n){P("update",n)})}function he(e){return e===K.aria||e===K.tooltips}function W(e){var i=e&&e.split(".")[0],a=i?e.substring(i.length):e;Object.keys(F).forEach(function(n){var o=n.split(".")[0],l=n.substring(o.length);(!i||i===o)&&(!a||a===l)&&(!he(l)||a===l)&&delete F[n]})}function P(e,i,a){Object.keys(F).forEach(function(n){var o=n.split(".")[0];e===o&&F[n].forEach(function(l){l.call(tt,z.map(t.format.to),i,z.slice(),a||!1,b.slice(),tt)})})}function Z(e,i,a,n,o,l,h){var c;return p.length>1&&!t.events.unconstrained&&(n&&i>0&&(c=m.getAbsoluteDistance(e[i-1],t.margin,!1),a=Math.max(a,c)),o&&i<p.length-1&&(c=m.getAbsoluteDistance(e[i+1],t.margin,!0),a=Math.min(a,c))),p.length>1&&t.limit&&(n&&i>0&&(c=m.getAbsoluteDistance(e[i-1],t.limit,!1),a=Math.min(a,c)),o&&i<p.length-1&&(c=m.getAbsoluteDistance(e[i+1],t.limit,!0),a=Math.max(a,c))),t.padding&&(i===0&&(c=m.getAbsoluteDistance(0,t.padding[0],!1),a=Math.max(a,c)),i===p.length-1&&(c=m.getAbsoluteDistance(100,t.padding[1],!0),a=Math.min(a,c))),h||(a=m.getStep(a)),a=jt(a),a===e[i]&&!l?!1:a}function ht(e,i){var a=t.ort;return(a?i:e)+", "+(a?e:i)}function Dt(e,i,a,n,o){var l=a.slice(),h=n[0],c=t.events.smoothSteps,S=[!e,e],x=[e,!e];n=n.slice(),e&&n.reverse(),n.length>1?n.forEach(function(A,g){var V=Z(l,A,l[A]+i,S[g],x[g],!1,c);V===!1?i=0:(i=V-l[A],l[A]=V)}):S=x=[!0];var E=!1;n.forEach(function(A,g){E=X(A,a[A]+i,S[g],x[g],!1,c)||E}),E&&(n.forEach(function(A){P("update",A),P("slide",A)}),o!=null&&P("drag",h))}function yt(e,i){return t.dir?100-e-i:e}function ve(e,i){b[e]=i,z[e]=m.fromStepping(i);var a=yt(i,0)-Wt,n="translate("+ht(a+"%","0")+")";if(p[e].style[t.transformRule]=n,t.events.invertConnects&&b.length>1){var o=b.every(function(l,h,c){return h===0||l>=c[h-1]});if(q!==!o){xe();return}}$(e),$(e+1),q&&($(e-1),$(e+2))}function vt(){M.forEach(function(e){var i=b[e]>50?-1:1,a=3+(p.length+i*e);p[e].style.zIndex=String(a)})}function X(e,i,a,n,o,l){return o||(i=Z(b,e,i,a,n,!1,l)),i===!1?!1:(ve(e,i),!0)}function $(e){if(D[e]){var i=b.slice();q&&i.sort(function(c,S){return c-S});var a=0,n=100;e!==0&&(a=i[e-1]),e!==D.length-1&&(n=i[e]);var o=n-a,l="translate("+ht(yt(a,o)+"%","0")+")",h="scale("+ht(o/100,"1")+")";D[e].style[t.transformRule]=l+" "+h}}function kt(e,i){return e===null||e===!1||e===void 0||(typeof e=="number"&&(e=String(e)),e=t.format.from(e),e!==!1&&(e=m.toStepping(e)),e===!1||isNaN(e))?b[i]:e}function Q(e,i,a){var n=it(e),o=b[0]===void 0;i=i===void 0?!0:i,t.animate&&!o&&Rt(v,t.cssClasses.tap,t.animationDuration),M.forEach(function(c){X(c,kt(n[c],c),!0,!1,a)});var l=M.length===1?0:1;if(o&&m.hasNoSize()&&(a=!0,b[0]=0,M.length>1)){var h=100/(M.length-1);M.forEach(function(c){b[c]=c*h})}for(;l<M.length;++l)M.forEach(function(c){X(c,b[c],!0,!0,a)});vt(),M.forEach(function(c){P("update",c),n[c]!==null&&i&&P("set",c)})}function de(e){Q(t.start,e)}function pe(e,i,a,n){if(e=Number(e),!(e>=0&&e<M.length))throw new Error("noUiSlider: invalid handle number, got: "+e);X(e,kt(i,e),!0,!0,n),P("update",e),a&&P("set",e)}function Ut(e){if(e===void 0&&(e=!1),e)return z.length===1?z[0]:z.slice(0);var i=z.map(t.format.to);return i.length===1?i[0]:i}function me(){for(W(K.aria),W(K.tooltips),Object.keys(t.cssClasses).forEach(function(e){et(v,t.cssClasses[e])});v.firstChild;)v.removeChild(v.firstChild);delete v.noUiSlider}function Mt(e){var i=b[e],a=m.getNearbySteps(i),n=z[e],o=a.thisStep.step,l=null;if(t.snap)return[n-a.stepBefore.startValue||null,a.stepAfter.startValue-n||null];o!==!1&&n+o>a.stepAfter.startValue&&(o=a.stepAfter.startValue-n),n>a.thisStep.startValue?l=a.thisStep.step:a.stepBefore.step===!1?l=!1:l=n-a.stepBefore.highestStep,i===100?o=null:i===0&&(l=null);var h=m.countStepDecimals();return o!==null&&o!==!1&&(o=Number(o.toFixed(h))),l!==null&&l!==!1&&(l=Number(l.toFixed(h))),[l,o]}function ge(){return M.map(Mt)}function Se(e,i){var a=Ut(),n=["margin","limit","padding","range","animate","snap","step","format","pips","tooltips","connect"];n.forEach(function(l){e[l]!==void 0&&(s[l]=e[l])});var o=Xt(s);n.forEach(function(l){e[l]!==void 0&&(t[l]=o[l])}),m=o.spectrum,t.margin=o.margin,t.limit=o.limit,t.padding=o.padding,t.pips?lt(t.pips):ot(),t.tooltips?Ct():st(),b=[],Q(St(e.start)?e.start:a,i),e.connect&&_t()}function _t(){for(;C.firstChild;)C.removeChild(C.firstChild);for(var e=0;e<=t.handles;e++)D[e]=at(C,t.connect[e]),$(e);Vt({drag:t.events.drag,fixed:!0})}function xe(){q=!q,qt(t,t.connect.map(function(e){return!e})),_t()}function be(){w=Jt(v),Gt(t.connect,w),Vt(t.events),Q(t.start),t.pips&&lt(t.pips),t.tooltips&&Ct(),ee()}be();var tt={destroy:me,steps:ge,on:ct,off:W,get:Ut,set:Q,setHandle:pe,reset:de,disable:Qt,enable:te,__moveHandles:function(e,i,a){Dt(e,i,b,a)},options:s,updateOptions:Se,target:v,removePips:ot,removeTooltips:st,getPositions:function(){return b.slice()},getTooltips:function(){return j},getOrigins:function(){return p},pips:lt};return tt}function ar(r,t){if(!r||!r.nodeName)throw new Error("noUiSlider: create requires a single element, got: "+r);if(r.noUiSlider)throw new Error("noUiSlider: Slider was already initialized.");var s=Xt(t),f=ir(r,s,t);return r.noUiSlider=f,f}var Yt={__spectrum:Kt,cssClasses:It,create:ar};function nr({arePipsStepped:r,behavior:t,decimalPlaces:s,fillTrack:f,isDisabled:u,isRtl:d,isVertical:v,maxDifference:w,minDifference:C,maxValue:p,minValue:D,nonLinearPoints:y,pipsDensity:j,pipsFilter:m,pipsFormatter:z,pipsMode:b,pipsValues:M,rangePadding:Y,state:F,step:q,tooltips:B}){return{state:F,slider:null,init(){this.slider=Yt.create(this.$el,{behaviour:t,direction:d?"rtl":"ltr",connect:f,format:{from:H=>H,to:H=>s!==null?+H.toFixed(s):H},limit:w,margin:C,orientation:v?"vertical":"horizontal",padding:Y,pips:b?{density:j??10,filter:m,format:z,mode:b,stepped:r,values:M}:null,range:{min:D,...y??{},max:p},start:Alpine.raw(this.state),step:q,tooltips:B}),u&&this.slider.disable(),this.slider.on("change",H=>{this.state=H.length>1?H:H[0]}),this.$watch("state",()=>{this.slider.set(Alpine.raw(this.state))})},destroy(){this.slider.destroy(),this.slider=null}}}export{nr as default};
