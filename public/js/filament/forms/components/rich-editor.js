function he(t){this.content=t}he.prototype={constructor:he,find:function(t){for(var e=0;e<this.content.length;e+=2)if(this.content[e]===t)return e;return-1},get:function(t){var e=this.find(t);return e==-1?void 0:this.content[e+1]},update:function(t,e,n){var r=n&&n!=t?this.remove(n):this,o=r.find(t),i=r.content.slice();return o==-1?i.push(n||t,e):(i[o+1]=e,n&&(i[o]=n)),new he(i)},remove:function(t){var e=this.find(t);if(e==-1)return this;var n=this.content.slice();return n.splice(e,2),new he(n)},addToStart:function(t,e){return new he([t,e].concat(this.remove(t).content))},addToEnd:function(t,e){var n=this.remove(t).content.slice();return n.push(t,e),new he(n)},addBefore:function(t,e,n){var r=this.remove(e),o=r.content.slice(),i=r.find(t);return o.splice(i==-1?o.length:i,0,e,n),new he(o)},forEach:function(t){for(var e=0;e<this.content.length;e+=2)t(this.content[e],this.content[e+1])},prepend:function(t){return t=he.from(t),t.size?new he(t.content.concat(this.subtract(t).content)):this},append:function(t){return t=he.from(t),t.size?new he(this.subtract(t).content.concat(t.content)):this},subtract:function(t){var e=this;t=he.from(t);for(var n=0;n<t.content.length;n+=2)e=e.remove(t.content[n]);return e},toObject:function(){var t={};return this.forEach(function(e,n){t[e]=n}),t},get size(){return this.content.length>>1}};he.from=function(t){if(t instanceof he)return t;var e=[];if(t)for(var n in t)e.push(n,t[n]);return new he(e)};var bi=he;function ca(t,e,n){for(let r=0;;r++){if(r==t.childCount||r==e.childCount)return t.childCount==e.childCount?null:n;let o=t.child(r),i=e.child(r);if(o==i){n+=o.nodeSize;continue}if(!o.sameMarkup(i))return n;if(o.isText&&o.text!=i.text){for(let s=0;o.text[s]==i.text[s];s++)n++;return n}if(o.content.size||i.content.size){let s=ca(o.content,i.content,n+1);if(s!=null)return s}n+=o.nodeSize}}function ua(t,e,n,r){for(let o=t.childCount,i=e.childCount;;){if(o==0||i==0)return o==i?null:{a:n,b:r};let s=t.child(--o),l=e.child(--i),a=s.nodeSize;if(s==l){n-=a,r-=a;continue}if(!s.sameMarkup(l))return{a:n,b:r};if(s.isText&&s.text!=l.text){let c=0,u=Math.min(s.text.length,l.text.length);for(;c<u&&s.text[s.text.length-c-1]==l.text[l.text.length-c-1];)c++,n--,r--;return{a:n,b:r}}if(s.content.size||l.content.size){let c=ua(s.content,l.content,n-1,r-1);if(c)return c}n-=a,r-=a}}var w=class t{constructor(e,n){if(this.content=e,this.size=n||0,n==null)for(let r=0;r<e.length;r++)this.size+=e[r].nodeSize}nodesBetween(e,n,r,o=0,i){for(let s=0,l=0;l<n;s++){let a=this.content[s],c=l+a.nodeSize;if(c>e&&r(a,o+l,i||null,s)!==!1&&a.content.size){let u=l+1;a.nodesBetween(Math.max(0,e-u),Math.min(a.content.size,n-u),r,o+u)}l=c}}descendants(e){this.nodesBetween(0,this.size,e)}textBetween(e,n,r,o){let i="",s=!0;return this.nodesBetween(e,n,(l,a)=>{let c=l.isText?l.text.slice(Math.max(e,a)-a,n-a):l.isLeaf?o?typeof o=="function"?o(l):o:l.type.spec.leafText?l.type.spec.leafText(l):"":"";l.isBlock&&(l.isLeaf&&c||l.isTextblock)&&r&&(s?s=!1:i+=r),i+=c},0),i}append(e){if(!e.size)return this;if(!this.size)return e;let n=this.lastChild,r=e.firstChild,o=this.content.slice(),i=0;for(n.isText&&n.sameMarkup(r)&&(o[o.length-1]=n.withText(n.text+r.text),i=1);i<e.content.length;i++)o.push(e.content[i]);return new t(o,this.size+e.size)}cut(e,n=this.size){if(e==0&&n==this.size)return this;let r=[],o=0;if(n>e)for(let i=0,s=0;s<n;i++){let l=this.content[i],a=s+l.nodeSize;a>e&&((s<e||a>n)&&(l.isText?l=l.cut(Math.max(0,e-s),Math.min(l.text.length,n-s)):l=l.cut(Math.max(0,e-s-1),Math.min(l.content.size,n-s-1))),r.push(l),o+=l.nodeSize),s=a}return new t(r,o)}cutByIndex(e,n){return e==n?t.empty:e==0&&n==this.content.length?this:new t(this.content.slice(e,n))}replaceChild(e,n){let r=this.content[e];if(r==n)return this;let o=this.content.slice(),i=this.size+n.nodeSize-r.nodeSize;return o[e]=n,new t(o,i)}addToStart(e){return new t([e].concat(this.content),this.size+e.nodeSize)}addToEnd(e){return new t(this.content.concat(e),this.size+e.nodeSize)}eq(e){if(this.content.length!=e.content.length)return!1;for(let n=0;n<this.content.length;n++)if(!this.content[n].eq(e.content[n]))return!1;return!0}get firstChild(){return this.content.length?this.content[0]:null}get lastChild(){return this.content.length?this.content[this.content.length-1]:null}get childCount(){return this.content.length}child(e){let n=this.content[e];if(!n)throw new RangeError("Index "+e+" out of range for "+this);return n}maybeChild(e){return this.content[e]||null}forEach(e){for(let n=0,r=0;n<this.content.length;n++){let o=this.content[n];e(o,r,n),r+=o.nodeSize}}findDiffStart(e,n=0){return ca(this,e,n)}findDiffEnd(e,n=this.size,r=e.size){return ua(this,e,n,r)}findIndex(e){if(e==0)return Sr(0,e);if(e==this.size)return Sr(this.content.length,e);if(e>this.size||e<0)throw new RangeError(`Position ${e} outside of fragment (${this})`);for(let n=0,r=0;;n++){let o=this.child(n),i=r+o.nodeSize;if(i>=e)return i==e?Sr(n+1,i):Sr(n,r);r=i}}toString(){return"<"+this.toStringInner()+">"}toStringInner(){return this.content.join(", ")}toJSON(){return this.content.length?this.content.map(e=>e.toJSON()):null}static fromJSON(e,n){if(!n)return t.empty;if(!Array.isArray(n))throw new RangeError("Invalid input for Fragment.fromJSON");return new t(n.map(e.nodeFromJSON))}static fromArray(e){if(!e.length)return t.empty;let n,r=0;for(let o=0;o<e.length;o++){let i=e[o];r+=i.nodeSize,o&&i.isText&&e[o-1].sameMarkup(i)?(n||(n=e.slice(0,o)),n[n.length-1]=i.withText(n[n.length-1].text+i.text)):n&&n.push(i)}return new t(n||e,r)}static from(e){if(!e)return t.empty;if(e instanceof t)return e;if(Array.isArray(e))return this.fromArray(e);if(e.attrs)return new t([e],e.nodeSize);throw new RangeError("Can not convert "+e+" to a Fragment"+(e.nodesBetween?" (looks like multiple versions of prosemirror-model were loaded)":""))}};w.empty=new w([],0);var xi={index:0,offset:0};function Sr(t,e){return xi.index=t,xi.offset=e,xi}function Cr(t,e){if(t===e)return!0;if(!(t&&typeof t=="object")||!(e&&typeof e=="object"))return!1;let n=Array.isArray(t);if(Array.isArray(e)!=n)return!1;if(n){if(t.length!=e.length)return!1;for(let r=0;r<t.length;r++)if(!Cr(t[r],e[r]))return!1}else{for(let r in t)if(!(r in e)||!Cr(t[r],e[r]))return!1;for(let r in e)if(!(r in t))return!1}return!0}var V=class t{constructor(e,n){this.type=e,this.attrs=n}addToSet(e){let n,r=!1;for(let o=0;o<e.length;o++){let i=e[o];if(this.eq(i))return e;if(this.type.excludes(i.type))n||(n=e.slice(0,o));else{if(i.type.excludes(this.type))return e;!r&&i.type.rank>this.type.rank&&(n||(n=e.slice(0,o)),n.push(this),r=!0),n&&n.push(i)}}return n||(n=e.slice()),r||n.push(this),n}removeFromSet(e){for(let n=0;n<e.length;n++)if(this.eq(e[n]))return e.slice(0,n).concat(e.slice(n+1));return e}isInSet(e){for(let n=0;n<e.length;n++)if(this.eq(e[n]))return!0;return!1}eq(e){return this==e||this.type==e.type&&Cr(this.attrs,e.attrs)}toJSON(){let e={type:this.type.name};for(let n in this.attrs){e.attrs=this.attrs;break}return e}static fromJSON(e,n){if(!n)throw new RangeError("Invalid input for Mark.fromJSON");let r=e.marks[n.type];if(!r)throw new RangeError(`There is no mark type ${n.type} in this schema`);let o=r.create(n.attrs);return r.checkAttrs(o.attrs),o}static sameSet(e,n){if(e==n)return!0;if(e.length!=n.length)return!1;for(let r=0;r<e.length;r++)if(!e[r].eq(n[r]))return!1;return!0}static setFrom(e){if(!e||Array.isArray(e)&&e.length==0)return t.none;if(e instanceof t)return[e];let n=e.slice();return n.sort((r,o)=>r.type.rank-o.type.rank),n}};V.none=[];var Bt=class extends Error{},M=class t{constructor(e,n,r){this.content=e,this.openStart=n,this.openEnd=r}get size(){return this.content.size-this.openStart-this.openEnd}insertAt(e,n){let r=fa(this.content,e+this.openStart,n);return r&&new t(r,this.openStart,this.openEnd)}removeBetween(e,n){return new t(da(this.content,e+this.openStart,n+this.openStart),this.openStart,this.openEnd)}eq(e){return this.content.eq(e.content)&&this.openStart==e.openStart&&this.openEnd==e.openEnd}toString(){return this.content+"("+this.openStart+","+this.openEnd+")"}toJSON(){if(!this.content.size)return null;let e={content:this.content.toJSON()};return this.openStart>0&&(e.openStart=this.openStart),this.openEnd>0&&(e.openEnd=this.openEnd),e}static fromJSON(e,n){if(!n)return t.empty;let r=n.openStart||0,o=n.openEnd||0;if(typeof r!="number"||typeof o!="number")throw new RangeError("Invalid input for Slice.fromJSON");return new t(w.fromJSON(e,n.content),r,o)}static maxOpen(e,n=!0){let r=0,o=0;for(let i=e.firstChild;i&&!i.isLeaf&&(n||!i.type.spec.isolating);i=i.firstChild)r++;for(let i=e.lastChild;i&&!i.isLeaf&&(n||!i.type.spec.isolating);i=i.lastChild)o++;return new t(e,r,o)}};M.empty=new M(w.empty,0,0);function da(t,e,n){let{index:r,offset:o}=t.findIndex(e),i=t.maybeChild(r),{index:s,offset:l}=t.findIndex(n);if(o==e||i.isText){if(l!=n&&!t.child(s).isText)throw new RangeError("Removing non-flat range");return t.cut(0,e).append(t.cut(n))}if(r!=s)throw new RangeError("Removing non-flat range");return t.replaceChild(r,i.copy(da(i.content,e-o-1,n-o-1)))}function fa(t,e,n,r){let{index:o,offset:i}=t.findIndex(e),s=t.maybeChild(o);if(i==e||s.isText)return r&&!r.canReplace(o,o,n)?null:t.cut(0,e).append(n).append(t.cut(e));let l=fa(s.content,e-i-1,n);return l&&t.replaceChild(o,s.copy(l))}function Hh(t,e,n){if(n.openStart>t.depth)throw new Bt("Inserted content deeper than insertion position");if(t.depth-n.openStart!=e.depth-n.openEnd)throw new Bt("Inconsistent open depths");return ha(t,e,n,0)}function ha(t,e,n,r){let o=t.index(r),i=t.node(r);if(o==e.index(r)&&r<t.depth-n.openStart){let s=ha(t,e,n,r+1);return i.copy(i.content.replaceChild(o,s))}else if(n.content.size)if(!n.openStart&&!n.openEnd&&t.depth==r&&e.depth==r){let s=t.parent,l=s.content;return Lt(s,l.cut(0,t.parentOffset).append(n.content).append(l.cut(e.parentOffset)))}else{let{start:s,end:l}=Fh(n,t);return Lt(i,ma(t,s,l,e,r))}else return Lt(i,Mr(t,e,r))}function pa(t,e){if(!e.type.compatibleContent(t.type))throw new Bt("Cannot join "+e.type.name+" onto "+t.type.name)}function Si(t,e,n){let r=t.node(n);return pa(r,e.node(n)),r}function It(t,e){let n=e.length-1;n>=0&&t.isText&&t.sameMarkup(e[n])?e[n]=t.withText(e[n].text+t.text):e.push(t)}function Nn(t,e,n,r){let o=(e||t).node(n),i=0,s=e?e.index(n):o.childCount;t&&(i=t.index(n),t.depth>n?i++:t.textOffset&&(It(t.nodeAfter,r),i++));for(let l=i;l<s;l++)It(o.child(l),r);e&&e.depth==n&&e.textOffset&&It(e.nodeBefore,r)}function Lt(t,e){return t.type.checkContent(e),t.copy(e)}function ma(t,e,n,r,o){let i=t.depth>o&&Si(t,e,o+1),s=r.depth>o&&Si(n,r,o+1),l=[];return Nn(null,t,o,l),i&&s&&e.index(o)==n.index(o)?(pa(i,s),It(Lt(i,ma(t,e,n,r,o+1)),l)):(i&&It(Lt(i,Mr(t,e,o+1)),l),Nn(e,n,o,l),s&&It(Lt(s,Mr(n,r,o+1)),l)),Nn(r,null,o,l),new w(l)}function Mr(t,e,n){let r=[];if(Nn(null,t,n,r),t.depth>n){let o=Si(t,e,n+1);It(Lt(o,Mr(t,e,n+1)),r)}return Nn(e,null,n,r),new w(r)}function Fh(t,e){let n=e.depth-t.openStart,o=e.node(n).copy(t.content);for(let i=n-1;i>=0;i--)o=e.node(i).copy(w.from(o));return{start:o.resolveNoCache(t.openStart+n),end:o.resolveNoCache(o.content.size-t.openEnd-n)}}var vr=class t{constructor(e,n,r){this.pos=e,this.path=n,this.parentOffset=r,this.depth=n.length/3-1}resolveDepth(e){return e==null?this.depth:e<0?this.depth+e:e}get parent(){return this.node(this.depth)}get doc(){return this.node(0)}node(e){return this.path[this.resolveDepth(e)*3]}index(e){return this.path[this.resolveDepth(e)*3+1]}indexAfter(e){return e=this.resolveDepth(e),this.index(e)+(e==this.depth&&!this.textOffset?0:1)}start(e){return e=this.resolveDepth(e),e==0?0:this.path[e*3-1]+1}end(e){return e=this.resolveDepth(e),this.start(e)+this.node(e).content.size}before(e){if(e=this.resolveDepth(e),!e)throw new RangeError("There is no position before the top-level node");return e==this.depth+1?this.pos:this.path[e*3-1]}after(e){if(e=this.resolveDepth(e),!e)throw new RangeError("There is no position after the top-level node");return e==this.depth+1?this.pos:this.path[e*3-1]+this.path[e*3].nodeSize}get textOffset(){return this.pos-this.path[this.path.length-1]}get nodeAfter(){let e=this.parent,n=this.index(this.depth);if(n==e.childCount)return null;let r=this.pos-this.path[this.path.length-1],o=e.child(n);return r?e.child(n).cut(r):o}get nodeBefore(){let e=this.index(this.depth),n=this.pos-this.path[this.path.length-1];return n?this.parent.child(e).cut(0,n):e==0?null:this.parent.child(e-1)}posAtIndex(e,n){n=this.resolveDepth(n);let r=this.path[n*3],o=n==0?0:this.path[n*3-1]+1;for(let i=0;i<e;i++)o+=r.child(i).nodeSize;return o}marks(){let e=this.parent,n=this.index();if(e.content.size==0)return V.none;if(this.textOffset)return e.child(n).marks;let r=e.maybeChild(n-1),o=e.maybeChild(n);if(!r){let l=r;r=o,o=l}let i=r.marks;for(var s=0;s<i.length;s++)i[s].type.spec.inclusive===!1&&(!o||!i[s].isInSet(o.marks))&&(i=i[s--].removeFromSet(i));return i}marksAcross(e){let n=this.parent.maybeChild(this.index());if(!n||!n.isInline)return null;let r=n.marks,o=e.parent.maybeChild(e.index());for(var i=0;i<r.length;i++)r[i].type.spec.inclusive===!1&&(!o||!r[i].isInSet(o.marks))&&(r=r[i--].removeFromSet(r));return r}sharedDepth(e){for(let n=this.depth;n>0;n--)if(this.start(n)<=e&&this.end(n)>=e)return n;return 0}blockRange(e=this,n){if(e.pos<this.pos)return e.blockRange(this);for(let r=this.depth-(this.parent.inlineContent||this.pos==e.pos?1:0);r>=0;r--)if(e.pos<=this.end(r)&&(!n||n(this.node(r))))return new zt(this,e,r);return null}sameParent(e){return this.pos-this.parentOffset==e.pos-e.parentOffset}max(e){return e.pos>this.pos?e:this}min(e){return e.pos<this.pos?e:this}toString(){let e="";for(let n=1;n<=this.depth;n++)e+=(e?"/":"")+this.node(n).type.name+"_"+this.index(n-1);return e+":"+this.parentOffset}static resolve(e,n){if(!(n>=0&&n<=e.content.size))throw new RangeError("Position "+n+" out of range");let r=[],o=0,i=n;for(let s=e;;){let{index:l,offset:a}=s.content.findIndex(i),c=i-a;if(r.push(s,l,o+a),!c||(s=s.child(l),s.isText))break;i=c-1,o+=a+1}return new t(n,r,i)}static resolveCached(e,n){let r=ea.get(e);if(r)for(let i=0;i<r.elts.length;i++){let s=r.elts[i];if(s.pos==n)return s}else ea.set(e,r=new ki);let o=r.elts[r.i]=t.resolve(e,n);return r.i=(r.i+1)%$h,o}},ki=class{constructor(){this.elts=[],this.i=0}},$h=12,ea=new WeakMap,zt=class{constructor(e,n,r){this.$from=e,this.$to=n,this.depth=r}get start(){return this.$from.before(this.depth+1)}get end(){return this.$to.after(this.depth+1)}get parent(){return this.$from.node(this.depth)}get startIndex(){return this.$from.index(this.depth)}get endIndex(){return this.$to.indexAfter(this.depth)}},Vh=Object.create(null),le=class t{constructor(e,n,r,o=V.none){this.type=e,this.attrs=n,this.marks=o,this.content=r||w.empty}get children(){return this.content.content}get nodeSize(){return this.isLeaf?1:2+this.content.size}get childCount(){return this.content.childCount}child(e){return this.content.child(e)}maybeChild(e){return this.content.maybeChild(e)}forEach(e){this.content.forEach(e)}nodesBetween(e,n,r,o=0){this.content.nodesBetween(e,n,r,o,this)}descendants(e){this.nodesBetween(0,this.content.size,e)}get textContent(){return this.isLeaf&&this.type.spec.leafText?this.type.spec.leafText(this):this.textBetween(0,this.content.size,"")}textBetween(e,n,r,o){return this.content.textBetween(e,n,r,o)}get firstChild(){return this.content.firstChild}get lastChild(){return this.content.lastChild}eq(e){return this==e||this.sameMarkup(e)&&this.content.eq(e.content)}sameMarkup(e){return this.hasMarkup(e.type,e.attrs,e.marks)}hasMarkup(e,n,r){return this.type==e&&Cr(this.attrs,n||e.defaultAttrs||Vh)&&V.sameSet(this.marks,r||V.none)}copy(e=null){return e==this.content?this:new t(this.type,this.attrs,e,this.marks)}mark(e){return e==this.marks?this:new t(this.type,this.attrs,this.content,e)}cut(e,n=this.content.size){return e==0&&n==this.content.size?this:this.copy(this.content.cut(e,n))}slice(e,n=this.content.size,r=!1){if(e==n)return M.empty;let o=this.resolve(e),i=this.resolve(n),s=r?0:o.sharedDepth(n),l=o.start(s),c=o.node(s).content.cut(o.pos-l,i.pos-l);return new M(c,o.depth-s,i.depth-s)}replace(e,n,r){return Hh(this.resolve(e),this.resolve(n),r)}nodeAt(e){for(let n=this;;){let{index:r,offset:o}=n.content.findIndex(e);if(n=n.maybeChild(r),!n)return null;if(o==e||n.isText)return n;e-=o+1}}childAfter(e){let{index:n,offset:r}=this.content.findIndex(e);return{node:this.content.maybeChild(n),index:n,offset:r}}childBefore(e){if(e==0)return{node:null,index:0,offset:0};let{index:n,offset:r}=this.content.findIndex(e);if(r<e)return{node:this.content.child(n),index:n,offset:r};let o=this.content.child(n-1);return{node:o,index:n-1,offset:r-o.nodeSize}}resolve(e){return vr.resolveCached(this,e)}resolveNoCache(e){return vr.resolve(this,e)}rangeHasMark(e,n,r){let o=!1;return n>e&&this.nodesBetween(e,n,i=>(r.isInSet(i.marks)&&(o=!0),!o)),o}get isBlock(){return this.type.isBlock}get isTextblock(){return this.type.isTextblock}get inlineContent(){return this.type.inlineContent}get isInline(){return this.type.isInline}get isText(){return this.type.isText}get isLeaf(){return this.type.isLeaf}get isAtom(){return this.type.isAtom}toString(){if(this.type.spec.toDebugString)return this.type.spec.toDebugString(this);let e=this.type.name;return this.content.size&&(e+="("+this.content.toStringInner()+")"),ga(this.marks,e)}contentMatchAt(e){let n=this.type.contentMatch.matchFragment(this.content,0,e);if(!n)throw new Error("Called contentMatchAt on a node with invalid content");return n}canReplace(e,n,r=w.empty,o=0,i=r.childCount){let s=this.contentMatchAt(e).matchFragment(r,o,i),l=s&&s.matchFragment(this.content,n);if(!l||!l.validEnd)return!1;for(let a=o;a<i;a++)if(!this.type.allowsMarks(r.child(a).marks))return!1;return!0}canReplaceWith(e,n,r,o){if(o&&!this.type.allowsMarks(o))return!1;let i=this.contentMatchAt(e).matchType(r),s=i&&i.matchFragment(this.content,n);return s?s.validEnd:!1}canAppend(e){return e.content.size?this.canReplace(this.childCount,this.childCount,e.content):this.type.compatibleContent(e.type)}check(){this.type.checkContent(this.content),this.type.checkAttrs(this.attrs);let e=V.none;for(let n=0;n<this.marks.length;n++){let r=this.marks[n];r.type.checkAttrs(r.attrs),e=r.addToSet(e)}if(!V.sameSet(e,this.marks))throw new RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map(n=>n.type.name)}`);this.content.forEach(n=>n.check())}toJSON(){let e={type:this.type.name};for(let n in this.attrs){e.attrs=this.attrs;break}return this.content.size&&(e.content=this.content.toJSON()),this.marks.length&&(e.marks=this.marks.map(n=>n.toJSON())),e}static fromJSON(e,n){if(!n)throw new RangeError("Invalid input for Node.fromJSON");let r;if(n.marks){if(!Array.isArray(n.marks))throw new RangeError("Invalid mark data for Node.fromJSON");r=n.marks.map(e.markFromJSON)}if(n.type=="text"){if(typeof n.text!="string")throw new RangeError("Invalid text node in JSON");return e.text(n.text,r)}let o=w.fromJSON(e,n.content),i=e.nodeType(n.type).create(n.attrs,o,r);return i.type.checkAttrs(i.attrs),i}};le.prototype.text=void 0;var Ci=class t extends le{constructor(e,n,r,o){if(super(e,n,null,o),!r)throw new RangeError("Empty text nodes are not allowed");this.text=r}toString(){return this.type.spec.toDebugString?this.type.spec.toDebugString(this):ga(this.marks,JSON.stringify(this.text))}get textContent(){return this.text}textBetween(e,n){return this.text.slice(e,n)}get nodeSize(){return this.text.length}mark(e){return e==this.marks?this:new t(this.type,this.attrs,this.text,e)}withText(e){return e==this.text?this:new t(this.type,this.attrs,e,this.marks)}cut(e=0,n=this.text.length){return e==0&&n==this.text.length?this:this.withText(this.text.slice(e,n))}eq(e){return this.sameMarkup(e)&&this.text==e.text}toJSON(){let e=super.toJSON();return e.text=this.text,e}};function ga(t,e){for(let n=t.length-1;n>=0;n--)e=t[n].type.name+"("+e+")";return e}var Ht=class t{constructor(e){this.validEnd=e,this.next=[],this.wrapCache=[]}static parse(e,n){let r=new Mi(e,n);if(r.next==null)return t.empty;let o=ya(r);r.next&&r.err("Unexpected trailing text");let i=qh(Jh(o));return Gh(i,r),i}matchType(e){for(let n=0;n<this.next.length;n++)if(this.next[n].type==e)return this.next[n].next;return null}matchFragment(e,n=0,r=e.childCount){let o=this;for(let i=n;o&&i<r;i++)o=o.matchType(e.child(i).type);return o}get inlineContent(){return this.next.length!=0&&this.next[0].type.isInline}get defaultType(){for(let e=0;e<this.next.length;e++){let{type:n}=this.next[e];if(!(n.isText||n.hasRequiredAttrs()))return n}return null}compatible(e){for(let n=0;n<this.next.length;n++)for(let r=0;r<e.next.length;r++)if(this.next[n].type==e.next[r].type)return!0;return!1}fillBefore(e,n=!1,r=0){let o=[this];function i(s,l){let a=s.matchFragment(e,r);if(a&&(!n||a.validEnd))return w.from(l.map(c=>c.createAndFill()));for(let c=0;c<s.next.length;c++){let{type:u,next:d}=s.next[c];if(!(u.isText||u.hasRequiredAttrs())&&o.indexOf(d)==-1){o.push(d);let f=i(d,l.concat(u));if(f)return f}}return null}return i(this,[])}findWrapping(e){for(let r=0;r<this.wrapCache.length;r+=2)if(this.wrapCache[r]==e)return this.wrapCache[r+1];let n=this.computeWrapping(e);return this.wrapCache.push(e,n),n}computeWrapping(e){let n=Object.create(null),r=[{match:this,type:null,via:null}];for(;r.length;){let o=r.shift(),i=o.match;if(i.matchType(e)){let s=[];for(let l=o;l.type;l=l.via)s.push(l.type);return s.reverse()}for(let s=0;s<i.next.length;s++){let{type:l,next:a}=i.next[s];!l.isLeaf&&!l.hasRequiredAttrs()&&!(l.name in n)&&(!o.type||a.validEnd)&&(r.push({match:l.contentMatch,type:l,via:o}),n[l.name]=!0)}}return null}get edgeCount(){return this.next.length}edge(e){if(e>=this.next.length)throw new RangeError(`There's no ${e}th edge in this content match`);return this.next[e]}toString(){let e=[];function n(r){e.push(r);for(let o=0;o<r.next.length;o++)e.indexOf(r.next[o].next)==-1&&n(r.next[o].next)}return n(this),e.map((r,o)=>{let i=o+(r.validEnd?"*":" ")+" ";for(let s=0;s<r.next.length;s++)i+=(s?", ":"")+r.next[s].type.name+"->"+e.indexOf(r.next[s].next);return i}).join(`
`)}};Ht.empty=new Ht(!0);var Mi=class{constructor(e,n){this.string=e,this.nodeTypes=n,this.inline=null,this.pos=0,this.tokens=e.split(/\s*(?=\b|\W|$)/),this.tokens[this.tokens.length-1]==""&&this.tokens.pop(),this.tokens[0]==""&&this.tokens.shift()}get next(){return this.tokens[this.pos]}eat(e){return this.next==e&&(this.pos++||!0)}err(e){throw new SyntaxError(e+" (in content expression '"+this.string+"')")}};function ya(t){let e=[];do e.push(Wh(t));while(t.eat("|"));return e.length==1?e[0]:{type:"choice",exprs:e}}function Wh(t){let e=[];do e.push(_h(t));while(t.next&&t.next!=")"&&t.next!="|");return e.length==1?e[0]:{type:"seq",exprs:e}}function _h(t){let e=Uh(t);for(;;)if(t.eat("+"))e={type:"plus",expr:e};else if(t.eat("*"))e={type:"star",expr:e};else if(t.eat("?"))e={type:"opt",expr:e};else if(t.eat("{"))e=jh(t,e);else break;return e}function ta(t){/\D/.test(t.next)&&t.err("Expected number, got '"+t.next+"'");let e=Number(t.next);return t.pos++,e}function jh(t,e){let n=ta(t),r=n;return t.eat(",")&&(t.next!="}"?r=ta(t):r=-1),t.eat("}")||t.err("Unclosed braced range"),{type:"range",min:n,max:r,expr:e}}function Kh(t,e){let n=t.nodeTypes,r=n[e];if(r)return[r];let o=[];for(let i in n){let s=n[i];s.isInGroup(e)&&o.push(s)}return o.length==0&&t.err("No node type or group '"+e+"' found"),o}function Uh(t){if(t.eat("(")){let e=ya(t);return t.eat(")")||t.err("Missing closing paren"),e}else if(/\W/.test(t.next))t.err("Unexpected token '"+t.next+"'");else{let e=Kh(t,t.next).map(n=>(t.inline==null?t.inline=n.isInline:t.inline!=n.isInline&&t.err("Mixing inline and block content"),{type:"name",value:n}));return t.pos++,e.length==1?e[0]:{type:"choice",exprs:e}}}function Jh(t){let e=[[]];return o(i(t,0),n()),e;function n(){return e.push([])-1}function r(s,l,a){let c={term:a,to:l};return e[s].push(c),c}function o(s,l){s.forEach(a=>a.to=l)}function i(s,l){if(s.type=="choice")return s.exprs.reduce((a,c)=>a.concat(i(c,l)),[]);if(s.type=="seq")for(let a=0;;a++){let c=i(s.exprs[a],l);if(a==s.exprs.length-1)return c;o(c,l=n())}else if(s.type=="star"){let a=n();return r(l,a),o(i(s.expr,a),a),[r(a)]}else if(s.type=="plus"){let a=n();return o(i(s.expr,l),a),o(i(s.expr,a),a),[r(a)]}else{if(s.type=="opt")return[r(l)].concat(i(s.expr,l));if(s.type=="range"){let a=l;for(let c=0;c<s.min;c++){let u=n();o(i(s.expr,a),u),a=u}if(s.max==-1)o(i(s.expr,a),a);else for(let c=s.min;c<s.max;c++){let u=n();r(a,u),o(i(s.expr,a),u),a=u}return[r(a)]}else{if(s.type=="name")return[r(l,void 0,s.value)];throw new Error("Unknown expr type")}}}}function ba(t,e){return e-t}function na(t,e){let n=[];return r(e),n.sort(ba);function r(o){let i=t[o];if(i.length==1&&!i[0].term)return r(i[0].to);n.push(o);for(let s=0;s<i.length;s++){let{term:l,to:a}=i[s];!l&&n.indexOf(a)==-1&&r(a)}}}function qh(t){let e=Object.create(null);return n(na(t,0));function n(r){let o=[];r.forEach(s=>{t[s].forEach(({term:l,to:a})=>{if(!l)return;let c;for(let u=0;u<o.length;u++)o[u][0]==l&&(c=o[u][1]);na(t,a).forEach(u=>{c||o.push([l,c=[]]),c.indexOf(u)==-1&&c.push(u)})})});let i=e[r.join(",")]=new Ht(r.indexOf(t.length-1)>-1);for(let s=0;s<o.length;s++){let l=o[s][1].sort(ba);i.next.push({type:o[s][0],next:e[l.join(",")]||n(l)})}return i}}function Gh(t,e){for(let n=0,r=[t];n<r.length;n++){let o=r[n],i=!o.validEnd,s=[];for(let l=0;l<o.next.length;l++){let{type:a,next:c}=o.next[l];s.push(a.name),i&&!(a.isText||a.hasRequiredAttrs())&&(i=!1),r.indexOf(c)==-1&&r.push(c)}i&&e.err("Only non-generatable nodes ("+s.join(", ")+") in a required position (see https://prosemirror.net/docs/guide/#generatable)")}}function xa(t){let e=Object.create(null);for(let n in t){let r=t[n];if(!r.hasDefault)return null;e[n]=r.default}return e}function wa(t,e){let n=Object.create(null);for(let r in t){let o=e&&e[r];if(o===void 0){let i=t[r];if(i.hasDefault)o=i.default;else throw new RangeError("No value supplied for attribute "+r)}n[r]=o}return n}function Sa(t,e,n,r){for(let o in e)if(!(o in t))throw new RangeError(`Unsupported attribute ${o} for ${n} of type ${o}`);for(let o in t){let i=t[o];i.validate&&i.validate(e[o])}}function ka(t,e){let n=Object.create(null);if(e)for(let r in e)n[r]=new vi(t,r,e[r]);return n}var Tr=class t{constructor(e,n,r){this.name=e,this.schema=n,this.spec=r,this.markSet=null,this.groups=r.group?r.group.split(" "):[],this.attrs=ka(e,r.attrs),this.defaultAttrs=xa(this.attrs),this.contentMatch=null,this.inlineContent=null,this.isBlock=!(r.inline||e=="text"),this.isText=e=="text"}get isInline(){return!this.isBlock}get isTextblock(){return this.isBlock&&this.inlineContent}get isLeaf(){return this.contentMatch==Ht.empty}get isAtom(){return this.isLeaf||!!this.spec.atom}isInGroup(e){return this.groups.indexOf(e)>-1}get whitespace(){return this.spec.whitespace||(this.spec.code?"pre":"normal")}hasRequiredAttrs(){for(let e in this.attrs)if(this.attrs[e].isRequired)return!0;return!1}compatibleContent(e){return this==e||this.contentMatch.compatible(e.contentMatch)}computeAttrs(e){return!e&&this.defaultAttrs?this.defaultAttrs:wa(this.attrs,e)}create(e=null,n,r){if(this.isText)throw new Error("NodeType.create can't construct text nodes");return new le(this,this.computeAttrs(e),w.from(n),V.setFrom(r))}createChecked(e=null,n,r){return n=w.from(n),this.checkContent(n),new le(this,this.computeAttrs(e),n,V.setFrom(r))}createAndFill(e=null,n,r){if(e=this.computeAttrs(e),n=w.from(n),n.size){let s=this.contentMatch.fillBefore(n);if(!s)return null;n=s.append(n)}let o=this.contentMatch.matchFragment(n),i=o&&o.fillBefore(w.empty,!0);return i?new le(this,e,n.append(i),V.setFrom(r)):null}validContent(e){let n=this.contentMatch.matchFragment(e);if(!n||!n.validEnd)return!1;for(let r=0;r<e.childCount;r++)if(!this.allowsMarks(e.child(r).marks))return!1;return!0}checkContent(e){if(!this.validContent(e))throw new RangeError(`Invalid content for node ${this.name}: ${e.toString().slice(0,50)}`)}checkAttrs(e){Sa(this.attrs,e,"node",this.name)}allowsMarkType(e){return this.markSet==null||this.markSet.indexOf(e)>-1}allowsMarks(e){if(this.markSet==null)return!0;for(let n=0;n<e.length;n++)if(!this.allowsMarkType(e[n].type))return!1;return!0}allowedMarks(e){if(this.markSet==null)return e;let n;for(let r=0;r<e.length;r++)this.allowsMarkType(e[r].type)?n&&n.push(e[r]):n||(n=e.slice(0,r));return n?n.length?n:V.none:e}static compile(e,n){let r=Object.create(null);e.forEach((i,s)=>r[i]=new t(i,n,s));let o=n.spec.topNode||"doc";if(!r[o])throw new RangeError("Schema is missing its top node type ('"+o+"')");if(!r.text)throw new RangeError("Every schema needs a 'text' type");for(let i in r.text.attrs)throw new RangeError("The text node type should not have attributes");return r}};function Yh(t,e,n){let r=n.split("|");return o=>{let i=o===null?"null":typeof o;if(r.indexOf(i)<0)throw new RangeError(`Expected value of type ${r} for attribute ${e} on type ${t}, got ${i}`)}}var vi=class{constructor(e,n,r){this.hasDefault=Object.prototype.hasOwnProperty.call(r,"default"),this.default=r.default,this.validate=typeof r.validate=="string"?Yh(e,n,r.validate):r.validate}get isRequired(){return!this.hasDefault}},Dn=class t{constructor(e,n,r,o){this.name=e,this.rank=n,this.schema=r,this.spec=o,this.attrs=ka(e,o.attrs),this.excluded=null;let i=xa(this.attrs);this.instance=i?new V(this,i):null}create(e=null){return!e&&this.instance?this.instance:new V(this,wa(this.attrs,e))}static compile(e,n){let r=Object.create(null),o=0;return e.forEach((i,s)=>r[i]=new t(i,o++,n,s)),r}removeFromSet(e){for(var n=0;n<e.length;n++)e[n].type==this&&(e=e.slice(0,n).concat(e.slice(n+1)),n--);return e}isInSet(e){for(let n=0;n<e.length;n++)if(e[n].type==this)return e[n]}checkAttrs(e){Sa(this.attrs,e,"mark",this.name)}excludes(e){return this.excluded.indexOf(e)>-1}},sn=class{constructor(e){this.linebreakReplacement=null,this.cached=Object.create(null);let n=this.spec={};for(let o in e)n[o]=e[o];n.nodes=bi.from(e.nodes),n.marks=bi.from(e.marks||{}),this.nodes=Tr.compile(this.spec.nodes,this),this.marks=Dn.compile(this.spec.marks,this);let r=Object.create(null);for(let o in this.nodes){if(o in this.marks)throw new RangeError(o+" can not be both a node and a mark");let i=this.nodes[o],s=i.spec.content||"",l=i.spec.marks;if(i.contentMatch=r[s]||(r[s]=Ht.parse(s,this.nodes)),i.inlineContent=i.contentMatch.inlineContent,i.spec.linebreakReplacement){if(this.linebreakReplacement)throw new RangeError("Multiple linebreak nodes defined");if(!i.isInline||!i.isLeaf)throw new RangeError("Linebreak replacement nodes must be inline leaf nodes");this.linebreakReplacement=i}i.markSet=l=="_"?null:l?ra(this,l.split(" ")):l==""||!i.inlineContent?[]:null}for(let o in this.marks){let i=this.marks[o],s=i.spec.excludes;i.excluded=s==null?[i]:s==""?[]:ra(this,s.split(" "))}this.nodeFromJSON=o=>le.fromJSON(this,o),this.markFromJSON=o=>V.fromJSON(this,o),this.topNodeType=this.nodes[this.spec.topNode||"doc"],this.cached.wrappings=Object.create(null)}node(e,n=null,r,o){if(typeof e=="string")e=this.nodeType(e);else if(e instanceof Tr){if(e.schema!=this)throw new RangeError("Node type from different schema used ("+e.name+")")}else throw new RangeError("Invalid node type: "+e);return e.createChecked(n,r,o)}text(e,n){let r=this.nodes.text;return new Ci(r,r.defaultAttrs,e,V.setFrom(n))}mark(e,n){return typeof e=="string"&&(e=this.marks[e]),e.create(n)}nodeType(e){let n=this.nodes[e];if(!n)throw new RangeError("Unknown node type: "+e);return n}};function ra(t,e){let n=[];for(let r=0;r<e.length;r++){let o=e[r],i=t.marks[o],s=i;if(i)n.push(i);else for(let l in t.marks){let a=t.marks[l];(o=="_"||a.spec.group&&a.spec.group.split(" ").indexOf(o)>-1)&&n.push(s=a)}if(!s)throw new SyntaxError("Unknown mark type: '"+e[r]+"'")}return n}function Xh(t){return t.tag!=null}function Qh(t){return t.style!=null}var Ue=class t{constructor(e,n){this.schema=e,this.rules=n,this.tags=[],this.styles=[];let r=this.matchedStyles=[];n.forEach(o=>{if(Xh(o))this.tags.push(o);else if(Qh(o)){let i=/[^=]*/.exec(o.style)[0];r.indexOf(i)<0&&r.push(i),this.styles.push(o)}}),this.normalizeLists=!this.tags.some(o=>{if(!/^(ul|ol)\b/.test(o.tag)||!o.node)return!1;let i=e.nodes[o.node];return i.contentMatch.matchType(i)})}parse(e,n={}){let r=new Ar(this,n,!1);return r.addAll(e,V.none,n.from,n.to),r.finish()}parseSlice(e,n={}){let r=new Ar(this,n,!0);return r.addAll(e,V.none,n.from,n.to),M.maxOpen(r.finish())}matchTag(e,n,r){for(let o=r?this.tags.indexOf(r)+1:0;o<this.tags.length;o++){let i=this.tags[o];if(tp(e,i.tag)&&(i.namespace===void 0||e.namespaceURI==i.namespace)&&(!i.context||n.matchesContext(i.context))){if(i.getAttrs){let s=i.getAttrs(e);if(s===!1)continue;i.attrs=s||void 0}return i}}}matchStyle(e,n,r,o){for(let i=o?this.styles.indexOf(o)+1:0;i<this.styles.length;i++){let s=this.styles[i],l=s.style;if(!(l.indexOf(e)!=0||s.context&&!r.matchesContext(s.context)||l.length>e.length&&(l.charCodeAt(e.length)!=61||l.slice(e.length+1)!=n))){if(s.getAttrs){let a=s.getAttrs(n);if(a===!1)continue;s.attrs=a||void 0}return s}}}static schemaRules(e){let n=[];function r(o){let i=o.priority==null?50:o.priority,s=0;for(;s<n.length;s++){let l=n[s];if((l.priority==null?50:l.priority)<i)break}n.splice(s,0,o)}for(let o in e.marks){let i=e.marks[o].spec.parseDOM;i&&i.forEach(s=>{r(s=ia(s)),s.mark||s.ignore||s.clearMark||(s.mark=o)})}for(let o in e.nodes){let i=e.nodes[o].spec.parseDOM;i&&i.forEach(s=>{r(s=ia(s)),s.node||s.ignore||s.mark||(s.node=o)})}return n}static fromSchema(e){return e.cached.domParser||(e.cached.domParser=new t(e,t.schemaRules(e)))}},Ca={address:!0,article:!0,aside:!0,blockquote:!0,canvas:!0,dd:!0,div:!0,dl:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,li:!0,noscript:!0,ol:!0,output:!0,p:!0,pre:!0,section:!0,table:!0,tfoot:!0,ul:!0},Zh={head:!0,noscript:!0,object:!0,script:!0,style:!0,title:!0},Ma={ol:!0,ul:!0},Pn=1,Ti=2,Rn=4;function oa(t,e,n){return e!=null?(e?Pn:0)|(e==="full"?Ti:0):t&&t.whitespace=="pre"?Pn|Ti:n&~Rn}var on=class{constructor(e,n,r,o,i,s){this.type=e,this.attrs=n,this.marks=r,this.solid=o,this.options=s,this.content=[],this.activeMarks=V.none,this.match=i||(s&Rn?null:e.contentMatch)}findWrapping(e){if(!this.match){if(!this.type)return[];let n=this.type.contentMatch.fillBefore(w.from(e));if(n)this.match=this.type.contentMatch.matchFragment(n);else{let r=this.type.contentMatch,o;return(o=r.findWrapping(e.type))?(this.match=r,o):null}}return this.match.findWrapping(e.type)}finish(e){if(!(this.options&Pn)){let r=this.content[this.content.length-1],o;if(r&&r.isText&&(o=/[ \t\r\n\u000c]+$/.exec(r.text))){let i=r;r.text.length==o[0].length?this.content.pop():this.content[this.content.length-1]=i.withText(i.text.slice(0,i.text.length-o[0].length))}}let n=w.from(this.content);return!e&&this.match&&(n=n.append(this.match.fillBefore(w.empty,!0))),this.type?this.type.create(this.attrs,n,this.marks):n}inlineContext(e){return this.type?this.type.inlineContent:this.content.length?this.content[0].isInline:e.parentNode&&!Ca.hasOwnProperty(e.parentNode.nodeName.toLowerCase())}},Ar=class{constructor(e,n,r){this.parser=e,this.options=n,this.isOpen=r,this.open=0,this.localPreserveWS=!1;let o=n.topNode,i,s=oa(null,n.preserveWhitespace,0)|(r?Rn:0);o?i=new on(o.type,o.attrs,V.none,!0,n.topMatch||o.type.contentMatch,s):r?i=new on(null,null,V.none,!0,null,s):i=new on(e.schema.topNodeType,null,V.none,!0,null,s),this.nodes=[i],this.find=n.findPositions,this.needsBlock=!1}get top(){return this.nodes[this.open]}addDOM(e,n){e.nodeType==3?this.addTextNode(e,n):e.nodeType==1&&this.addElement(e,n)}addTextNode(e,n){let r=e.nodeValue,o=this.top,i=o.options&Ti?"full":this.localPreserveWS||(o.options&Pn)>0;if(i==="full"||o.inlineContext(e)||/[^ \t\r\n\u000c]/.test(r)){if(i)i!=="full"?r=r.replace(/\r?\n|\r/g," "):r=r.replace(/\r\n?/g,`
`);else if(r=r.replace(/[ \t\r\n\u000c]+/g," "),/^[ \t\r\n\u000c]/.test(r)&&this.open==this.nodes.length-1){let s=o.content[o.content.length-1],l=e.previousSibling;(!s||l&&l.nodeName=="BR"||s.isText&&/[ \t\r\n\u000c]$/.test(s.text))&&(r=r.slice(1))}r&&this.insertNode(this.parser.schema.text(r),n,!/\S/.test(r)),this.findInText(e)}else this.findInside(e)}addElement(e,n,r){let o=this.localPreserveWS,i=this.top;(e.tagName=="PRE"||/pre/.test(e.style&&e.style.whiteSpace))&&(this.localPreserveWS=!0);let s=e.nodeName.toLowerCase(),l;Ma.hasOwnProperty(s)&&this.parser.normalizeLists&&ep(e);let a=this.options.ruleFromNode&&this.options.ruleFromNode(e)||(l=this.parser.matchTag(e,this,r));e:if(a?a.ignore:Zh.hasOwnProperty(s))this.findInside(e),this.ignoreFallback(e,n);else if(!a||a.skip||a.closeParent){a&&a.closeParent?this.open=Math.max(0,this.open-1):a&&a.skip.nodeType&&(e=a.skip);let c,u=this.needsBlock;if(Ca.hasOwnProperty(s))i.content.length&&i.content[0].isInline&&this.open&&(this.open--,i=this.top),c=!0,i.type||(this.needsBlock=!0);else if(!e.firstChild){this.leafFallback(e,n);break e}let d=a&&a.skip?n:this.readStyles(e,n);d&&this.addAll(e,d),c&&this.sync(i),this.needsBlock=u}else{let c=this.readStyles(e,n);c&&this.addElementByRule(e,a,c,a.consuming===!1?l:void 0)}this.localPreserveWS=o}leafFallback(e,n){e.nodeName=="BR"&&this.top.type&&this.top.type.inlineContent&&this.addTextNode(e.ownerDocument.createTextNode(`
`),n)}ignoreFallback(e,n){e.nodeName=="BR"&&(!this.top.type||!this.top.type.inlineContent)&&this.findPlace(this.parser.schema.text("-"),n,!0)}readStyles(e,n){let r=e.style;if(r&&r.length)for(let o=0;o<this.parser.matchedStyles.length;o++){let i=this.parser.matchedStyles[o],s=r.getPropertyValue(i);if(s)for(let l=void 0;;){let a=this.parser.matchStyle(i,s,this,l);if(!a)break;if(a.ignore)return null;if(a.clearMark?n=n.filter(c=>!a.clearMark(c)):n=n.concat(this.parser.schema.marks[a.mark].create(a.attrs)),a.consuming===!1)l=a;else break}}return n}addElementByRule(e,n,r,o){let i,s;if(n.node)if(s=this.parser.schema.nodes[n.node],s.isLeaf)this.insertNode(s.create(n.attrs),r,e.nodeName=="BR")||this.leafFallback(e,r);else{let a=this.enter(s,n.attrs||null,r,n.preserveWhitespace);a&&(i=!0,r=a)}else{let a=this.parser.schema.marks[n.mark];r=r.concat(a.create(n.attrs))}let l=this.top;if(s&&s.isLeaf)this.findInside(e);else if(o)this.addElement(e,r,o);else if(n.getContent)this.findInside(e),n.getContent(e,this.parser.schema).forEach(a=>this.insertNode(a,r,!1));else{let a=e;typeof n.contentElement=="string"?a=e.querySelector(n.contentElement):typeof n.contentElement=="function"?a=n.contentElement(e):n.contentElement&&(a=n.contentElement),this.findAround(e,a,!0),this.addAll(a,r),this.findAround(e,a,!1)}i&&this.sync(l)&&this.open--}addAll(e,n,r,o){let i=r||0;for(let s=r?e.childNodes[r]:e.firstChild,l=o==null?null:e.childNodes[o];s!=l;s=s.nextSibling,++i)this.findAtPoint(e,i),this.addDOM(s,n);this.findAtPoint(e,i)}findPlace(e,n,r){let o,i;for(let s=this.open,l=0;s>=0;s--){let a=this.nodes[s],c=a.findWrapping(e);if(c&&(!o||o.length>c.length+l)&&(o=c,i=a,!c.length))break;if(a.solid){if(r)break;l+=2}}if(!o)return null;this.sync(i);for(let s=0;s<o.length;s++)n=this.enterInner(o[s],null,n,!1);return n}insertNode(e,n,r){if(e.isInline&&this.needsBlock&&!this.top.type){let i=this.textblockFromContext();i&&(n=this.enterInner(i,null,n))}let o=this.findPlace(e,n,r);if(o){this.closeExtra();let i=this.top;i.match&&(i.match=i.match.matchType(e.type));let s=V.none;for(let l of o.concat(e.marks))(i.type?i.type.allowsMarkType(l.type):sa(l.type,e.type))&&(s=l.addToSet(s));return i.content.push(e.mark(s)),!0}return!1}enter(e,n,r,o){let i=this.findPlace(e.create(n),r,!1);return i&&(i=this.enterInner(e,n,r,!0,o)),i}enterInner(e,n,r,o=!1,i){this.closeExtra();let s=this.top;s.match=s.match&&s.match.matchType(e);let l=oa(e,i,s.options);s.options&Rn&&s.content.length==0&&(l|=Rn);let a=V.none;return r=r.filter(c=>(s.type?s.type.allowsMarkType(c.type):sa(c.type,e))?(a=c.addToSet(a),!1):!0),this.nodes.push(new on(e,n,a,o,null,l)),this.open++,r}closeExtra(e=!1){let n=this.nodes.length-1;if(n>this.open){for(;n>this.open;n--)this.nodes[n-1].content.push(this.nodes[n].finish(e));this.nodes.length=this.open+1}}finish(){return this.open=0,this.closeExtra(this.isOpen),this.nodes[0].finish(!!(this.isOpen||this.options.topOpen))}sync(e){for(let n=this.open;n>=0;n--){if(this.nodes[n]==e)return this.open=n,!0;this.localPreserveWS&&(this.nodes[n].options|=Pn)}return!1}get currentPos(){this.closeExtra();let e=0;for(let n=this.open;n>=0;n--){let r=this.nodes[n].content;for(let o=r.length-1;o>=0;o--)e+=r[o].nodeSize;n&&e++}return e}findAtPoint(e,n){if(this.find)for(let r=0;r<this.find.length;r++)this.find[r].node==e&&this.find[r].offset==n&&(this.find[r].pos=this.currentPos)}findInside(e){if(this.find)for(let n=0;n<this.find.length;n++)this.find[n].pos==null&&e.nodeType==1&&e.contains(this.find[n].node)&&(this.find[n].pos=this.currentPos)}findAround(e,n,r){if(e!=n&&this.find)for(let o=0;o<this.find.length;o++)this.find[o].pos==null&&e.nodeType==1&&e.contains(this.find[o].node)&&n.compareDocumentPosition(this.find[o].node)&(r?2:4)&&(this.find[o].pos=this.currentPos)}findInText(e){if(this.find)for(let n=0;n<this.find.length;n++)this.find[n].node==e&&(this.find[n].pos=this.currentPos-(e.nodeValue.length-this.find[n].offset))}matchesContext(e){if(e.indexOf("|")>-1)return e.split(/\s*\|\s*/).some(this.matchesContext,this);let n=e.split("/"),r=this.options.context,o=!this.isOpen&&(!r||r.parent.type==this.nodes[0].type),i=-(r?r.depth+1:0)+(o?0:1),s=(l,a)=>{for(;l>=0;l--){let c=n[l];if(c==""){if(l==n.length-1||l==0)continue;for(;a>=i;a--)if(s(l-1,a))return!0;return!1}else{let u=a>0||a==0&&o?this.nodes[a].type:r&&a>=i?r.node(a-i).type:null;if(!u||u.name!=c&&!u.isInGroup(c))return!1;a--}}return!0};return s(n.length-1,this.open)}textblockFromContext(){let e=this.options.context;if(e)for(let n=e.depth;n>=0;n--){let r=e.node(n).contentMatchAt(e.indexAfter(n)).defaultType;if(r&&r.isTextblock&&r.defaultAttrs)return r}for(let n in this.parser.schema.nodes){let r=this.parser.schema.nodes[n];if(r.isTextblock&&r.defaultAttrs)return r}}};function ep(t){for(let e=t.firstChild,n=null;e;e=e.nextSibling){let r=e.nodeType==1?e.nodeName.toLowerCase():null;r&&Ma.hasOwnProperty(r)&&n?(n.appendChild(e),e=n):r=="li"?n=e:r&&(n=null)}}function tp(t,e){return(t.matches||t.msMatchesSelector||t.webkitMatchesSelector||t.mozMatchesSelector).call(t,e)}function ia(t){let e={};for(let n in t)e[n]=t[n];return e}function sa(t,e){let n=e.schema.nodes;for(let r in n){let o=n[r];if(!o.allowsMarkType(t))continue;let i=[],s=l=>{i.push(l);for(let a=0;a<l.edgeCount;a++){let{type:c,next:u}=l.edge(a);if(c==e||i.indexOf(u)<0&&s(u))return!0}};if(s(o.contentMatch))return!0}}var it=class t{constructor(e,n){this.nodes=e,this.marks=n}serializeFragment(e,n={},r){r||(r=wi(n).createDocumentFragment());let o=r,i=[];return e.forEach(s=>{if(i.length||s.marks.length){let l=0,a=0;for(;l<i.length&&a<s.marks.length;){let c=s.marks[a];if(!this.marks[c.type.name]){a++;continue}if(!c.eq(i[l][0])||c.type.spec.spanning===!1)break;l++,a++}for(;l<i.length;)o=i.pop()[1];for(;a<s.marks.length;){let c=s.marks[a++],u=this.serializeMark(c,s.isInline,n);u&&(i.push([c,o]),o.appendChild(u.dom),o=u.contentDOM||u.dom)}}o.appendChild(this.serializeNodeInner(s,n))}),r}serializeNodeInner(e,n){let{dom:r,contentDOM:o}=kr(wi(n),this.nodes[e.type.name](e),null,e.attrs);if(o){if(e.isLeaf)throw new RangeError("Content hole not allowed in a leaf node spec");this.serializeFragment(e.content,n,o)}return r}serializeNode(e,n={}){let r=this.serializeNodeInner(e,n);for(let o=e.marks.length-1;o>=0;o--){let i=this.serializeMark(e.marks[o],e.isInline,n);i&&((i.contentDOM||i.dom).appendChild(r),r=i.dom)}return r}serializeMark(e,n,r={}){let o=this.marks[e.type.name];return o&&kr(wi(r),o(e,n),null,e.attrs)}static renderSpec(e,n,r=null,o){return kr(e,n,r,o)}static fromSchema(e){return e.cached.domSerializer||(e.cached.domSerializer=new t(this.nodesFromSchema(e),this.marksFromSchema(e)))}static nodesFromSchema(e){let n=la(e.nodes);return n.text||(n.text=r=>r.text),n}static marksFromSchema(e){return la(e.marks)}};function la(t){let e={};for(let n in t){let r=t[n].spec.toDOM;r&&(e[n]=r)}return e}function wi(t){return t.document||window.document}var aa=new WeakMap;function np(t){let e=aa.get(t);return e===void 0&&aa.set(t,e=rp(t)),e}function rp(t){let e=null;function n(r){if(r&&typeof r=="object")if(Array.isArray(r))if(typeof r[0]=="string")e||(e=[]),e.push(r);else for(let o=0;o<r.length;o++)n(r[o]);else for(let o in r)n(r[o])}return n(t),e}function kr(t,e,n,r){if(typeof e=="string")return{dom:t.createTextNode(e)};if(e.nodeType!=null)return{dom:e};if(e.dom&&e.dom.nodeType!=null)return e;let o=e[0],i;if(typeof o!="string")throw new RangeError("Invalid array passed to renderSpec");if(r&&(i=np(r))&&i.indexOf(e)>-1)throw new RangeError("Using an array from an attribute object as a DOM spec. This may be an attempted cross site scripting attack.");let s=o.indexOf(" ");s>0&&(n=o.slice(0,s),o=o.slice(s+1));let l,a=n?t.createElementNS(n,o):t.createElement(o),c=e[1],u=1;if(c&&typeof c=="object"&&c.nodeType==null&&!Array.isArray(c)){u=2;for(let d in c)if(c[d]!=null){let f=d.indexOf(" ");f>0?a.setAttributeNS(d.slice(0,f),d.slice(f+1),c[d]):d=="style"&&a.style?a.style.cssText=c[d]:a.setAttribute(d,c[d])}}for(let d=u;d<e.length;d++){let f=e[d];if(f===0){if(d<e.length-1||d>u)throw new RangeError("Content hole must be the only child of its parent node");return{dom:a,contentDOM:a}}else{let{dom:h,contentDOM:p}=kr(t,f,n,r);if(a.appendChild(h),p){if(l)throw new RangeError("Multiple content holes");l=p}}}return{dom:a,contentDOM:l}}var Aa=65535,Ea=Math.pow(2,16);function op(t,e){return t+e*Ea}function va(t){return t&Aa}function ip(t){return(t-(t&Aa))/Ea}var Oa=1,Na=2,Er=4,Ra=8,Bn=class{constructor(e,n,r){this.pos=e,this.delInfo=n,this.recover=r}get deleted(){return(this.delInfo&Ra)>0}get deletedBefore(){return(this.delInfo&(Oa|Er))>0}get deletedAfter(){return(this.delInfo&(Na|Er))>0}get deletedAcross(){return(this.delInfo&Er)>0}},st=class t{constructor(e,n=!1){if(this.ranges=e,this.inverted=n,!e.length&&t.empty)return t.empty}recover(e){let n=0,r=va(e);if(!this.inverted)for(let o=0;o<r;o++)n+=this.ranges[o*3+2]-this.ranges[o*3+1];return this.ranges[r*3]+n+ip(e)}mapResult(e,n=1){return this._map(e,n,!1)}map(e,n=1){return this._map(e,n,!0)}_map(e,n,r){let o=0,i=this.inverted?2:1,s=this.inverted?1:2;for(let l=0;l<this.ranges.length;l+=3){let a=this.ranges[l]-(this.inverted?o:0);if(a>e)break;let c=this.ranges[l+i],u=this.ranges[l+s],d=a+c;if(e<=d){let f=c?e==a?-1:e==d?1:n:n,h=a+o+(f<0?0:u);if(r)return h;let p=e==(n<0?a:d)?null:op(l/3,e-a),m=e==a?Na:e==d?Oa:Er;return(n<0?e!=a:e!=d)&&(m|=Ra),new Bn(h,m,p)}o+=u-c}return r?e+o:new Bn(e+o,0,null)}touches(e,n){let r=0,o=va(n),i=this.inverted?2:1,s=this.inverted?1:2;for(let l=0;l<this.ranges.length;l+=3){let a=this.ranges[l]-(this.inverted?r:0);if(a>e)break;let c=this.ranges[l+i],u=a+c;if(e<=u&&l==o*3)return!0;r+=this.ranges[l+s]-c}return!1}forEach(e){let n=this.inverted?2:1,r=this.inverted?1:2;for(let o=0,i=0;o<this.ranges.length;o+=3){let s=this.ranges[o],l=s-(this.inverted?i:0),a=s+(this.inverted?0:i),c=this.ranges[o+n],u=this.ranges[o+r];e(l,l+c,a,a+u),i+=u-c}}invert(){return new t(this.ranges,!this.inverted)}toString(){return(this.inverted?"-":"")+JSON.stringify(this.ranges)}static offset(e){return e==0?t.empty:new t(e<0?[0,-e,0]:[0,0,e])}};st.empty=new st([]);var zn=class t{constructor(e,n,r=0,o=e?e.length:0){this.mirror=n,this.from=r,this.to=o,this._maps=e||[],this.ownData=!(e||n)}get maps(){return this._maps}slice(e=0,n=this.maps.length){return new t(this._maps,this.mirror,e,n)}appendMap(e,n){this.ownData||(this._maps=this._maps.slice(),this.mirror=this.mirror&&this.mirror.slice(),this.ownData=!0),this.to=this._maps.push(e),n!=null&&this.setMirror(this._maps.length-1,n)}appendMapping(e){for(let n=0,r=this._maps.length;n<e._maps.length;n++){let o=e.getMirror(n);this.appendMap(e._maps[n],o!=null&&o<n?r+o:void 0)}}getMirror(e){if(this.mirror){for(let n=0;n<this.mirror.length;n++)if(this.mirror[n]==e)return this.mirror[n+(n%2?-1:1)]}}setMirror(e,n){this.mirror||(this.mirror=[]),this.mirror.push(e,n)}appendMappingInverted(e){for(let n=e.maps.length-1,r=this._maps.length+e._maps.length;n>=0;n--){let o=e.getMirror(n);this.appendMap(e._maps[n].invert(),o!=null&&o>n?r-o-1:void 0)}}invert(){let e=new t;return e.appendMappingInverted(this),e}map(e,n=1){if(this.mirror)return this._map(e,n,!0);for(let r=this.from;r<this.to;r++)e=this._maps[r].map(e,n);return e}mapResult(e,n=1){return this._map(e,n,!1)}_map(e,n,r){let o=0;for(let i=this.from;i<this.to;i++){let s=this._maps[i],l=s.mapResult(e,n);if(l.recover!=null){let a=this.getMirror(i);if(a!=null&&a>i&&a<this.to){i=a,e=this._maps[a].recover(l.recover);continue}}o|=l.delInfo,e=l.pos}return r?e:new Bn(e,o,null)}},Ai=Object.create(null),ae=class{getMap(){return st.empty}merge(e){return null}static fromJSON(e,n){if(!n||!n.stepType)throw new RangeError("Invalid input for Step.fromJSON");let r=Ai[n.stepType];if(!r)throw new RangeError(`No step type ${n.stepType} defined`);return r.fromJSON(e,n)}static jsonID(e,n){if(e in Ai)throw new RangeError("Duplicate use of step JSON ID "+e);return Ai[e]=n,n.prototype.jsonID=e,n}},ce=class t{constructor(e,n){this.doc=e,this.failed=n}static ok(e){return new t(e,null)}static fail(e){return new t(null,e)}static fromReplace(e,n,r,o){try{return t.ok(e.replace(n,r,o))}catch(i){if(i instanceof Bt)return t.fail(i.message);throw i}}};function Di(t,e,n){let r=[];for(let o=0;o<t.childCount;o++){let i=t.child(o);i.content.size&&(i=i.copy(Di(i.content,e,i))),i.isInline&&(i=e(i,n,o)),r.push(i)}return w.fromArray(r)}var Hn=class t extends ae{constructor(e,n,r){super(),this.from=e,this.to=n,this.mark=r}apply(e){let n=e.slice(this.from,this.to),r=e.resolve(this.from),o=r.node(r.sharedDepth(this.to)),i=new M(Di(n.content,(s,l)=>!s.isAtom||!l.type.allowsMarkType(this.mark.type)?s:s.mark(this.mark.addToSet(s.marks)),o),n.openStart,n.openEnd);return ce.fromReplace(e,this.from,this.to,i)}invert(){return new lt(this.from,this.to,this.mark)}map(e){let n=e.mapResult(this.from,1),r=e.mapResult(this.to,-1);return n.deleted&&r.deleted||n.pos>=r.pos?null:new t(n.pos,r.pos,this.mark)}merge(e){return e instanceof t&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new t(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"addMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,n){if(typeof n.from!="number"||typeof n.to!="number")throw new RangeError("Invalid input for AddMarkStep.fromJSON");return new t(n.from,n.to,e.markFromJSON(n.mark))}};ae.jsonID("addMark",Hn);var lt=class t extends ae{constructor(e,n,r){super(),this.from=e,this.to=n,this.mark=r}apply(e){let n=e.slice(this.from,this.to),r=new M(Di(n.content,o=>o.mark(this.mark.removeFromSet(o.marks)),e),n.openStart,n.openEnd);return ce.fromReplace(e,this.from,this.to,r)}invert(){return new Hn(this.from,this.to,this.mark)}map(e){let n=e.mapResult(this.from,1),r=e.mapResult(this.to,-1);return n.deleted&&r.deleted||n.pos>=r.pos?null:new t(n.pos,r.pos,this.mark)}merge(e){return e instanceof t&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new t(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"removeMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,n){if(typeof n.from!="number"||typeof n.to!="number")throw new RangeError("Invalid input for RemoveMarkStep.fromJSON");return new t(n.from,n.to,e.markFromJSON(n.mark))}};ae.jsonID("removeMark",lt);var Fn=class t extends ae{constructor(e,n){super(),this.pos=e,this.mark=n}apply(e){let n=e.nodeAt(this.pos);if(!n)return ce.fail("No node at mark step's position");let r=n.type.create(n.attrs,null,this.mark.addToSet(n.marks));return ce.fromReplace(e,this.pos,this.pos+1,new M(w.from(r),0,n.isLeaf?0:1))}invert(e){let n=e.nodeAt(this.pos);if(n){let r=this.mark.addToSet(n.marks);if(r.length==n.marks.length){for(let o=0;o<n.marks.length;o++)if(!n.marks[o].isInSet(r))return new t(this.pos,n.marks[o]);return new t(this.pos,this.mark)}}return new ln(this.pos,this.mark)}map(e){let n=e.mapResult(this.pos,1);return n.deletedAfter?null:new t(n.pos,this.mark)}toJSON(){return{stepType:"addNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,n){if(typeof n.pos!="number")throw new RangeError("Invalid input for AddNodeMarkStep.fromJSON");return new t(n.pos,e.markFromJSON(n.mark))}};ae.jsonID("addNodeMark",Fn);var ln=class t extends ae{constructor(e,n){super(),this.pos=e,this.mark=n}apply(e){let n=e.nodeAt(this.pos);if(!n)return ce.fail("No node at mark step's position");let r=n.type.create(n.attrs,null,this.mark.removeFromSet(n.marks));return ce.fromReplace(e,this.pos,this.pos+1,new M(w.from(r),0,n.isLeaf?0:1))}invert(e){let n=e.nodeAt(this.pos);return!n||!this.mark.isInSet(n.marks)?this:new Fn(this.pos,this.mark)}map(e){let n=e.mapResult(this.pos,1);return n.deletedAfter?null:new t(n.pos,this.mark)}toJSON(){return{stepType:"removeNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,n){if(typeof n.pos!="number")throw new RangeError("Invalid input for RemoveNodeMarkStep.fromJSON");return new t(n.pos,e.markFromJSON(n.mark))}};ae.jsonID("removeNodeMark",ln);var pe=class t extends ae{constructor(e,n,r,o=!1){super(),this.from=e,this.to=n,this.slice=r,this.structure=o}apply(e){return this.structure&&Ni(e,this.from,this.to)?ce.fail("Structure replace would overwrite content"):ce.fromReplace(e,this.from,this.to,this.slice)}getMap(){return new st([this.from,this.to-this.from,this.slice.size])}invert(e){return new t(this.from,this.from+this.slice.size,e.slice(this.from,this.to))}map(e){let n=e.mapResult(this.from,1),r=e.mapResult(this.to,-1);return n.deletedAcross&&r.deletedAcross?null:new t(n.pos,Math.max(n.pos,r.pos),this.slice,this.structure)}merge(e){if(!(e instanceof t)||e.structure||this.structure)return null;if(this.from+this.slice.size==e.from&&!this.slice.openEnd&&!e.slice.openStart){let n=this.slice.size+e.slice.size==0?M.empty:new M(this.slice.content.append(e.slice.content),this.slice.openStart,e.slice.openEnd);return new t(this.from,this.to+(e.to-e.from),n,this.structure)}else if(e.to==this.from&&!this.slice.openStart&&!e.slice.openEnd){let n=this.slice.size+e.slice.size==0?M.empty:new M(e.slice.content.append(this.slice.content),e.slice.openStart,this.slice.openEnd);return new t(e.from,this.to,n,this.structure)}else return null}toJSON(){let e={stepType:"replace",from:this.from,to:this.to};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,n){if(typeof n.from!="number"||typeof n.to!="number")throw new RangeError("Invalid input for ReplaceStep.fromJSON");return new t(n.from,n.to,M.fromJSON(e,n.slice),!!n.structure)}};ae.jsonID("replace",pe);var ne=class t extends ae{constructor(e,n,r,o,i,s,l=!1){super(),this.from=e,this.to=n,this.gapFrom=r,this.gapTo=o,this.slice=i,this.insert=s,this.structure=l}apply(e){if(this.structure&&(Ni(e,this.from,this.gapFrom)||Ni(e,this.gapTo,this.to)))return ce.fail("Structure gap-replace would overwrite content");let n=e.slice(this.gapFrom,this.gapTo);if(n.openStart||n.openEnd)return ce.fail("Gap is not a flat range");let r=this.slice.insertAt(this.insert,n.content);return r?ce.fromReplace(e,this.from,this.to,r):ce.fail("Content does not fit in gap")}getMap(){return new st([this.from,this.gapFrom-this.from,this.insert,this.gapTo,this.to-this.gapTo,this.slice.size-this.insert])}invert(e){let n=this.gapTo-this.gapFrom;return new t(this.from,this.from+this.slice.size+n,this.from+this.insert,this.from+this.insert+n,e.slice(this.from,this.to).removeBetween(this.gapFrom-this.from,this.gapTo-this.from),this.gapFrom-this.from,this.structure)}map(e){let n=e.mapResult(this.from,1),r=e.mapResult(this.to,-1),o=this.from==this.gapFrom?n.pos:e.map(this.gapFrom,-1),i=this.to==this.gapTo?r.pos:e.map(this.gapTo,1);return n.deletedAcross&&r.deletedAcross||o<n.pos||i>r.pos?null:new t(n.pos,r.pos,o,i,this.slice,this.insert,this.structure)}toJSON(){let e={stepType:"replaceAround",from:this.from,to:this.to,gapFrom:this.gapFrom,gapTo:this.gapTo,insert:this.insert};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,n){if(typeof n.from!="number"||typeof n.to!="number"||typeof n.gapFrom!="number"||typeof n.gapTo!="number"||typeof n.insert!="number")throw new RangeError("Invalid input for ReplaceAroundStep.fromJSON");return new t(n.from,n.to,n.gapFrom,n.gapTo,M.fromJSON(e,n.slice),n.insert,!!n.structure)}};ae.jsonID("replaceAround",ne);function Ni(t,e,n){let r=t.resolve(e),o=n-e,i=r.depth;for(;o>0&&i>0&&r.indexAfter(i)==r.node(i).childCount;)i--,o--;if(o>0){let s=r.node(i).maybeChild(r.indexAfter(i));for(;o>0;){if(!s||s.isLeaf)return!0;s=s.firstChild,o--}}return!1}function sp(t,e,n,r){let o=[],i=[],s,l;t.doc.nodesBetween(e,n,(a,c,u)=>{if(!a.isInline)return;let d=a.marks;if(!r.isInSet(d)&&u.type.allowsMarkType(r.type)){let f=Math.max(c,e),h=Math.min(c+a.nodeSize,n),p=r.addToSet(d);for(let m=0;m<d.length;m++)d[m].isInSet(p)||(s&&s.to==f&&s.mark.eq(d[m])?s.to=h:o.push(s=new lt(f,h,d[m])));l&&l.to==f?l.to=h:i.push(l=new Hn(f,h,r))}}),o.forEach(a=>t.step(a)),i.forEach(a=>t.step(a))}function lp(t,e,n,r){let o=[],i=0;t.doc.nodesBetween(e,n,(s,l)=>{if(!s.isInline)return;i++;let a=null;if(r instanceof Dn){let c=s.marks,u;for(;u=r.isInSet(c);)(a||(a=[])).push(u),c=u.removeFromSet(c)}else r?r.isInSet(s.marks)&&(a=[r]):a=s.marks;if(a&&a.length){let c=Math.min(l+s.nodeSize,n);for(let u=0;u<a.length;u++){let d=a[u],f;for(let h=0;h<o.length;h++){let p=o[h];p.step==i-1&&d.eq(o[h].style)&&(f=p)}f?(f.to=c,f.step=i):o.push({style:d,from:Math.max(l,e),to:c,step:i})}}}),o.forEach(s=>t.step(new lt(s.from,s.to,s.style)))}function Pi(t,e,n,r=n.contentMatch,o=!0){let i=t.doc.nodeAt(e),s=[],l=e+1;for(let a=0;a<i.childCount;a++){let c=i.child(a),u=l+c.nodeSize,d=r.matchType(c.type);if(!d)s.push(new pe(l,u,M.empty));else{r=d;for(let f=0;f<c.marks.length;f++)n.allowsMarkType(c.marks[f].type)||t.step(new lt(l,u,c.marks[f]));if(o&&c.isText&&n.whitespace!="pre"){let f,h=/\r?\n|\r/g,p;for(;f=h.exec(c.text);)p||(p=new M(w.from(n.schema.text(" ",n.allowedMarks(c.marks))),0,0)),s.push(new pe(l+f.index,l+f.index+f[0].length,p))}}l=u}if(!r.validEnd){let a=r.fillBefore(w.empty,!0);t.replace(l,l,new M(a,0,0))}for(let a=s.length-1;a>=0;a--)t.step(s[a])}function ap(t,e,n){return(e==0||t.canReplace(e,t.childCount))&&(n==t.childCount||t.canReplace(0,n))}function at(t){let n=t.parent.content.cutByIndex(t.startIndex,t.endIndex);for(let r=t.depth;;--r){let o=t.$from.node(r),i=t.$from.index(r),s=t.$to.indexAfter(r);if(r<t.depth&&o.canReplace(i,s,n))return r;if(r==0||o.type.spec.isolating||!ap(o,i,s))break}return null}function cp(t,e,n){let{$from:r,$to:o,depth:i}=e,s=r.before(i+1),l=o.after(i+1),a=s,c=l,u=w.empty,d=0;for(let p=i,m=!1;p>n;p--)m||r.index(p)>0?(m=!0,u=w.from(r.node(p).copy(u)),d++):a--;let f=w.empty,h=0;for(let p=i,m=!1;p>n;p--)m||o.after(p+1)<o.end(p)?(m=!0,f=w.from(o.node(p).copy(f)),h++):c++;t.step(new ne(a,c,s,l,new M(u.append(f),d,h),u.size-d,!0))}function cn(t,e,n=null,r=t){let o=up(t,e),i=o&&dp(r,e);return i?o.map(Ta).concat({type:e,attrs:n}).concat(i.map(Ta)):null}function Ta(t){return{type:t,attrs:null}}function up(t,e){let{parent:n,startIndex:r,endIndex:o}=t,i=n.contentMatchAt(r).findWrapping(e);if(!i)return null;let s=i.length?i[0]:e;return n.canReplaceWith(r,o,s)?i:null}function dp(t,e){let{parent:n,startIndex:r,endIndex:o}=t,i=n.child(r),s=e.contentMatch.findWrapping(i.type);if(!s)return null;let a=(s.length?s[s.length-1]:e).contentMatch;for(let c=r;a&&c<o;c++)a=a.matchType(n.child(c).type);return!a||!a.validEnd?null:s}function fp(t,e,n){let r=w.empty;for(let s=n.length-1;s>=0;s--){if(r.size){let l=n[s].type.contentMatch.matchFragment(r);if(!l||!l.validEnd)throw new RangeError("Wrapper type given to Transform.wrap does not form valid content of its parent wrapper")}r=w.from(n[s].type.create(n[s].attrs,r))}let o=e.start,i=e.end;t.step(new ne(o,i,o,i,new M(r,0,0),n.length,!0))}function hp(t,e,n,r,o){if(!r.isTextblock)throw new RangeError("Type given to setBlockType should be a textblock");let i=t.steps.length;t.doc.nodesBetween(e,n,(s,l)=>{let a=typeof o=="function"?o(s):o;if(s.isTextblock&&!s.hasMarkup(r,a)&&pp(t.doc,t.mapping.slice(i).map(l),r)){let c=null;if(r.schema.linebreakReplacement){let h=r.whitespace=="pre",p=!!r.contentMatch.matchType(r.schema.linebreakReplacement);h&&!p?c=!1:!h&&p&&(c=!0)}c===!1&&Pa(t,s,l,i),Pi(t,t.mapping.slice(i).map(l,1),r,void 0,c===null);let u=t.mapping.slice(i),d=u.map(l,1),f=u.map(l+s.nodeSize,1);return t.step(new ne(d,f,d+1,f-1,new M(w.from(r.create(a,null,s.marks)),0,0),1,!0)),c===!0&&Da(t,s,l,i),!1}})}function Da(t,e,n,r){e.forEach((o,i)=>{if(o.isText){let s,l=/\r?\n|\r/g;for(;s=l.exec(o.text);){let a=t.mapping.slice(r).map(n+1+i+s.index);t.replaceWith(a,a+1,e.type.schema.linebreakReplacement.create())}}})}function Pa(t,e,n,r){e.forEach((o,i)=>{if(o.type==o.type.schema.linebreakReplacement){let s=t.mapping.slice(r).map(n+1+i);t.replaceWith(s,s+1,e.type.schema.text(`
`))}})}function pp(t,e,n){let r=t.resolve(e),o=r.index();return r.parent.canReplaceWith(o,o+1,n)}function mp(t,e,n,r,o){let i=t.doc.nodeAt(e);if(!i)throw new RangeError("No node at given position");n||(n=i.type);let s=n.create(r,null,o||i.marks);if(i.isLeaf)return t.replaceWith(e,e+i.nodeSize,s);if(!n.validContent(i.content))throw new RangeError("Invalid content for node type "+n.name);t.step(new ne(e,e+i.nodeSize,e+1,e+i.nodeSize-1,new M(w.from(s),0,0),1,!0))}function Ae(t,e,n=1,r){let o=t.resolve(e),i=o.depth-n,s=r&&r[r.length-1]||o.parent;if(i<0||o.parent.type.spec.isolating||!o.parent.canReplace(o.index(),o.parent.childCount)||!s.type.validContent(o.parent.content.cutByIndex(o.index(),o.parent.childCount)))return!1;for(let c=o.depth-1,u=n-2;c>i;c--,u--){let d=o.node(c),f=o.index(c);if(d.type.spec.isolating)return!1;let h=d.content.cutByIndex(f,d.childCount),p=r&&r[u+1];p&&(h=h.replaceChild(0,p.type.create(p.attrs)));let m=r&&r[u]||d;if(!d.canReplace(f+1,d.childCount)||!m.type.validContent(h))return!1}let l=o.indexAfter(i),a=r&&r[0];return o.node(i).canReplaceWith(l,l,a?a.type:o.node(i+1).type)}function gp(t,e,n=1,r){let o=t.doc.resolve(e),i=w.empty,s=w.empty;for(let l=o.depth,a=o.depth-n,c=n-1;l>a;l--,c--){i=w.from(o.node(l).copy(i));let u=r&&r[c];s=w.from(u?u.type.create(u.attrs,s):o.node(l).copy(s))}t.step(new pe(e,e,new M(i.append(s),n,n),!0))}function Ne(t,e){let n=t.resolve(e),r=n.index();return Ia(n.nodeBefore,n.nodeAfter)&&n.parent.canReplace(r,r+1)}function yp(t,e){e.content.size||t.type.compatibleContent(e.type);let n=t.contentMatchAt(t.childCount),{linebreakReplacement:r}=t.type.schema;for(let o=0;o<e.childCount;o++){let i=e.child(o),s=i.type==r?t.type.schema.nodes.text:i.type;if(n=n.matchType(s),!n||!t.type.allowsMarks(i.marks))return!1}return n.validEnd}function Ia(t,e){return!!(t&&e&&!t.isLeaf&&yp(t,e))}function Ft(t,e,n=-1){let r=t.resolve(e);for(let o=r.depth;;o--){let i,s,l=r.index(o);if(o==r.depth?(i=r.nodeBefore,s=r.nodeAfter):n>0?(i=r.node(o+1),l++,s=r.node(o).maybeChild(l)):(i=r.node(o).maybeChild(l-1),s=r.node(o+1)),i&&!i.isTextblock&&Ia(i,s)&&r.node(o).canReplace(l,l+1))return e;if(o==0)break;e=n<0?r.before(o):r.after(o)}}function bp(t,e,n){let r=null,{linebreakReplacement:o}=t.doc.type.schema,i=t.doc.resolve(e-n),s=i.node().type;if(o&&s.inlineContent){let u=s.whitespace=="pre",d=!!s.contentMatch.matchType(o);u&&!d?r=!1:!u&&d&&(r=!0)}let l=t.steps.length;if(r===!1){let u=t.doc.resolve(e+n);Pa(t,u.node(),u.before(),l)}s.inlineContent&&Pi(t,e+n-1,s,i.node().contentMatchAt(i.index()),r==null);let a=t.mapping.slice(l),c=a.map(e-n);if(t.step(new pe(c,a.map(e+n,-1),M.empty,!0)),r===!0){let u=t.doc.resolve(c);Da(t,u.node(),u.before(),t.steps.length)}return t}function xp(t,e,n){let r=t.resolve(e);if(r.parent.canReplaceWith(r.index(),r.index(),n))return e;if(r.parentOffset==0)for(let o=r.depth-1;o>=0;o--){let i=r.index(o);if(r.node(o).canReplaceWith(i,i,n))return r.before(o+1);if(i>0)return null}if(r.parentOffset==r.parent.content.size)for(let o=r.depth-1;o>=0;o--){let i=r.indexAfter(o);if(r.node(o).canReplaceWith(i,i,n))return r.after(o+1);if(i<r.node(o).childCount)return null}return null}function Rr(t,e,n){let r=t.resolve(e);if(!n.content.size)return e;let o=n.content;for(let i=0;i<n.openStart;i++)o=o.firstChild.content;for(let i=1;i<=(n.openStart==0&&n.size?2:1);i++)for(let s=r.depth;s>=0;s--){let l=s==r.depth?0:r.pos<=(r.start(s+1)+r.end(s+1))/2?-1:1,a=r.index(s)+(l>0?1:0),c=r.node(s),u=!1;if(i==1)u=c.canReplace(a,a,o);else{let d=c.contentMatchAt(a).findWrapping(o.firstChild.type);u=d&&c.canReplaceWith(a,a,d[0])}if(u)return l==0?r.pos:l<0?r.before(s+1):r.after(s+1)}return null}function $n(t,e,n=e,r=M.empty){if(e==n&&!r.size)return null;let o=t.resolve(e),i=t.resolve(n);return La(o,i,r)?new pe(e,n,r):new Ri(o,i,r).fit()}function La(t,e,n){return!n.openStart&&!n.openEnd&&t.start()==e.start()&&t.parent.canReplace(t.index(),e.index(),n.content)}var Ri=class{constructor(e,n,r){this.$from=e,this.$to=n,this.unplaced=r,this.frontier=[],this.placed=w.empty;for(let o=0;o<=e.depth;o++){let i=e.node(o);this.frontier.push({type:i.type,match:i.contentMatchAt(e.indexAfter(o))})}for(let o=e.depth;o>0;o--)this.placed=w.from(e.node(o).copy(this.placed))}get depth(){return this.frontier.length-1}fit(){for(;this.unplaced.size;){let c=this.findFittable();c?this.placeNodes(c):this.openMore()||this.dropNode()}let e=this.mustMoveInline(),n=this.placed.size-this.depth-this.$from.depth,r=this.$from,o=this.close(e<0?this.$to:r.doc.resolve(e));if(!o)return null;let i=this.placed,s=r.depth,l=o.depth;for(;s&&l&&i.childCount==1;)i=i.firstChild.content,s--,l--;let a=new M(i,s,l);return e>-1?new ne(r.pos,e,this.$to.pos,this.$to.end(),a,n):a.size||r.pos!=this.$to.pos?new pe(r.pos,o.pos,a):null}findFittable(){let e=this.unplaced.openStart;for(let n=this.unplaced.content,r=0,o=this.unplaced.openEnd;r<e;r++){let i=n.firstChild;if(n.childCount>1&&(o=0),i.type.spec.isolating&&o<=r){e=r;break}n=i.content}for(let n=1;n<=2;n++)for(let r=n==1?e:this.unplaced.openStart;r>=0;r--){let o,i=null;r?(i=Ei(this.unplaced.content,r-1).firstChild,o=i.content):o=this.unplaced.content;let s=o.firstChild;for(let l=this.depth;l>=0;l--){let{type:a,match:c}=this.frontier[l],u,d=null;if(n==1&&(s?c.matchType(s.type)||(d=c.fillBefore(w.from(s),!1)):i&&a.compatibleContent(i.type)))return{sliceDepth:r,frontierDepth:l,parent:i,inject:d};if(n==2&&s&&(u=c.findWrapping(s.type)))return{sliceDepth:r,frontierDepth:l,parent:i,wrap:u};if(i&&c.matchType(i.type))break}}}openMore(){let{content:e,openStart:n,openEnd:r}=this.unplaced,o=Ei(e,n);return!o.childCount||o.firstChild.isLeaf?!1:(this.unplaced=new M(e,n+1,Math.max(r,o.size+n>=e.size-r?n+1:0)),!0)}dropNode(){let{content:e,openStart:n,openEnd:r}=this.unplaced,o=Ei(e,n);if(o.childCount<=1&&n>0){let i=e.size-n<=n+o.size;this.unplaced=new M(In(e,n-1,1),n-1,i?n-1:r)}else this.unplaced=new M(In(e,n,1),n,r)}placeNodes({sliceDepth:e,frontierDepth:n,parent:r,inject:o,wrap:i}){for(;this.depth>n;)this.closeFrontierNode();if(i)for(let m=0;m<i.length;m++)this.openFrontierNode(i[m]);let s=this.unplaced,l=r?r.content:s.content,a=s.openStart-e,c=0,u=[],{match:d,type:f}=this.frontier[n];if(o){for(let m=0;m<o.childCount;m++)u.push(o.child(m));d=d.matchFragment(o)}let h=l.size+e-(s.content.size-s.openEnd);for(;c<l.childCount;){let m=l.child(c),g=d.matchType(m.type);if(!g)break;c++,(c>1||a==0||m.content.size)&&(d=g,u.push(Ba(m.mark(f.allowedMarks(m.marks)),c==1?a:0,c==l.childCount?h:-1)))}let p=c==l.childCount;p||(h=-1),this.placed=Ln(this.placed,n,w.from(u)),this.frontier[n].match=d,p&&h<0&&r&&r.type==this.frontier[this.depth].type&&this.frontier.length>1&&this.closeFrontierNode();for(let m=0,g=l;m<h;m++){let y=g.lastChild;this.frontier.push({type:y.type,match:y.contentMatchAt(y.childCount)}),g=y.content}this.unplaced=p?e==0?M.empty:new M(In(s.content,e-1,1),e-1,h<0?s.openEnd:e-1):new M(In(s.content,e,c),s.openStart,s.openEnd)}mustMoveInline(){if(!this.$to.parent.isTextblock)return-1;let e=this.frontier[this.depth],n;if(!e.type.isTextblock||!Oi(this.$to,this.$to.depth,e.type,e.match,!1)||this.$to.depth==this.depth&&(n=this.findCloseLevel(this.$to))&&n.depth==this.depth)return-1;let{depth:r}=this.$to,o=this.$to.after(r);for(;r>1&&o==this.$to.end(--r);)++o;return o}findCloseLevel(e){e:for(let n=Math.min(this.depth,e.depth);n>=0;n--){let{match:r,type:o}=this.frontier[n],i=n<e.depth&&e.end(n+1)==e.pos+(e.depth-(n+1)),s=Oi(e,n,o,r,i);if(s){for(let l=n-1;l>=0;l--){let{match:a,type:c}=this.frontier[l],u=Oi(e,l,c,a,!0);if(!u||u.childCount)continue e}return{depth:n,fit:s,move:i?e.doc.resolve(e.after(n+1)):e}}}}close(e){let n=this.findCloseLevel(e);if(!n)return null;for(;this.depth>n.depth;)this.closeFrontierNode();n.fit.childCount&&(this.placed=Ln(this.placed,n.depth,n.fit)),e=n.move;for(let r=n.depth+1;r<=e.depth;r++){let o=e.node(r),i=o.type.contentMatch.fillBefore(o.content,!0,e.index(r));this.openFrontierNode(o.type,o.attrs,i)}return e}openFrontierNode(e,n=null,r){let o=this.frontier[this.depth];o.match=o.match.matchType(e),this.placed=Ln(this.placed,this.depth,w.from(e.create(n,r))),this.frontier.push({type:e,match:e.contentMatch})}closeFrontierNode(){let n=this.frontier.pop().match.fillBefore(w.empty,!0);n.childCount&&(this.placed=Ln(this.placed,this.frontier.length,n))}};function In(t,e,n){return e==0?t.cutByIndex(n,t.childCount):t.replaceChild(0,t.firstChild.copy(In(t.firstChild.content,e-1,n)))}function Ln(t,e,n){return e==0?t.append(n):t.replaceChild(t.childCount-1,t.lastChild.copy(Ln(t.lastChild.content,e-1,n)))}function Ei(t,e){for(let n=0;n<e;n++)t=t.firstChild.content;return t}function Ba(t,e,n){if(e<=0)return t;let r=t.content;return e>1&&(r=r.replaceChild(0,Ba(r.firstChild,e-1,r.childCount==1?n-1:0))),e>0&&(r=t.type.contentMatch.fillBefore(r).append(r),n<=0&&(r=r.append(t.type.contentMatch.matchFragment(r).fillBefore(w.empty,!0)))),t.copy(r)}function Oi(t,e,n,r,o){let i=t.node(e),s=o?t.indexAfter(e):t.index(e);if(s==i.childCount&&!n.compatibleContent(i.type))return null;let l=r.fillBefore(i.content,!0,s);return l&&!wp(n,i.content,s)?l:null}function wp(t,e,n){for(let r=n;r<e.childCount;r++)if(!t.allowsMarks(e.child(r).marks))return!0;return!1}function Sp(t){return t.spec.defining||t.spec.definingForContent}function kp(t,e,n,r){if(!r.size)return t.deleteRange(e,n);let o=t.doc.resolve(e),i=t.doc.resolve(n);if(La(o,i,r))return t.step(new pe(e,n,r));let s=Ha(o,t.doc.resolve(n));s[s.length-1]==0&&s.pop();let l=-(o.depth+1);s.unshift(l);for(let f=o.depth,h=o.pos-1;f>0;f--,h--){let p=o.node(f).type.spec;if(p.defining||p.definingAsContext||p.isolating)break;s.indexOf(f)>-1?l=f:o.before(f)==h&&s.splice(1,0,-f)}let a=s.indexOf(l),c=[],u=r.openStart;for(let f=r.content,h=0;;h++){let p=f.firstChild;if(c.push(p),h==r.openStart)break;f=p.content}for(let f=u-1;f>=0;f--){let h=c[f],p=Sp(h.type);if(p&&!h.sameMarkup(o.node(Math.abs(l)-1)))u=f;else if(p||!h.type.isTextblock)break}for(let f=r.openStart;f>=0;f--){let h=(f+u+1)%(r.openStart+1),p=c[h];if(p)for(let m=0;m<s.length;m++){let g=s[(m+a)%s.length],y=!0;g<0&&(y=!1,g=-g);let b=o.node(g-1),x=o.index(g-1);if(b.canReplaceWith(x,x,p.type,p.marks))return t.replace(o.before(g),y?i.after(g):n,new M(za(r.content,0,r.openStart,h),h,r.openEnd))}}let d=t.steps.length;for(let f=s.length-1;f>=0&&(t.replace(e,n,r),!(t.steps.length>d));f--){let h=s[f];h<0||(e=o.before(h),n=i.after(h))}}function za(t,e,n,r,o){if(e<n){let i=t.firstChild;t=t.replaceChild(0,i.copy(za(i.content,e+1,n,r,i)))}if(e>r){let i=o.contentMatchAt(0),s=i.fillBefore(t).append(t);t=s.append(i.matchFragment(s).fillBefore(w.empty,!0))}return t}function Cp(t,e,n,r){if(!r.isInline&&e==n&&t.doc.resolve(e).parent.content.size){let o=xp(t.doc,e,r.type);o!=null&&(e=n=o)}t.replaceRange(e,n,new M(w.from(r),0,0))}function Mp(t,e,n){let r=t.doc.resolve(e),o=t.doc.resolve(n),i=Ha(r,o);for(let s=0;s<i.length;s++){let l=i[s],a=s==i.length-1;if(a&&l==0||r.node(l).type.contentMatch.validEnd)return t.delete(r.start(l),o.end(l));if(l>0&&(a||r.node(l-1).canReplace(r.index(l-1),o.indexAfter(l-1))))return t.delete(r.before(l),o.after(l))}for(let s=1;s<=r.depth&&s<=o.depth;s++)if(e-r.start(s)==r.depth-s&&n>r.end(s)&&o.end(s)-n!=o.depth-s&&r.start(s-1)==o.start(s-1)&&r.node(s-1).canReplace(r.index(s-1),o.index(s-1)))return t.delete(r.before(s),n);t.delete(e,n)}function Ha(t,e){let n=[],r=Math.min(t.depth,e.depth);for(let o=r;o>=0;o--){let i=t.start(o);if(i<t.pos-(t.depth-o)||e.end(o)>e.pos+(e.depth-o)||t.node(o).type.spec.isolating||e.node(o).type.spec.isolating)break;(i==e.start(o)||o==t.depth&&o==e.depth&&t.parent.inlineContent&&e.parent.inlineContent&&o&&e.start(o-1)==i-1)&&n.push(o)}return n}var Or=class t extends ae{constructor(e,n,r){super(),this.pos=e,this.attr=n,this.value=r}apply(e){let n=e.nodeAt(this.pos);if(!n)return ce.fail("No node at attribute step's position");let r=Object.create(null);for(let i in n.attrs)r[i]=n.attrs[i];r[this.attr]=this.value;let o=n.type.create(r,null,n.marks);return ce.fromReplace(e,this.pos,this.pos+1,new M(w.from(o),0,n.isLeaf?0:1))}getMap(){return st.empty}invert(e){return new t(this.pos,this.attr,e.nodeAt(this.pos).attrs[this.attr])}map(e){let n=e.mapResult(this.pos,1);return n.deletedAfter?null:new t(n.pos,this.attr,this.value)}toJSON(){return{stepType:"attr",pos:this.pos,attr:this.attr,value:this.value}}static fromJSON(e,n){if(typeof n.pos!="number"||typeof n.attr!="string")throw new RangeError("Invalid input for AttrStep.fromJSON");return new t(n.pos,n.attr,n.value)}};ae.jsonID("attr",Or);var Nr=class t extends ae{constructor(e,n){super(),this.attr=e,this.value=n}apply(e){let n=Object.create(null);for(let o in e.attrs)n[o]=e.attrs[o];n[this.attr]=this.value;let r=e.type.create(n,e.content,e.marks);return ce.ok(r)}getMap(){return st.empty}invert(e){return new t(this.attr,e.attrs[this.attr])}map(e){return this}toJSON(){return{stepType:"docAttr",attr:this.attr,value:this.value}}static fromJSON(e,n){if(typeof n.attr!="string")throw new RangeError("Invalid input for DocAttrStep.fromJSON");return new t(n.attr,n.value)}};ae.jsonID("docAttr",Nr);var an=class extends Error{};an=function t(e){let n=Error.call(this,e);return n.__proto__=t.prototype,n};an.prototype=Object.create(Error.prototype);an.prototype.constructor=an;an.prototype.name="TransformError";var kt=class{constructor(e){this.doc=e,this.steps=[],this.docs=[],this.mapping=new zn}get before(){return this.docs.length?this.docs[0]:this.doc}step(e){let n=this.maybeStep(e);if(n.failed)throw new an(n.failed);return this}maybeStep(e){let n=e.apply(this.doc);return n.failed||this.addStep(e,n.doc),n}get docChanged(){return this.steps.length>0}addStep(e,n){this.docs.push(this.doc),this.steps.push(e),this.mapping.appendMap(e.getMap()),this.doc=n}replace(e,n=e,r=M.empty){let o=$n(this.doc,e,n,r);return o&&this.step(o),this}replaceWith(e,n,r){return this.replace(e,n,new M(w.from(r),0,0))}delete(e,n){return this.replace(e,n,M.empty)}insert(e,n){return this.replaceWith(e,e,n)}replaceRange(e,n,r){return kp(this,e,n,r),this}replaceRangeWith(e,n,r){return Cp(this,e,n,r),this}deleteRange(e,n){return Mp(this,e,n),this}lift(e,n){return cp(this,e,n),this}join(e,n=1){return bp(this,e,n),this}wrap(e,n){return fp(this,e,n),this}setBlockType(e,n=e,r,o=null){return hp(this,e,n,r,o),this}setNodeMarkup(e,n,r=null,o){return mp(this,e,n,r,o),this}setNodeAttribute(e,n,r){return this.step(new Or(e,n,r)),this}setDocAttribute(e,n){return this.step(new Nr(e,n)),this}addNodeMark(e,n){return this.step(new Fn(e,n)),this}removeNodeMark(e,n){let r=this.doc.nodeAt(e);if(!r)throw new RangeError("No node at position "+e);if(n instanceof V)n.isInSet(r.marks)&&this.step(new ln(e,n));else{let o=r.marks,i,s=[];for(;i=n.isInSet(o);)s.push(new ln(e,i)),o=i.removeFromSet(o);for(let l=s.length-1;l>=0;l--)this.step(s[l])}return this}split(e,n=1,r){return gp(this,e,n,r),this}addMark(e,n,r){return sp(this,e,n,r),this}removeMark(e,n,r){return lp(this,e,n,r),this}clearIncompatible(e,n,r){return Pi(this,e,n,r),this}};var Ii=Object.create(null),E=class{constructor(e,n,r){this.$anchor=e,this.$head=n,this.ranges=r||[new dn(e.min(n),e.max(n))]}get anchor(){return this.$anchor.pos}get head(){return this.$head.pos}get from(){return this.$from.pos}get to(){return this.$to.pos}get $from(){return this.ranges[0].$from}get $to(){return this.ranges[0].$to}get empty(){let e=this.ranges;for(let n=0;n<e.length;n++)if(e[n].$from.pos!=e[n].$to.pos)return!1;return!0}content(){return this.$from.doc.slice(this.from,this.to,!0)}replace(e,n=M.empty){let r=n.content.lastChild,o=null;for(let l=0;l<n.openEnd;l++)o=r,r=r.lastChild;let i=e.steps.length,s=this.ranges;for(let l=0;l<s.length;l++){let{$from:a,$to:c}=s[l],u=e.mapping.slice(i);e.replaceRange(u.map(a.pos),u.map(c.pos),l?M.empty:n),l==0&&Va(e,i,(r?r.isInline:o&&o.isTextblock)?-1:1)}}replaceWith(e,n){let r=e.steps.length,o=this.ranges;for(let i=0;i<o.length;i++){let{$from:s,$to:l}=o[i],a=e.mapping.slice(r),c=a.map(s.pos),u=a.map(l.pos);i?e.deleteRange(c,u):(e.replaceRangeWith(c,u,n),Va(e,r,n.isInline?-1:1))}}static findFrom(e,n,r=!1){let o=e.parent.inlineContent?new A(e):un(e.node(0),e.parent,e.pos,e.index(),n,r);if(o)return o;for(let i=e.depth-1;i>=0;i--){let s=n<0?un(e.node(0),e.node(i),e.before(i+1),e.index(i),n,r):un(e.node(0),e.node(i),e.after(i+1),e.index(i)+1,n,r);if(s)return s}return null}static near(e,n=1){return this.findFrom(e,n)||this.findFrom(e,-n)||new be(e.node(0))}static atStart(e){return un(e,e,0,0,1)||new be(e)}static atEnd(e){return un(e,e,e.content.size,e.childCount,-1)||new be(e)}static fromJSON(e,n){if(!n||!n.type)throw new RangeError("Invalid input for Selection.fromJSON");let r=Ii[n.type];if(!r)throw new RangeError(`No selection type ${n.type} defined`);return r.fromJSON(e,n)}static jsonID(e,n){if(e in Ii)throw new RangeError("Duplicate use of selection JSON ID "+e);return Ii[e]=n,n.prototype.jsonID=e,n}getBookmark(){return A.between(this.$anchor,this.$head).getBookmark()}};E.prototype.visible=!0;var dn=class{constructor(e,n){this.$from=e,this.$to=n}},Fa=!1;function $a(t){!Fa&&!t.parent.inlineContent&&(Fa=!0,console.warn("TextSelection endpoint not pointing into a node with inline content ("+t.parent.type.name+")"))}var A=class t extends E{constructor(e,n=e){$a(e),$a(n),super(e,n)}get $cursor(){return this.$anchor.pos==this.$head.pos?this.$head:null}map(e,n){let r=e.resolve(n.map(this.head));if(!r.parent.inlineContent)return E.near(r);let o=e.resolve(n.map(this.anchor));return new t(o.parent.inlineContent?o:r,r)}replace(e,n=M.empty){if(super.replace(e,n),n==M.empty){let r=this.$from.marksAcross(this.$to);r&&e.ensureMarks(r)}}eq(e){return e instanceof t&&e.anchor==this.anchor&&e.head==this.head}getBookmark(){return new Pr(this.anchor,this.head)}toJSON(){return{type:"text",anchor:this.anchor,head:this.head}}static fromJSON(e,n){if(typeof n.anchor!="number"||typeof n.head!="number")throw new RangeError("Invalid input for TextSelection.fromJSON");return new t(e.resolve(n.anchor),e.resolve(n.head))}static create(e,n,r=n){let o=e.resolve(n);return new this(o,r==n?o:e.resolve(r))}static between(e,n,r){let o=e.pos-n.pos;if((!r||o)&&(r=o>=0?1:-1),!n.parent.inlineContent){let i=E.findFrom(n,r,!0)||E.findFrom(n,-r,!0);if(i)n=i.$head;else return E.near(n,r)}return e.parent.inlineContent||(o==0?e=n:(e=(E.findFrom(e,-r,!0)||E.findFrom(e,r,!0)).$anchor,e.pos<n.pos!=o<0&&(e=n))),new t(e,n)}};E.jsonID("text",A);var Pr=class t{constructor(e,n){this.anchor=e,this.head=n}map(e){return new t(e.map(this.anchor),e.map(this.head))}resolve(e){return A.between(e.resolve(this.anchor),e.resolve(this.head))}},O=class t extends E{constructor(e){let n=e.nodeAfter,r=e.node(0).resolve(e.pos+n.nodeSize);super(e,r),this.node=n}map(e,n){let{deleted:r,pos:o}=n.mapResult(this.anchor),i=e.resolve(o);return r?E.near(i):new t(i)}content(){return new M(w.from(this.node),0,0)}eq(e){return e instanceof t&&e.anchor==this.anchor}toJSON(){return{type:"node",anchor:this.anchor}}getBookmark(){return new Bi(this.anchor)}static fromJSON(e,n){if(typeof n.anchor!="number")throw new RangeError("Invalid input for NodeSelection.fromJSON");return new t(e.resolve(n.anchor))}static create(e,n){return new t(e.resolve(n))}static isSelectable(e){return!e.isText&&e.type.spec.selectable!==!1}};O.prototype.visible=!1;E.jsonID("node",O);var Bi=class t{constructor(e){this.anchor=e}map(e){let{deleted:n,pos:r}=e.mapResult(this.anchor);return n?new Pr(r,r):new t(r)}resolve(e){let n=e.resolve(this.anchor),r=n.nodeAfter;return r&&O.isSelectable(r)?new O(n):E.near(n)}},be=class t extends E{constructor(e){super(e.resolve(0),e.resolve(e.content.size))}replace(e,n=M.empty){if(n==M.empty){e.delete(0,e.doc.content.size);let r=E.atStart(e.doc);r.eq(e.selection)||e.setSelection(r)}else super.replace(e,n)}toJSON(){return{type:"all"}}static fromJSON(e){return new t(e)}map(e){return new t(e)}eq(e){return e instanceof t}getBookmark(){return vp}};E.jsonID("all",be);var vp={map(){return this},resolve(t){return new be(t)}};function un(t,e,n,r,o,i=!1){if(e.inlineContent)return A.create(t,n);for(let s=r-(o>0?0:1);o>0?s<e.childCount:s>=0;s+=o){let l=e.child(s);if(l.isAtom){if(!i&&O.isSelectable(l))return O.create(t,n-(o<0?l.nodeSize:0))}else{let a=un(t,l,n+o,o<0?l.childCount:0,o,i);if(a)return a}n+=l.nodeSize*o}return null}function Va(t,e,n){let r=t.steps.length-1;if(r<e)return;let o=t.steps[r];if(!(o instanceof pe||o instanceof ne))return;let i=t.mapping.maps[r],s;i.forEach((l,a,c,u)=>{s==null&&(s=u)}),t.setSelection(E.near(t.doc.resolve(s),n))}var Wa=1,Dr=2,_a=4,zi=class extends kt{constructor(e){super(e.doc),this.curSelectionFor=0,this.updated=0,this.meta=Object.create(null),this.time=Date.now(),this.curSelection=e.selection,this.storedMarks=e.storedMarks}get selection(){return this.curSelectionFor<this.steps.length&&(this.curSelection=this.curSelection.map(this.doc,this.mapping.slice(this.curSelectionFor)),this.curSelectionFor=this.steps.length),this.curSelection}setSelection(e){if(e.$from.doc!=this.doc)throw new RangeError("Selection passed to setSelection must point at the current document");return this.curSelection=e,this.curSelectionFor=this.steps.length,this.updated=(this.updated|Wa)&~Dr,this.storedMarks=null,this}get selectionSet(){return(this.updated&Wa)>0}setStoredMarks(e){return this.storedMarks=e,this.updated|=Dr,this}ensureMarks(e){return V.sameSet(this.storedMarks||this.selection.$from.marks(),e)||this.setStoredMarks(e),this}addStoredMark(e){return this.ensureMarks(e.addToSet(this.storedMarks||this.selection.$head.marks()))}removeStoredMark(e){return this.ensureMarks(e.removeFromSet(this.storedMarks||this.selection.$head.marks()))}get storedMarksSet(){return(this.updated&Dr)>0}addStep(e,n){super.addStep(e,n),this.updated=this.updated&~Dr,this.storedMarks=null}setTime(e){return this.time=e,this}replaceSelection(e){return this.selection.replace(this,e),this}replaceSelectionWith(e,n=!0){let r=this.selection;return n&&(e=e.mark(this.storedMarks||(r.empty?r.$from.marks():r.$from.marksAcross(r.$to)||V.none))),r.replaceWith(this,e),this}deleteSelection(){return this.selection.replace(this),this}insertText(e,n,r){let o=this.doc.type.schema;if(n==null)return e?this.replaceSelectionWith(o.text(e),!0):this.deleteSelection();{if(r==null&&(r=n),r=r??n,!e)return this.deleteRange(n,r);let i=this.storedMarks;if(!i){let s=this.doc.resolve(n);i=r==n?s.marks():s.marksAcross(this.doc.resolve(r))}return this.replaceRangeWith(n,r,o.text(e,i)),this.selection.empty||this.setSelection(E.near(this.selection.$to)),this}}setMeta(e,n){return this.meta[typeof e=="string"?e:e.key]=n,this}getMeta(e){return this.meta[typeof e=="string"?e:e.key]}get isGeneric(){for(let e in this.meta)return!1;return!0}scrollIntoView(){return this.updated|=_a,this}get scrolledIntoView(){return(this.updated&_a)>0}};function ja(t,e){return!e||!t?t:t.bind(e)}var $t=class{constructor(e,n,r){this.name=e,this.init=ja(n.init,r),this.apply=ja(n.apply,r)}},Tp=[new $t("doc",{init(t){return t.doc||t.schema.topNodeType.createAndFill()},apply(t){return t.doc}}),new $t("selection",{init(t,e){return t.selection||E.atStart(e.doc)},apply(t){return t.selection}}),new $t("storedMarks",{init(t){return t.storedMarks||null},apply(t,e,n,r){return r.selection.$cursor?t.storedMarks:null}}),new $t("scrollToSelection",{init(){return 0},apply(t,e){return t.scrolledIntoView?e+1:e}})],Vn=class{constructor(e,n){this.schema=e,this.plugins=[],this.pluginsByKey=Object.create(null),this.fields=Tp.slice(),n&&n.forEach(r=>{if(this.pluginsByKey[r.key])throw new RangeError("Adding different instances of a keyed plugin ("+r.key+")");this.plugins.push(r),this.pluginsByKey[r.key]=r,r.spec.state&&this.fields.push(new $t(r.key,r.spec.state,r))})}},Ir=class t{constructor(e){this.config=e}get schema(){return this.config.schema}get plugins(){return this.config.plugins}apply(e){return this.applyTransaction(e).state}filterTransaction(e,n=-1){for(let r=0;r<this.config.plugins.length;r++)if(r!=n){let o=this.config.plugins[r];if(o.spec.filterTransaction&&!o.spec.filterTransaction.call(o,e,this))return!1}return!0}applyTransaction(e){if(!this.filterTransaction(e))return{state:this,transactions:[]};let n=[e],r=this.applyInner(e),o=null;for(;;){let i=!1;for(let s=0;s<this.config.plugins.length;s++){let l=this.config.plugins[s];if(l.spec.appendTransaction){let a=o?o[s].n:0,c=o?o[s].state:this,u=a<n.length&&l.spec.appendTransaction.call(l,a?n.slice(a):n,c,r);if(u&&r.filterTransaction(u,s)){if(u.setMeta("appendedTransaction",e),!o){o=[];for(let d=0;d<this.config.plugins.length;d++)o.push(d<s?{state:r,n:n.length}:{state:this,n:0})}n.push(u),r=r.applyInner(u),i=!0}o&&(o[s]={state:r,n:n.length})}}if(!i)return{state:r,transactions:n}}}applyInner(e){if(!e.before.eq(this.doc))throw new RangeError("Applying a mismatched transaction");let n=new t(this.config),r=this.config.fields;for(let o=0;o<r.length;o++){let i=r[o];n[i.name]=i.apply(e,this[i.name],this,n)}return n}get tr(){return new zi(this)}static create(e){let n=new Vn(e.doc?e.doc.type.schema:e.schema,e.plugins),r=new t(n);for(let o=0;o<n.fields.length;o++)r[n.fields[o].name]=n.fields[o].init(e,r);return r}reconfigure(e){let n=new Vn(this.schema,e.plugins),r=n.fields,o=new t(n);for(let i=0;i<r.length;i++){let s=r[i].name;o[s]=this.hasOwnProperty(s)?this[s]:r[i].init(e,o)}return o}toJSON(e){let n={doc:this.doc.toJSON(),selection:this.selection.toJSON()};if(this.storedMarks&&(n.storedMarks=this.storedMarks.map(r=>r.toJSON())),e&&typeof e=="object")for(let r in e){if(r=="doc"||r=="selection")throw new RangeError("The JSON fields `doc` and `selection` are reserved");let o=e[r],i=o.spec.state;i&&i.toJSON&&(n[r]=i.toJSON.call(o,this[o.key]))}return n}static fromJSON(e,n,r){if(!n)throw new RangeError("Invalid input for EditorState.fromJSON");if(!e.schema)throw new RangeError("Required config field 'schema' missing");let o=new Vn(e.schema,e.plugins),i=new t(o);return o.fields.forEach(s=>{if(s.name=="doc")i.doc=le.fromJSON(e.schema,n.doc);else if(s.name=="selection")i.selection=E.fromJSON(i.doc,n.selection);else if(s.name=="storedMarks")n.storedMarks&&(i.storedMarks=n.storedMarks.map(e.schema.markFromJSON));else{if(r)for(let l in r){let a=r[l],c=a.spec.state;if(a.key==s.name&&c&&c.fromJSON&&Object.prototype.hasOwnProperty.call(n,l)){i[s.name]=c.fromJSON.call(a,e,n[l],i);return}}i[s.name]=s.init(e,i)}}),i}};function Ka(t,e,n){for(let r in t){let o=t[r];o instanceof Function?o=o.bind(e):r=="handleDOMEvents"&&(o=Ka(o,e,{})),n[r]=o}return n}var N=class{constructor(e){this.spec=e,this.props={},e.props&&Ka(e.props,this,this.props),this.key=e.key?e.key.key:Ua("plugin")}getState(e){return e[this.key]}},Li=Object.create(null);function Ua(t){return t in Li?t+"$"+ ++Li[t]:(Li[t]=0,t+"$")}var L=class{constructor(e="key"){this.key=Ua(e)}get(e){return e.config.pluginsByKey[this.key]}getState(e){return e[this.key]}};var ue=function(t){for(var e=0;;e++)if(t=t.previousSibling,!t)return e},gn=function(t){let e=t.assignedSlot||t.parentNode;return e&&e.nodeType==11?e.host:e},_i=null,ut=function(t,e,n){let r=_i||(_i=document.createRange());return r.setEnd(t,n??t.nodeValue.length),r.setStart(t,e||0),r},Ap=function(){_i=null},Jt=function(t,e,n,r){return n&&(Ja(t,e,n,r,-1)||Ja(t,e,n,r,1))},Ep=/^(img|br|input|textarea|hr)$/i;function Ja(t,e,n,r,o){for(var i;;){if(t==n&&e==r)return!0;if(e==(o<0?0:De(t))){let s=t.parentNode;if(!s||s.nodeType!=1||Gn(t)||Ep.test(t.nodeName)||t.contentEditable=="false")return!1;e=ue(t)+(o<0?0:1),t=s}else if(t.nodeType==1){let s=t.childNodes[e+(o<0?-1:0)];if(s.nodeType==1&&s.contentEditable=="false")if(!((i=s.pmViewDesc)===null||i===void 0)&&i.ignoreForSelection)e+=o;else return!1;else t=s,e=o<0?De(t):0}else return!1}}function De(t){return t.nodeType==3?t.nodeValue.length:t.childNodes.length}function Op(t,e){for(;;){if(t.nodeType==3&&e)return t;if(t.nodeType==1&&e>0){if(t.contentEditable=="false")return null;t=t.childNodes[e-1],e=De(t)}else if(t.parentNode&&!Gn(t))e=ue(t),t=t.parentNode;else return null}}function Np(t,e){for(;;){if(t.nodeType==3&&e<t.nodeValue.length)return t;if(t.nodeType==1&&e<t.childNodes.length){if(t.contentEditable=="false")return null;t=t.childNodes[e],e=0}else if(t.parentNode&&!Gn(t))e=ue(t)+1,t=t.parentNode;else return null}}function Rp(t,e,n){for(let r=e==0,o=e==De(t);r||o;){if(t==n)return!0;let i=ue(t);if(t=t.parentNode,!t)return!1;r=r&&i==0,o=o&&i==De(t)}}function Gn(t){let e;for(let n=t;n&&!(e=n.pmViewDesc);n=n.parentNode);return e&&e.node&&e.node.isBlock&&(e.dom==t||e.contentDOM==t)}var Kr=function(t){return t.focusNode&&Jt(t.focusNode,t.focusOffset,t.anchorNode,t.anchorOffset)};function Vt(t,e){let n=document.createEvent("Event");return n.initEvent("keydown",!0,!0),n.keyCode=t,n.key=n.code=e,n}function Dp(t){let e=t.activeElement;for(;e&&e.shadowRoot;)e=e.shadowRoot.activeElement;return e}function Pp(t,e,n){if(t.caretPositionFromPoint)try{let r=t.caretPositionFromPoint(e,n);if(r)return{node:r.offsetNode,offset:Math.min(De(r.offsetNode),r.offset)}}catch{}if(t.caretRangeFromPoint){let r=t.caretRangeFromPoint(e,n);if(r)return{node:r.startContainer,offset:Math.min(De(r.startContainer),r.startOffset)}}}var Je=typeof navigator<"u"?navigator:null,qa=typeof document<"u"?document:null,Et=Je&&Je.userAgent||"",ji=/Edge\/(\d+)/.exec(Et),Tc=/MSIE \d/.exec(Et),Ki=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(Et),ke=!!(Tc||Ki||ji),Tt=Tc?document.documentMode:Ki?+Ki[1]:ji?+ji[1]:0,Ve=!ke&&/gecko\/(\d+)/i.test(Et);Ve&&+(/Firefox\/(\d+)/.exec(Et)||[0,0])[1];var Ui=!ke&&/Chrome\/(\d+)/.exec(Et),ge=!!Ui,Ac=Ui?+Ui[1]:0,xe=!ke&&!!Je&&/Apple Computer/.test(Je.vendor),yn=xe&&(/Mobile\/\w+/.test(Et)||!!Je&&Je.maxTouchPoints>2),Re=yn||(Je?/Mac/.test(Je.platform):!1),Ip=Je?/Win/.test(Je.platform):!1,dt=/Android \d/.test(Et),Yn=!!qa&&"webkitFontSmoothing"in qa.documentElement.style,Lp=Yn?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0;function Bp(t){let e=t.defaultView&&t.defaultView.visualViewport;return e?{left:0,right:e.width,top:0,bottom:e.height}:{left:0,right:t.documentElement.clientWidth,top:0,bottom:t.documentElement.clientHeight}}function ct(t,e){return typeof t=="number"?t:t[e]}function zp(t){let e=t.getBoundingClientRect(),n=e.width/t.offsetWidth||1,r=e.height/t.offsetHeight||1;return{left:e.left,right:e.left+t.clientWidth*n,top:e.top,bottom:e.top+t.clientHeight*r}}function Ga(t,e,n){let r=t.someProp("scrollThreshold")||0,o=t.someProp("scrollMargin")||5,i=t.dom.ownerDocument;for(let s=n||t.dom;s;){if(s.nodeType!=1){s=gn(s);continue}let l=s,a=l==i.body,c=a?Bp(i):zp(l),u=0,d=0;if(e.top<c.top+ct(r,"top")?d=-(c.top-e.top+ct(o,"top")):e.bottom>c.bottom-ct(r,"bottom")&&(d=e.bottom-e.top>c.bottom-c.top?e.top+ct(o,"top")-c.top:e.bottom-c.bottom+ct(o,"bottom")),e.left<c.left+ct(r,"left")?u=-(c.left-e.left+ct(o,"left")):e.right>c.right-ct(r,"right")&&(u=e.right-c.right+ct(o,"right")),u||d)if(a)i.defaultView.scrollBy(u,d);else{let h=l.scrollLeft,p=l.scrollTop;d&&(l.scrollTop+=d),u&&(l.scrollLeft+=u);let m=l.scrollLeft-h,g=l.scrollTop-p;e={left:e.left-m,top:e.top-g,right:e.right-m,bottom:e.bottom-g}}let f=a?"fixed":getComputedStyle(s).position;if(/^(fixed|sticky)$/.test(f))break;s=f=="absolute"?s.offsetParent:gn(s)}}function Hp(t){let e=t.dom.getBoundingClientRect(),n=Math.max(0,e.top),r,o;for(let i=(e.left+e.right)/2,s=n+1;s<Math.min(innerHeight,e.bottom);s+=5){let l=t.root.elementFromPoint(i,s);if(!l||l==t.dom||!t.dom.contains(l))continue;let a=l.getBoundingClientRect();if(a.top>=n-20){r=l,o=a.top;break}}return{refDOM:r,refTop:o,stack:Ec(t.dom)}}function Ec(t){let e=[],n=t.ownerDocument;for(let r=t;r&&(e.push({dom:r,top:r.scrollTop,left:r.scrollLeft}),t!=n);r=gn(r));return e}function Fp({refDOM:t,refTop:e,stack:n}){let r=t?t.getBoundingClientRect().top:0;Oc(n,r==0?0:r-e)}function Oc(t,e){for(let n=0;n<t.length;n++){let{dom:r,top:o,left:i}=t[n];r.scrollTop!=o+e&&(r.scrollTop=o+e),r.scrollLeft!=i&&(r.scrollLeft=i)}}var fn=null;function $p(t){if(t.setActive)return t.setActive();if(fn)return t.focus(fn);let e=Ec(t);t.focus(fn==null?{get preventScroll(){return fn={preventScroll:!0},!0}}:void 0),fn||(fn=!1,Oc(e,0))}function Nc(t,e){let n,r=2e8,o,i=0,s=e.top,l=e.top,a,c;for(let u=t.firstChild,d=0;u;u=u.nextSibling,d++){let f;if(u.nodeType==1)f=u.getClientRects();else if(u.nodeType==3)f=ut(u).getClientRects();else continue;for(let h=0;h<f.length;h++){let p=f[h];if(p.top<=s&&p.bottom>=l){s=Math.max(p.bottom,s),l=Math.min(p.top,l);let m=p.left>e.left?p.left-e.left:p.right<e.left?e.left-p.right:0;if(m<r){n=u,r=m,o=m&&n.nodeType==3?{left:p.right<e.left?p.right:p.left,top:e.top}:e,u.nodeType==1&&m&&(i=d+(e.left>=(p.left+p.right)/2?1:0));continue}}else p.top>e.top&&!a&&p.left<=e.left&&p.right>=e.left&&(a=u,c={left:Math.max(p.left,Math.min(p.right,e.left)),top:p.top});!n&&(e.left>=p.right&&e.top>=p.top||e.left>=p.left&&e.top>=p.bottom)&&(i=d+1)}}return!n&&a&&(n=a,o=c,r=0),n&&n.nodeType==3?Vp(n,o):!n||r&&n.nodeType==1?{node:t,offset:i}:Nc(n,o)}function Vp(t,e){let n=t.nodeValue.length,r=document.createRange();for(let o=0;o<n;o++){r.setEnd(t,o+1),r.setStart(t,o);let i=Ct(r,1);if(i.top!=i.bottom&&as(e,i))return{node:t,offset:o+(e.left>=(i.left+i.right)/2?1:0)}}return{node:t,offset:0}}function as(t,e){return t.left>=e.left-1&&t.left<=e.right+1&&t.top>=e.top-1&&t.top<=e.bottom+1}function Wp(t,e){let n=t.parentNode;return n&&/^li$/i.test(n.nodeName)&&e.left<t.getBoundingClientRect().left?n:t}function _p(t,e,n){let{node:r,offset:o}=Nc(e,n),i=-1;if(r.nodeType==1&&!r.firstChild){let s=r.getBoundingClientRect();i=s.left!=s.right&&n.left>(s.left+s.right)/2?1:-1}return t.docView.posFromDOM(r,o,i)}function jp(t,e,n,r){let o=-1;for(let i=e,s=!1;i!=t.dom;){let l=t.docView.nearestDesc(i,!0),a;if(!l)return null;if(l.dom.nodeType==1&&(l.node.isBlock&&l.parent||!l.contentDOM)&&((a=l.dom.getBoundingClientRect()).width||a.height)&&(l.node.isBlock&&l.parent&&(!s&&a.left>r.left||a.top>r.top?o=l.posBefore:(!s&&a.right<r.left||a.bottom<r.top)&&(o=l.posAfter),s=!0),!l.contentDOM&&o<0&&!l.node.isText))return(l.node.isBlock?r.top<(a.top+a.bottom)/2:r.left<(a.left+a.right)/2)?l.posBefore:l.posAfter;i=l.dom.parentNode}return o>-1?o:t.docView.posFromDOM(e,n,-1)}function Rc(t,e,n){let r=t.childNodes.length;if(r&&n.top<n.bottom)for(let o=Math.max(0,Math.min(r-1,Math.floor(r*(e.top-n.top)/(n.bottom-n.top))-2)),i=o;;){let s=t.childNodes[i];if(s.nodeType==1){let l=s.getClientRects();for(let a=0;a<l.length;a++){let c=l[a];if(as(e,c))return Rc(s,e,c)}}if((i=(i+1)%r)==o)break}return t}function Kp(t,e){let n=t.dom.ownerDocument,r,o=0,i=Pp(n,e.left,e.top);i&&({node:r,offset:o}=i);let s=(t.root.elementFromPoint?t.root:n).elementFromPoint(e.left,e.top),l;if(!s||!t.dom.contains(s.nodeType!=1?s.parentNode:s)){let c=t.dom.getBoundingClientRect();if(!as(e,c)||(s=Rc(t.dom,e,c),!s))return null}if(xe)for(let c=s;r&&c;c=gn(c))c.draggable&&(r=void 0);if(s=Wp(s,e),r){if(Ve&&r.nodeType==1&&(o=Math.min(o,r.childNodes.length),o<r.childNodes.length)){let u=r.childNodes[o],d;u.nodeName=="IMG"&&(d=u.getBoundingClientRect()).right<=e.left&&d.bottom>e.top&&o++}let c;Yn&&o&&r.nodeType==1&&(c=r.childNodes[o-1]).nodeType==1&&c.contentEditable=="false"&&c.getBoundingClientRect().top>=e.top&&o--,r==t.dom&&o==r.childNodes.length-1&&r.lastChild.nodeType==1&&e.top>r.lastChild.getBoundingClientRect().bottom?l=t.state.doc.content.size:(o==0||r.nodeType!=1||r.childNodes[o-1].nodeName!="BR")&&(l=jp(t,r,o,e))}l==null&&(l=_p(t,s,e));let a=t.docView.nearestDesc(s,!0);return{pos:l,inside:a?a.posAtStart-a.border:-1}}function Ya(t){return t.top<t.bottom||t.left<t.right}function Ct(t,e){let n=t.getClientRects();if(n.length){let r=n[e<0?0:n.length-1];if(Ya(r))return r}return Array.prototype.find.call(n,Ya)||t.getBoundingClientRect()}var Up=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/;function Dc(t,e,n){let{node:r,offset:o,atom:i}=t.docView.domFromPos(e,n<0?-1:1),s=Yn||Ve;if(r.nodeType==3)if(s&&(Up.test(r.nodeValue)||(n<0?!o:o==r.nodeValue.length))){let a=Ct(ut(r,o,o),n);if(Ve&&o&&/\s/.test(r.nodeValue[o-1])&&o<r.nodeValue.length){let c=Ct(ut(r,o-1,o-1),-1);if(c.top==a.top){let u=Ct(ut(r,o,o+1),-1);if(u.top!=a.top)return Wn(u,u.left<c.left)}}return a}else{let a=o,c=o,u=n<0?1:-1;return n<0&&!o?(c++,u=-1):n>=0&&o==r.nodeValue.length?(a--,u=1):n<0?a--:c++,Wn(Ct(ut(r,a,c),u),u<0)}if(!t.state.doc.resolve(e-(i||0)).parent.inlineContent){if(i==null&&o&&(n<0||o==De(r))){let a=r.childNodes[o-1];if(a.nodeType==1)return Hi(a.getBoundingClientRect(),!1)}if(i==null&&o<De(r)){let a=r.childNodes[o];if(a.nodeType==1)return Hi(a.getBoundingClientRect(),!0)}return Hi(r.getBoundingClientRect(),n>=0)}if(i==null&&o&&(n<0||o==De(r))){let a=r.childNodes[o-1],c=a.nodeType==3?ut(a,De(a)-(s?0:1)):a.nodeType==1&&(a.nodeName!="BR"||!a.nextSibling)?a:null;if(c)return Wn(Ct(c,1),!1)}if(i==null&&o<De(r)){let a=r.childNodes[o];for(;a.pmViewDesc&&a.pmViewDesc.ignoreForCoords;)a=a.nextSibling;let c=a?a.nodeType==3?ut(a,0,s?0:1):a.nodeType==1?a:null:null;if(c)return Wn(Ct(c,-1),!0)}return Wn(Ct(r.nodeType==3?ut(r):r,-n),n>=0)}function Wn(t,e){if(t.width==0)return t;let n=e?t.left:t.right;return{top:t.top,bottom:t.bottom,left:n,right:n}}function Hi(t,e){if(t.height==0)return t;let n=e?t.top:t.bottom;return{top:n,bottom:n,left:t.left,right:t.right}}function Pc(t,e,n){let r=t.state,o=t.root.activeElement;r!=e&&t.updateState(e),o!=t.dom&&t.focus();try{return n()}finally{r!=e&&t.updateState(r),o!=t.dom&&o&&o.focus()}}function Jp(t,e,n){let r=e.selection,o=n=="up"?r.$from:r.$to;return Pc(t,e,()=>{let{node:i}=t.docView.domFromPos(o.pos,n=="up"?-1:1);for(;;){let l=t.docView.nearestDesc(i,!0);if(!l)break;if(l.node.isBlock){i=l.contentDOM||l.dom;break}i=l.dom.parentNode}let s=Dc(t,o.pos,1);for(let l=i.firstChild;l;l=l.nextSibling){let a;if(l.nodeType==1)a=l.getClientRects();else if(l.nodeType==3)a=ut(l,0,l.nodeValue.length).getClientRects();else continue;for(let c=0;c<a.length;c++){let u=a[c];if(u.bottom>u.top+1&&(n=="up"?s.top-u.top>(u.bottom-s.top)*2:u.bottom-s.bottom>(s.bottom-u.top)*2))return!1}}return!0})}var qp=/[\u0590-\u08ac]/;function Gp(t,e,n){let{$head:r}=e.selection;if(!r.parent.isTextblock)return!1;let o=r.parentOffset,i=!o,s=o==r.parent.content.size,l=t.domSelection();return l?!qp.test(r.parent.textContent)||!l.modify?n=="left"||n=="backward"?i:s:Pc(t,e,()=>{let{focusNode:a,focusOffset:c,anchorNode:u,anchorOffset:d}=t.domSelectionRange(),f=l.caretBidiLevel;l.modify("move",n,"character");let h=r.depth?t.docView.domAfterPos(r.before()):t.dom,{focusNode:p,focusOffset:m}=t.domSelectionRange(),g=p&&!h.contains(p.nodeType==1?p:p.parentNode)||a==p&&c==m;try{l.collapse(u,d),a&&(a!=u||c!=d)&&l.extend&&l.extend(a,c)}catch{}return f!=null&&(l.caretBidiLevel=f),g}):r.pos==r.start()||r.pos==r.end()}var Xa=null,Qa=null,Za=!1;function Yp(t,e,n){return Xa==e&&Qa==n?Za:(Xa=e,Qa=n,Za=n=="up"||n=="down"?Jp(t,e,n):Gp(t,e,n))}var Pe=0,ec=1,Wt=2,qe=3,qt=class{constructor(e,n,r,o){this.parent=e,this.children=n,this.dom=r,this.contentDOM=o,this.dirty=Pe,r.pmViewDesc=this}matchesWidget(e){return!1}matchesMark(e){return!1}matchesNode(e,n,r){return!1}matchesHack(e){return!1}parseRule(){return null}stopEvent(e){return!1}get size(){let e=0;for(let n=0;n<this.children.length;n++)e+=this.children[n].size;return e}get border(){return 0}destroy(){this.parent=void 0,this.dom.pmViewDesc==this&&(this.dom.pmViewDesc=void 0);for(let e=0;e<this.children.length;e++)this.children[e].destroy()}posBeforeChild(e){for(let n=0,r=this.posAtStart;;n++){let o=this.children[n];if(o==e)return r;r+=o.size}}get posBefore(){return this.parent.posBeforeChild(this)}get posAtStart(){return this.parent?this.parent.posBeforeChild(this)+this.border:0}get posAfter(){return this.posBefore+this.size}get posAtEnd(){return this.posAtStart+this.size-2*this.border}localPosFromDOM(e,n,r){if(this.contentDOM&&this.contentDOM.contains(e.nodeType==1?e:e.parentNode))if(r<0){let i,s;if(e==this.contentDOM)i=e.childNodes[n-1];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;i=e.previousSibling}for(;i&&!((s=i.pmViewDesc)&&s.parent==this);)i=i.previousSibling;return i?this.posBeforeChild(s)+s.size:this.posAtStart}else{let i,s;if(e==this.contentDOM)i=e.childNodes[n];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;i=e.nextSibling}for(;i&&!((s=i.pmViewDesc)&&s.parent==this);)i=i.nextSibling;return i?this.posBeforeChild(s):this.posAtEnd}let o;if(e==this.dom&&this.contentDOM)o=n>ue(this.contentDOM);else if(this.contentDOM&&this.contentDOM!=this.dom&&this.dom.contains(this.contentDOM))o=e.compareDocumentPosition(this.contentDOM)&2;else if(this.dom.firstChild){if(n==0)for(let i=e;;i=i.parentNode){if(i==this.dom){o=!1;break}if(i.previousSibling)break}if(o==null&&n==e.childNodes.length)for(let i=e;;i=i.parentNode){if(i==this.dom){o=!0;break}if(i.nextSibling)break}}return o??r>0?this.posAtEnd:this.posAtStart}nearestDesc(e,n=!1){for(let r=!0,o=e;o;o=o.parentNode){let i=this.getDesc(o),s;if(i&&(!n||i.node))if(r&&(s=i.nodeDOM)&&!(s.nodeType==1?s.contains(e.nodeType==1?e:e.parentNode):s==e))r=!1;else return i}}getDesc(e){let n=e.pmViewDesc;for(let r=n;r;r=r.parent)if(r==this)return n}posFromDOM(e,n,r){for(let o=e;o;o=o.parentNode){let i=this.getDesc(o);if(i)return i.localPosFromDOM(e,n,r)}return-1}descAt(e){for(let n=0,r=0;n<this.children.length;n++){let o=this.children[n],i=r+o.size;if(r==e&&i!=r){for(;!o.border&&o.children.length;)for(let s=0;s<o.children.length;s++){let l=o.children[s];if(l.size){o=l;break}}return o}if(e<i)return o.descAt(e-r-o.border);r=i}}domFromPos(e,n){if(!this.contentDOM)return{node:this.dom,offset:0,atom:e+1};let r=0,o=0;for(let i=0;r<this.children.length;r++){let s=this.children[r],l=i+s.size;if(l>e||s instanceof zr){o=e-i;break}i=l}if(o)return this.children[r].domFromPos(o-this.children[r].border,n);for(let i;r&&!(i=this.children[r-1]).size&&i instanceof Lr&&i.side>=0;r--);if(n<=0){let i,s=!0;for(;i=r?this.children[r-1]:null,!(!i||i.dom.parentNode==this.contentDOM);r--,s=!1);return i&&n&&s&&!i.border&&!i.domAtom?i.domFromPos(i.size,n):{node:this.contentDOM,offset:i?ue(i.dom)+1:0}}else{let i,s=!0;for(;i=r<this.children.length?this.children[r]:null,!(!i||i.dom.parentNode==this.contentDOM);r++,s=!1);return i&&s&&!i.border&&!i.domAtom?i.domFromPos(0,n):{node:this.contentDOM,offset:i?ue(i.dom):this.contentDOM.childNodes.length}}}parseRange(e,n,r=0){if(this.children.length==0)return{node:this.contentDOM,from:e,to:n,fromOffset:0,toOffset:this.contentDOM.childNodes.length};let o=-1,i=-1;for(let s=r,l=0;;l++){let a=this.children[l],c=s+a.size;if(o==-1&&e<=c){let u=s+a.border;if(e>=u&&n<=c-a.border&&a.node&&a.contentDOM&&this.contentDOM.contains(a.contentDOM))return a.parseRange(e,n,u);e=s;for(let d=l;d>0;d--){let f=this.children[d-1];if(f.size&&f.dom.parentNode==this.contentDOM&&!f.emptyChildAt(1)){o=ue(f.dom)+1;break}e-=f.size}o==-1&&(o=0)}if(o>-1&&(c>n||l==this.children.length-1)){n=c;for(let u=l+1;u<this.children.length;u++){let d=this.children[u];if(d.size&&d.dom.parentNode==this.contentDOM&&!d.emptyChildAt(-1)){i=ue(d.dom);break}n+=d.size}i==-1&&(i=this.contentDOM.childNodes.length);break}s=c}return{node:this.contentDOM,from:e,to:n,fromOffset:o,toOffset:i}}emptyChildAt(e){if(this.border||!this.contentDOM||!this.children.length)return!1;let n=this.children[e<0?0:this.children.length-1];return n.size==0||n.emptyChildAt(e)}domAfterPos(e){let{node:n,offset:r}=this.domFromPos(e,0);if(n.nodeType!=1||r==n.childNodes.length)throw new RangeError("No node after pos "+e);return n.childNodes[r]}setSelection(e,n,r,o=!1){let i=Math.min(e,n),s=Math.max(e,n);for(let h=0,p=0;h<this.children.length;h++){let m=this.children[h],g=p+m.size;if(i>p&&s<g)return m.setSelection(e-p-m.border,n-p-m.border,r,o);p=g}let l=this.domFromPos(e,e?-1:1),a=n==e?l:this.domFromPos(n,n?-1:1),c=r.root.getSelection(),u=r.domSelectionRange(),d=!1;if((Ve||xe)&&e==n){let{node:h,offset:p}=l;if(h.nodeType==3){if(d=!!(p&&h.nodeValue[p-1]==`
`),d&&p==h.nodeValue.length)for(let m=h,g;m;m=m.parentNode){if(g=m.nextSibling){g.nodeName=="BR"&&(l=a={node:g.parentNode,offset:ue(g)+1});break}let y=m.pmViewDesc;if(y&&y.node&&y.node.isBlock)break}}else{let m=h.childNodes[p-1];d=m&&(m.nodeName=="BR"||m.contentEditable=="false")}}if(Ve&&u.focusNode&&u.focusNode!=a.node&&u.focusNode.nodeType==1){let h=u.focusNode.childNodes[u.focusOffset];h&&h.contentEditable=="false"&&(o=!0)}if(!(o||d&&xe)&&Jt(l.node,l.offset,u.anchorNode,u.anchorOffset)&&Jt(a.node,a.offset,u.focusNode,u.focusOffset))return;let f=!1;if((c.extend||e==n)&&!d){c.collapse(l.node,l.offset);try{e!=n&&c.extend(a.node,a.offset),f=!0}catch{}}if(!f){if(e>n){let p=l;l=a,a=p}let h=document.createRange();h.setEnd(a.node,a.offset),h.setStart(l.node,l.offset),c.removeAllRanges(),c.addRange(h)}}ignoreMutation(e){return!this.contentDOM&&e.type!="selection"}get contentLost(){return this.contentDOM&&this.contentDOM!=this.dom&&!this.dom.contains(this.contentDOM)}markDirty(e,n){for(let r=0,o=0;o<this.children.length;o++){let i=this.children[o],s=r+i.size;if(r==s?e<=s&&n>=r:e<s&&n>r){let l=r+i.border,a=s-i.border;if(e>=l&&n<=a){this.dirty=e==r||n==s?Wt:ec,e==l&&n==a&&(i.contentLost||i.dom.parentNode!=this.contentDOM)?i.dirty=qe:i.markDirty(e-l,n-l);return}else i.dirty=i.dom==i.contentDOM&&i.dom.parentNode==this.contentDOM&&!i.children.length?Wt:qe}r=s}this.dirty=Wt}markParentsDirty(){let e=1;for(let n=this.parent;n;n=n.parent,e++){let r=e==1?Wt:ec;n.dirty<r&&(n.dirty=r)}}get domAtom(){return!1}get ignoreForCoords(){return!1}get ignoreForSelection(){return!1}isText(e){return!1}},Lr=class extends qt{constructor(e,n,r,o){let i,s=n.type.toDOM;if(typeof s=="function"&&(s=s(r,()=>{if(!i)return o;if(i.parent)return i.parent.posBeforeChild(i)})),!n.type.spec.raw){if(s.nodeType!=1){let l=document.createElement("span");l.appendChild(s),s=l}s.contentEditable="false",s.classList.add("ProseMirror-widget")}super(e,[],s,null),this.widget=n,this.widget=n,i=this}matchesWidget(e){return this.dirty==Pe&&e.type.eq(this.widget.type)}parseRule(){return{ignore:!0}}stopEvent(e){let n=this.widget.spec.stopEvent;return n?n(e):!1}ignoreMutation(e){return e.type!="selection"||this.widget.spec.ignoreSelection}destroy(){this.widget.type.destroy(this.dom),super.destroy()}get domAtom(){return!0}get ignoreForSelection(){return!!this.widget.type.spec.relaxedSide}get side(){return this.widget.type.side}},Ji=class extends qt{constructor(e,n,r,o){super(e,[],n,null),this.textDOM=r,this.text=o}get size(){return this.text.length}localPosFromDOM(e,n){return e!=this.textDOM?this.posAtStart+(n?this.size:0):this.posAtStart+n}domFromPos(e){return{node:this.textDOM,offset:e}}ignoreMutation(e){return e.type==="characterData"&&e.target.nodeValue==e.oldValue}},bn=class t extends qt{constructor(e,n,r,o,i){super(e,[],r,o),this.mark=n,this.spec=i}static create(e,n,r,o){let i=o.nodeViews[n.type.name],s=i&&i(n,o,r);return(!s||!s.dom)&&(s=it.renderSpec(document,n.type.spec.toDOM(n,r),null,n.attrs)),new t(e,n,s.dom,s.contentDOM||s.dom,s)}parseRule(){return this.dirty&qe||this.mark.type.spec.reparseInView?null:{mark:this.mark.type.name,attrs:this.mark.attrs,contentElement:this.contentDOM}}matchesMark(e){return this.dirty!=qe&&this.mark.eq(e)}markDirty(e,n){if(super.markDirty(e,n),this.dirty!=Pe){let r=this.parent;for(;!r.node;)r=r.parent;r.dirty<this.dirty&&(r.dirty=this.dirty),this.dirty=Pe}}slice(e,n,r){let o=t.create(this.parent,this.mark,!0,r),i=this.children,s=this.size;n<s&&(i=Xi(i,n,s,r)),e>0&&(i=Xi(i,0,e,r));for(let l=0;l<i.length;l++)i[l].parent=o;return o.children=i,o}ignoreMutation(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):super.ignoreMutation(e)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}},At=class t extends qt{constructor(e,n,r,o,i,s,l,a,c){super(e,[],i,s),this.node=n,this.outerDeco=r,this.innerDeco=o,this.nodeDOM=l}static create(e,n,r,o,i,s){let l=i.nodeViews[n.type.name],a,c=l&&l(n,i,()=>{if(!a)return s;if(a.parent)return a.parent.posBeforeChild(a)},r,o),u=c&&c.dom,d=c&&c.contentDOM;if(n.isText){if(!u)u=document.createTextNode(n.text);else if(u.nodeType!=3)throw new RangeError("Text must be rendered as a DOM text node")}else u||({dom:u,contentDOM:d}=it.renderSpec(document,n.type.spec.toDOM(n),null,n.attrs));!d&&!n.isText&&u.nodeName!="BR"&&(u.hasAttribute("contenteditable")||(u.contentEditable="false"),n.type.spec.draggable&&(u.draggable=!0));let f=u;return u=Bc(u,r,n),c?a=new qi(e,n,r,o,u,d||null,f,c,i,s+1):n.isText?new Br(e,n,r,o,u,f,i):new t(e,n,r,o,u,d||null,f,i,s+1)}parseRule(){if(this.node.type.spec.reparseInView)return null;let e={node:this.node.type.name,attrs:this.node.attrs};if(this.node.type.whitespace=="pre"&&(e.preserveWhitespace="full"),!this.contentDOM)e.getContent=()=>this.node.content;else if(!this.contentLost)e.contentElement=this.contentDOM;else{for(let n=this.children.length-1;n>=0;n--){let r=this.children[n];if(this.dom.contains(r.dom.parentNode)){e.contentElement=r.dom.parentNode;break}}e.contentElement||(e.getContent=()=>w.empty)}return e}matchesNode(e,n,r){return this.dirty==Pe&&e.eq(this.node)&&Hr(n,this.outerDeco)&&r.eq(this.innerDeco)}get size(){return this.node.nodeSize}get border(){return this.node.isLeaf?0:1}updateChildren(e,n){let r=this.node.inlineContent,o=n,i=e.composing?this.localCompositionInfo(e,n):null,s=i&&i.pos>-1?i:null,l=i&&i.pos<0,a=new Yi(this,s&&s.node,e);em(this.node,this.innerDeco,(c,u,d)=>{c.spec.marks?a.syncToMarks(c.spec.marks,r,e):c.type.side>=0&&!d&&a.syncToMarks(u==this.node.childCount?V.none:this.node.child(u).marks,r,e),a.placeWidget(c,e,o)},(c,u,d,f)=>{a.syncToMarks(c.marks,r,e);let h;a.findNodeMatch(c,u,d,f)||l&&e.state.selection.from>o&&e.state.selection.to<o+c.nodeSize&&(h=a.findIndexWithChild(i.node))>-1&&a.updateNodeAt(c,u,d,h,e)||a.updateNextNode(c,u,d,e,f,o)||a.addNode(c,u,d,e,o),o+=c.nodeSize}),a.syncToMarks([],r,e),this.node.isTextblock&&a.addTextblockHacks(),a.destroyRest(),(a.changed||this.dirty==Wt)&&(s&&this.protectLocalComposition(e,s),Ic(this.contentDOM,this.children,e),yn&&tm(this.dom))}localCompositionInfo(e,n){let{from:r,to:o}=e.state.selection;if(!(e.state.selection instanceof A)||r<n||o>n+this.node.content.size)return null;let i=e.input.compositionNode;if(!i||!this.dom.contains(i.parentNode))return null;if(this.node.inlineContent){let s=i.nodeValue,l=nm(this.node.content,s,r-n,o-n);return l<0?null:{node:i,pos:l,text:s}}else return{node:i,pos:-1,text:""}}protectLocalComposition(e,{node:n,pos:r,text:o}){if(this.getDesc(n))return;let i=n;for(;i.parentNode!=this.contentDOM;i=i.parentNode){for(;i.previousSibling;)i.parentNode.removeChild(i.previousSibling);for(;i.nextSibling;)i.parentNode.removeChild(i.nextSibling);i.pmViewDesc&&(i.pmViewDesc=void 0)}let s=new Ji(this,i,n,o);e.input.compositionNodes.push(s),this.children=Xi(this.children,r,r+o.length,e,s)}update(e,n,r,o){return this.dirty==qe||!e.sameMarkup(this.node)?!1:(this.updateInner(e,n,r,o),!0)}updateInner(e,n,r,o){this.updateOuterDeco(n),this.node=e,this.innerDeco=r,this.contentDOM&&this.updateChildren(o,this.posAtStart),this.dirty=Pe}updateOuterDeco(e){if(Hr(e,this.outerDeco))return;let n=this.nodeDOM.nodeType!=1,r=this.dom;this.dom=Lc(this.dom,this.nodeDOM,Gi(this.outerDeco,this.node,n),Gi(e,this.node,n)),this.dom!=r&&(r.pmViewDesc=void 0,this.dom.pmViewDesc=this),this.outerDeco=e}selectNode(){this.nodeDOM.nodeType==1&&this.nodeDOM.classList.add("ProseMirror-selectednode"),(this.contentDOM||!this.node.type.spec.draggable)&&(this.dom.draggable=!0)}deselectNode(){this.nodeDOM.nodeType==1&&(this.nodeDOM.classList.remove("ProseMirror-selectednode"),(this.contentDOM||!this.node.type.spec.draggable)&&this.dom.removeAttribute("draggable"))}get domAtom(){return this.node.isAtom}};function tc(t,e,n,r,o){Bc(r,e,t);let i=new At(void 0,t,e,n,r,r,r,o,0);return i.contentDOM&&i.updateChildren(o,0),i}var Br=class t extends At{constructor(e,n,r,o,i,s,l){super(e,n,r,o,i,null,s,l,0)}parseRule(){let e=this.nodeDOM.parentNode;for(;e&&e!=this.dom&&!e.pmIsDeco;)e=e.parentNode;return{skip:e||!0}}update(e,n,r,o){return this.dirty==qe||this.dirty!=Pe&&!this.inParent()||!e.sameMarkup(this.node)?!1:(this.updateOuterDeco(n),(this.dirty!=Pe||e.text!=this.node.text)&&e.text!=this.nodeDOM.nodeValue&&(this.nodeDOM.nodeValue=e.text,o.trackWrites==this.nodeDOM&&(o.trackWrites=null)),this.node=e,this.dirty=Pe,!0)}inParent(){let e=this.parent.contentDOM;for(let n=this.nodeDOM;n;n=n.parentNode)if(n==e)return!0;return!1}domFromPos(e){return{node:this.nodeDOM,offset:e}}localPosFromDOM(e,n,r){return e==this.nodeDOM?this.posAtStart+Math.min(n,this.node.text.length):super.localPosFromDOM(e,n,r)}ignoreMutation(e){return e.type!="characterData"&&e.type!="selection"}slice(e,n,r){let o=this.node.cut(e,n),i=document.createTextNode(o.text);return new t(this.parent,o,this.outerDeco,this.innerDeco,i,i,r)}markDirty(e,n){super.markDirty(e,n),this.dom!=this.nodeDOM&&(e==0||n==this.nodeDOM.nodeValue.length)&&(this.dirty=qe)}get domAtom(){return!1}isText(e){return this.node.text==e}},zr=class extends qt{parseRule(){return{ignore:!0}}matchesHack(e){return this.dirty==Pe&&this.dom.nodeName==e}get domAtom(){return!0}get ignoreForCoords(){return this.dom.nodeName=="IMG"}},qi=class extends At{constructor(e,n,r,o,i,s,l,a,c,u){super(e,n,r,o,i,s,l,c,u),this.spec=a}update(e,n,r,o){if(this.dirty==qe)return!1;if(this.spec.update&&(this.node.type==e.type||this.spec.multiType)){let i=this.spec.update(e,n,r);return i&&this.updateInner(e,n,r,o),i}else return!this.contentDOM&&!e.isLeaf?!1:super.update(e,n,r,o)}selectNode(){this.spec.selectNode?this.spec.selectNode():super.selectNode()}deselectNode(){this.spec.deselectNode?this.spec.deselectNode():super.deselectNode()}setSelection(e,n,r,o){this.spec.setSelection?this.spec.setSelection(e,n,r.root):super.setSelection(e,n,r,o)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}stopEvent(e){return this.spec.stopEvent?this.spec.stopEvent(e):!1}ignoreMutation(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):super.ignoreMutation(e)}};function Ic(t,e,n){let r=t.firstChild,o=!1;for(let i=0;i<e.length;i++){let s=e[i],l=s.dom;if(l.parentNode==t){for(;l!=r;)r=nc(r),o=!0;r=r.nextSibling}else o=!0,t.insertBefore(l,r);if(s instanceof bn){let a=r?r.previousSibling:t.lastChild;Ic(s.contentDOM,s.children,n),r=a?a.nextSibling:t.firstChild}}for(;r;)r=nc(r),o=!0;o&&n.trackWrites==t&&(n.trackWrites=null)}var _n=function(t){t&&(this.nodeName=t)};_n.prototype=Object.create(null);var _t=[new _n];function Gi(t,e,n){if(t.length==0)return _t;let r=n?_t[0]:new _n,o=[r];for(let i=0;i<t.length;i++){let s=t[i].type.attrs;if(s){s.nodeName&&o.push(r=new _n(s.nodeName));for(let l in s){let a=s[l];a!=null&&(n&&o.length==1&&o.push(r=new _n(e.isInline?"span":"div")),l=="class"?r.class=(r.class?r.class+" ":"")+a:l=="style"?r.style=(r.style?r.style+";":"")+a:l!="nodeName"&&(r[l]=a))}}}return o}function Lc(t,e,n,r){if(n==_t&&r==_t)return e;let o=e;for(let i=0;i<r.length;i++){let s=r[i],l=n[i];if(i){let a;l&&l.nodeName==s.nodeName&&o!=t&&(a=o.parentNode)&&a.nodeName.toLowerCase()==s.nodeName||(a=document.createElement(s.nodeName),a.pmIsDeco=!0,a.appendChild(o),l=_t[0]),o=a}Xp(o,l||_t[0],s)}return o}function Xp(t,e,n){for(let r in e)r!="class"&&r!="style"&&r!="nodeName"&&!(r in n)&&t.removeAttribute(r);for(let r in n)r!="class"&&r!="style"&&r!="nodeName"&&n[r]!=e[r]&&t.setAttribute(r,n[r]);if(e.class!=n.class){let r=e.class?e.class.split(" ").filter(Boolean):[],o=n.class?n.class.split(" ").filter(Boolean):[];for(let i=0;i<r.length;i++)o.indexOf(r[i])==-1&&t.classList.remove(r[i]);for(let i=0;i<o.length;i++)r.indexOf(o[i])==-1&&t.classList.add(o[i]);t.classList.length==0&&t.removeAttribute("class")}if(e.style!=n.style){if(e.style){let r=/\s*([\w\-\xa1-\uffff]+)\s*:(?:"(?:\\.|[^"])*"|'(?:\\.|[^'])*'|\(.*?\)|[^;])*/g,o;for(;o=r.exec(e.style);)t.style.removeProperty(o[1])}n.style&&(t.style.cssText+=n.style)}}function Bc(t,e,n){return Lc(t,t,_t,Gi(e,n,t.nodeType!=1))}function Hr(t,e){if(t.length!=e.length)return!1;for(let n=0;n<t.length;n++)if(!t[n].type.eq(e[n].type))return!1;return!0}function nc(t){let e=t.nextSibling;return t.parentNode.removeChild(t),e}var Yi=class{constructor(e,n,r){this.lock=n,this.view=r,this.index=0,this.stack=[],this.changed=!1,this.top=e,this.preMatch=Qp(e.node.content,e)}destroyBetween(e,n){if(e!=n){for(let r=e;r<n;r++)this.top.children[r].destroy();this.top.children.splice(e,n-e),this.changed=!0}}destroyRest(){this.destroyBetween(this.index,this.top.children.length)}syncToMarks(e,n,r){let o=0,i=this.stack.length>>1,s=Math.min(i,e.length);for(;o<s&&(o==i-1?this.top:this.stack[o+1<<1]).matchesMark(e[o])&&e[o].type.spec.spanning!==!1;)o++;for(;o<i;)this.destroyRest(),this.top.dirty=Pe,this.index=this.stack.pop(),this.top=this.stack.pop(),i--;for(;i<e.length;){this.stack.push(this.top,this.index+1);let l=-1;for(let a=this.index;a<Math.min(this.index+3,this.top.children.length);a++){let c=this.top.children[a];if(c.matchesMark(e[i])&&!this.isLocked(c.dom)){l=a;break}}if(l>-1)l>this.index&&(this.changed=!0,this.destroyBetween(this.index,l)),this.top=this.top.children[this.index];else{let a=bn.create(this.top,e[i],n,r);this.top.children.splice(this.index,0,a),this.top=a,this.changed=!0}this.index=0,i++}}findNodeMatch(e,n,r,o){let i=-1,s;if(o>=this.preMatch.index&&(s=this.preMatch.matches[o-this.preMatch.index]).parent==this.top&&s.matchesNode(e,n,r))i=this.top.children.indexOf(s,this.index);else for(let l=this.index,a=Math.min(this.top.children.length,l+5);l<a;l++){let c=this.top.children[l];if(c.matchesNode(e,n,r)&&!this.preMatch.matched.has(c)){i=l;break}}return i<0?!1:(this.destroyBetween(this.index,i),this.index++,!0)}updateNodeAt(e,n,r,o,i){let s=this.top.children[o];return s.dirty==qe&&s.dom==s.contentDOM&&(s.dirty=Wt),s.update(e,n,r,i)?(this.destroyBetween(this.index,o),this.index++,!0):!1}findIndexWithChild(e){for(;;){let n=e.parentNode;if(!n)return-1;if(n==this.top.contentDOM){let r=e.pmViewDesc;if(r){for(let o=this.index;o<this.top.children.length;o++)if(this.top.children[o]==r)return o}return-1}e=n}}updateNextNode(e,n,r,o,i,s){for(let l=this.index;l<this.top.children.length;l++){let a=this.top.children[l];if(a instanceof At){let c=this.preMatch.matched.get(a);if(c!=null&&c!=i)return!1;let u=a.dom,d,f=this.isLocked(u)&&!(e.isText&&a.node&&a.node.isText&&a.nodeDOM.nodeValue==e.text&&a.dirty!=qe&&Hr(n,a.outerDeco));if(!f&&a.update(e,n,r,o))return this.destroyBetween(this.index,l),a.dom!=u&&(this.changed=!0),this.index++,!0;if(!f&&(d=this.recreateWrapper(a,e,n,r,o,s)))return this.destroyBetween(this.index,l),this.top.children[this.index]=d,d.contentDOM&&(d.dirty=Wt,d.updateChildren(o,s+1),d.dirty=Pe),this.changed=!0,this.index++,!0;break}}return!1}recreateWrapper(e,n,r,o,i,s){if(e.dirty||n.isAtom||!e.children.length||!e.node.content.eq(n.content)||!Hr(r,e.outerDeco)||!o.eq(e.innerDeco))return null;let l=At.create(this.top,n,r,o,i,s);if(l.contentDOM){l.children=e.children,e.children=[];for(let a of l.children)a.parent=l}return e.destroy(),l}addNode(e,n,r,o,i){let s=At.create(this.top,e,n,r,o,i);s.contentDOM&&s.updateChildren(o,i+1),this.top.children.splice(this.index++,0,s),this.changed=!0}placeWidget(e,n,r){let o=this.index<this.top.children.length?this.top.children[this.index]:null;if(o&&o.matchesWidget(e)&&(e==o.widget||!o.widget.type.toDOM.parentNode))this.index++;else{let i=new Lr(this.top,e,n,r);this.top.children.splice(this.index++,0,i),this.changed=!0}}addTextblockHacks(){let e=this.top.children[this.index-1],n=this.top;for(;e instanceof bn;)n=e,e=n.children[n.children.length-1];(!e||!(e instanceof Br)||/\n$/.test(e.node.text)||this.view.requiresGeckoHackNode&&/\s$/.test(e.node.text))&&((xe||ge)&&e&&e.dom.contentEditable=="false"&&this.addHackNode("IMG",n),this.addHackNode("BR",this.top))}addHackNode(e,n){if(n==this.top&&this.index<n.children.length&&n.children[this.index].matchesHack(e))this.index++;else{let r=document.createElement(e);e=="IMG"&&(r.className="ProseMirror-separator",r.alt=""),e=="BR"&&(r.className="ProseMirror-trailingBreak");let o=new zr(this.top,[],r,null);n!=this.top?n.children.push(o):n.children.splice(this.index++,0,o),this.changed=!0}}isLocked(e){return this.lock&&(e==this.lock||e.nodeType==1&&e.contains(this.lock.parentNode))}};function Qp(t,e){let n=e,r=n.children.length,o=t.childCount,i=new Map,s=[];e:for(;o>0;){let l;for(;;)if(r){let c=n.children[r-1];if(c instanceof bn)n=c,r=c.children.length;else{l=c,r--;break}}else{if(n==e)break e;r=n.parent.children.indexOf(n),n=n.parent}let a=l.node;if(a){if(a!=t.child(o-1))break;--o,i.set(l,o),s.push(l)}}return{index:o,matched:i,matches:s.reverse()}}function Zp(t,e){return t.type.side-e.type.side}function em(t,e,n,r){let o=e.locals(t),i=0;if(o.length==0){for(let c=0;c<t.childCount;c++){let u=t.child(c);r(u,o,e.forChild(i,u),c),i+=u.nodeSize}return}let s=0,l=[],a=null;for(let c=0;;){let u,d;for(;s<o.length&&o[s].to==i;){let g=o[s++];g.widget&&(u?(d||(d=[u])).push(g):u=g)}if(u)if(d){d.sort(Zp);for(let g=0;g<d.length;g++)n(d[g],c,!!a)}else n(u,c,!!a);let f,h;if(a)h=-1,f=a,a=null;else if(c<t.childCount)h=c,f=t.child(c++);else break;for(let g=0;g<l.length;g++)l[g].to<=i&&l.splice(g--,1);for(;s<o.length&&o[s].from<=i&&o[s].to>i;)l.push(o[s++]);let p=i+f.nodeSize;if(f.isText){let g=p;s<o.length&&o[s].from<g&&(g=o[s].from);for(let y=0;y<l.length;y++)l[y].to<g&&(g=l[y].to);g<p&&(a=f.cut(g-i),f=f.cut(0,g-i),p=g,h=-1)}else for(;s<o.length&&o[s].to<p;)s++;let m=f.isInline&&!f.isLeaf?l.filter(g=>!g.inline):l.slice();r(f,m,e.forChild(i,f),h),i=p}}function tm(t){if(t.nodeName=="UL"||t.nodeName=="OL"){let e=t.style.cssText;t.style.cssText=e+"; list-style: square !important",window.getComputedStyle(t).listStyle,t.style.cssText=e}}function nm(t,e,n,r){for(let o=0,i=0;o<t.childCount&&i<=r;){let s=t.child(o++),l=i;if(i+=s.nodeSize,!s.isText)continue;let a=s.text;for(;o<t.childCount;){let c=t.child(o++);if(i+=c.nodeSize,!c.isText)break;a+=c.text}if(i>=n){if(i>=r&&a.slice(r-e.length-l,r-l)==e)return r-e.length;let c=l<r?a.lastIndexOf(e,r-l-1):-1;if(c>=0&&c+e.length+l>=n)return l+c;if(n==r&&a.length>=r+e.length-l&&a.slice(r-l,r-l+e.length)==e)return r}}return-1}function Xi(t,e,n,r,o){let i=[];for(let s=0,l=0;s<t.length;s++){let a=t[s],c=l,u=l+=a.size;c>=n||u<=e?i.push(a):(c<e&&i.push(a.slice(0,e-c,r)),o&&(i.push(o),o=void 0),u>n&&i.push(a.slice(n-c,a.size,r)))}return i}function cs(t,e=null){let n=t.domSelectionRange(),r=t.state.doc;if(!n.focusNode)return null;let o=t.docView.nearestDesc(n.focusNode),i=o&&o.size==0,s=t.docView.posFromDOM(n.focusNode,n.focusOffset,1);if(s<0)return null;let l=r.resolve(s),a,c;if(Kr(n)){for(a=s;o&&!o.node;)o=o.parent;let d=o.node;if(o&&d.isAtom&&O.isSelectable(d)&&o.parent&&!(d.isInline&&Rp(n.focusNode,n.focusOffset,o.dom))){let f=o.posBefore;c=new O(s==f?l:r.resolve(f))}}else{if(n instanceof t.dom.ownerDocument.defaultView.Selection&&n.rangeCount>1){let d=s,f=s;for(let h=0;h<n.rangeCount;h++){let p=n.getRangeAt(h);d=Math.min(d,t.docView.posFromDOM(p.startContainer,p.startOffset,1)),f=Math.max(f,t.docView.posFromDOM(p.endContainer,p.endOffset,-1))}if(d<0)return null;[a,s]=f==t.state.selection.anchor?[f,d]:[d,f],l=r.resolve(s)}else a=t.docView.posFromDOM(n.anchorNode,n.anchorOffset,1);if(a<0)return null}let u=r.resolve(a);if(!c){let d=e=="pointer"||t.state.selection.head<l.pos&&!i?1:-1;c=us(t,u,l,d)}return c}function zc(t){return t.editable?t.hasFocus():Fc(t)&&document.activeElement&&document.activeElement.contains(t.dom)}function ft(t,e=!1){let n=t.state.selection;if(Hc(t,n),!!zc(t)){if(!e&&t.input.mouseDown&&t.input.mouseDown.allowDefault&&ge){let r=t.domSelectionRange(),o=t.domObserver.currentSelection;if(r.anchorNode&&o.anchorNode&&Jt(r.anchorNode,r.anchorOffset,o.anchorNode,o.anchorOffset)){t.input.mouseDown.delayedSelectionSync=!0,t.domObserver.setCurSelection();return}}if(t.domObserver.disconnectSelection(),t.cursorWrapper)om(t);else{let{anchor:r,head:o}=n,i,s;rc&&!(n instanceof A)&&(n.$from.parent.inlineContent||(i=oc(t,n.from)),!n.empty&&!n.$from.parent.inlineContent&&(s=oc(t,n.to))),t.docView.setSelection(r,o,t,e),rc&&(i&&ic(i),s&&ic(s)),n.visible?t.dom.classList.remove("ProseMirror-hideselection"):(t.dom.classList.add("ProseMirror-hideselection"),"onselectionchange"in document&&rm(t))}t.domObserver.setCurSelection(),t.domObserver.connectSelection()}}var rc=xe||ge&&Ac<63;function oc(t,e){let{node:n,offset:r}=t.docView.domFromPos(e,0),o=r<n.childNodes.length?n.childNodes[r]:null,i=r?n.childNodes[r-1]:null;if(xe&&o&&o.contentEditable=="false")return Fi(o);if((!o||o.contentEditable=="false")&&(!i||i.contentEditable=="false")){if(o)return Fi(o);if(i)return Fi(i)}}function Fi(t){return t.contentEditable="true",xe&&t.draggable&&(t.draggable=!1,t.wasDraggable=!0),t}function ic(t){t.contentEditable="false",t.wasDraggable&&(t.draggable=!0,t.wasDraggable=null)}function rm(t){let e=t.dom.ownerDocument;e.removeEventListener("selectionchange",t.input.hideSelectionGuard);let n=t.domSelectionRange(),r=n.anchorNode,o=n.anchorOffset;e.addEventListener("selectionchange",t.input.hideSelectionGuard=()=>{(n.anchorNode!=r||n.anchorOffset!=o)&&(e.removeEventListener("selectionchange",t.input.hideSelectionGuard),setTimeout(()=>{(!zc(t)||t.state.selection.visible)&&t.dom.classList.remove("ProseMirror-hideselection")},20))})}function om(t){let e=t.domSelection(),n=document.createRange();if(!e)return;let r=t.cursorWrapper.dom,o=r.nodeName=="IMG";o?n.setStart(r.parentNode,ue(r)+1):n.setStart(r,0),n.collapse(!0),e.removeAllRanges(),e.addRange(n),!o&&!t.state.selection.visible&&ke&&Tt<=11&&(r.disabled=!0,r.disabled=!1)}function Hc(t,e){if(e instanceof O){let n=t.docView.descAt(e.from);n!=t.lastSelectedViewDesc&&(sc(t),n&&n.selectNode(),t.lastSelectedViewDesc=n)}else sc(t)}function sc(t){t.lastSelectedViewDesc&&(t.lastSelectedViewDesc.parent&&t.lastSelectedViewDesc.deselectNode(),t.lastSelectedViewDesc=void 0)}function us(t,e,n,r){return t.someProp("createSelectionBetween",o=>o(t,e,n))||A.between(e,n,r)}function lc(t){return t.editable&&!t.hasFocus()?!1:Fc(t)}function Fc(t){let e=t.domSelectionRange();if(!e.anchorNode)return!1;try{return t.dom.contains(e.anchorNode.nodeType==3?e.anchorNode.parentNode:e.anchorNode)&&(t.editable||t.dom.contains(e.focusNode.nodeType==3?e.focusNode.parentNode:e.focusNode))}catch{return!1}}function im(t){let e=t.docView.domFromPos(t.state.selection.anchor,0),n=t.domSelectionRange();return Jt(e.node,e.offset,n.anchorNode,n.anchorOffset)}function Qi(t,e){let{$anchor:n,$head:r}=t.selection,o=e>0?n.max(r):n.min(r),i=o.parent.inlineContent?o.depth?t.doc.resolve(e>0?o.after():o.before()):null:o;return i&&E.findFrom(i,e)}function Mt(t,e){return t.dispatch(t.state.tr.setSelection(e).scrollIntoView()),!0}function ac(t,e,n){let r=t.state.selection;if(r instanceof A)if(n.indexOf("s")>-1){let{$head:o}=r,i=o.textOffset?null:e<0?o.nodeBefore:o.nodeAfter;if(!i||i.isText||!i.isLeaf)return!1;let s=t.state.doc.resolve(o.pos+i.nodeSize*(e<0?-1:1));return Mt(t,new A(r.$anchor,s))}else if(r.empty){if(t.endOfTextblock(e>0?"forward":"backward")){let o=Qi(t.state,e);return o&&o instanceof O?Mt(t,o):!1}else if(!(Re&&n.indexOf("m")>-1)){let o=r.$head,i=o.textOffset?null:e<0?o.nodeBefore:o.nodeAfter,s;if(!i||i.isText)return!1;let l=e<0?o.pos-i.nodeSize:o.pos;return i.isAtom||(s=t.docView.descAt(l))&&!s.contentDOM?O.isSelectable(i)?Mt(t,new O(e<0?t.state.doc.resolve(o.pos-i.nodeSize):o)):Yn?Mt(t,new A(t.state.doc.resolve(e<0?l:l+i.nodeSize))):!1:!1}}else return!1;else{if(r instanceof O&&r.node.isInline)return Mt(t,new A(e>0?r.$to:r.$from));{let o=Qi(t.state,e);return o?Mt(t,o):!1}}}function Fr(t){return t.nodeType==3?t.nodeValue.length:t.childNodes.length}function jn(t,e){let n=t.pmViewDesc;return n&&n.size==0&&(e<0||t.nextSibling||t.nodeName!="BR")}function hn(t,e){return e<0?sm(t):lm(t)}function sm(t){let e=t.domSelectionRange(),n=e.focusNode,r=e.focusOffset;if(!n)return;let o,i,s=!1;for(Ve&&n.nodeType==1&&r<Fr(n)&&jn(n.childNodes[r],-1)&&(s=!0);;)if(r>0){if(n.nodeType!=1)break;{let l=n.childNodes[r-1];if(jn(l,-1))o=n,i=--r;else if(l.nodeType==3)n=l,r=n.nodeValue.length;else break}}else{if($c(n))break;{let l=n.previousSibling;for(;l&&jn(l,-1);)o=n.parentNode,i=ue(l),l=l.previousSibling;if(l)n=l,r=Fr(n);else{if(n=n.parentNode,n==t.dom)break;r=0}}}s?Zi(t,n,r):o&&Zi(t,o,i)}function lm(t){let e=t.domSelectionRange(),n=e.focusNode,r=e.focusOffset;if(!n)return;let o=Fr(n),i,s;for(;;)if(r<o){if(n.nodeType!=1)break;let l=n.childNodes[r];if(jn(l,1))i=n,s=++r;else break}else{if($c(n))break;{let l=n.nextSibling;for(;l&&jn(l,1);)i=l.parentNode,s=ue(l)+1,l=l.nextSibling;if(l)n=l,r=0,o=Fr(n);else{if(n=n.parentNode,n==t.dom)break;r=o=0}}}i&&Zi(t,i,s)}function $c(t){let e=t.pmViewDesc;return e&&e.node&&e.node.isBlock}function am(t,e){for(;t&&e==t.childNodes.length&&!Gn(t);)e=ue(t)+1,t=t.parentNode;for(;t&&e<t.childNodes.length;){let n=t.childNodes[e];if(n.nodeType==3)return n;if(n.nodeType==1&&n.contentEditable=="false")break;t=n,e=0}}function cm(t,e){for(;t&&!e&&!Gn(t);)e=ue(t),t=t.parentNode;for(;t&&e;){let n=t.childNodes[e-1];if(n.nodeType==3)return n;if(n.nodeType==1&&n.contentEditable=="false")break;t=n,e=t.childNodes.length}}function Zi(t,e,n){if(e.nodeType!=3){let i,s;(s=am(e,n))?(e=s,n=0):(i=cm(e,n))&&(e=i,n=i.nodeValue.length)}let r=t.domSelection();if(!r)return;if(Kr(r)){let i=document.createRange();i.setEnd(e,n),i.setStart(e,n),r.removeAllRanges(),r.addRange(i)}else r.extend&&r.extend(e,n);t.domObserver.setCurSelection();let{state:o}=t;setTimeout(()=>{t.state==o&&ft(t)},50)}function cc(t,e){let n=t.state.doc.resolve(e);if(!(ge||Ip)&&n.parent.inlineContent){let o=t.coordsAtPos(e);if(e>n.start()){let i=t.coordsAtPos(e-1),s=(i.top+i.bottom)/2;if(s>o.top&&s<o.bottom&&Math.abs(i.left-o.left)>1)return i.left<o.left?"ltr":"rtl"}if(e<n.end()){let i=t.coordsAtPos(e+1),s=(i.top+i.bottom)/2;if(s>o.top&&s<o.bottom&&Math.abs(i.left-o.left)>1)return i.left>o.left?"ltr":"rtl"}}return getComputedStyle(t.dom).direction=="rtl"?"rtl":"ltr"}function uc(t,e,n){let r=t.state.selection;if(r instanceof A&&!r.empty||n.indexOf("s")>-1||Re&&n.indexOf("m")>-1)return!1;let{$from:o,$to:i}=r;if(!o.parent.inlineContent||t.endOfTextblock(e<0?"up":"down")){let s=Qi(t.state,e);if(s&&s instanceof O)return Mt(t,s)}if(!o.parent.inlineContent){let s=e<0?o:i,l=r instanceof be?E.near(s,e):E.findFrom(s,e);return l?Mt(t,l):!1}return!1}function dc(t,e){if(!(t.state.selection instanceof A))return!0;let{$head:n,$anchor:r,empty:o}=t.state.selection;if(!n.sameParent(r))return!0;if(!o)return!1;if(t.endOfTextblock(e>0?"forward":"backward"))return!0;let i=!n.textOffset&&(e<0?n.nodeBefore:n.nodeAfter);if(i&&!i.isText){let s=t.state.tr;return e<0?s.delete(n.pos-i.nodeSize,n.pos):s.delete(n.pos,n.pos+i.nodeSize),t.dispatch(s),!0}return!1}function fc(t,e,n){t.domObserver.stop(),e.contentEditable=n,t.domObserver.start()}function um(t){if(!xe||t.state.selection.$head.parentOffset>0)return!1;let{focusNode:e,focusOffset:n}=t.domSelectionRange();if(e&&e.nodeType==1&&n==0&&e.firstChild&&e.firstChild.contentEditable=="false"){let r=e.firstChild;fc(t,r,"true"),setTimeout(()=>fc(t,r,"false"),20)}return!1}function dm(t){let e="";return t.ctrlKey&&(e+="c"),t.metaKey&&(e+="m"),t.altKey&&(e+="a"),t.shiftKey&&(e+="s"),e}function fm(t,e){let n=e.keyCode,r=dm(e);if(n==8||Re&&n==72&&r=="c")return dc(t,-1)||hn(t,-1);if(n==46&&!e.shiftKey||Re&&n==68&&r=="c")return dc(t,1)||hn(t,1);if(n==13||n==27)return!0;if(n==37||Re&&n==66&&r=="c"){let o=n==37?cc(t,t.state.selection.from)=="ltr"?-1:1:-1;return ac(t,o,r)||hn(t,o)}else if(n==39||Re&&n==70&&r=="c"){let o=n==39?cc(t,t.state.selection.from)=="ltr"?1:-1:1;return ac(t,o,r)||hn(t,o)}else{if(n==38||Re&&n==80&&r=="c")return uc(t,-1,r)||hn(t,-1);if(n==40||Re&&n==78&&r=="c")return um(t)||uc(t,1,r)||hn(t,1);if(r==(Re?"m":"c")&&(n==66||n==73||n==89||n==90))return!0}return!1}function ds(t,e){t.someProp("transformCopied",h=>{e=h(e,t)});let n=[],{content:r,openStart:o,openEnd:i}=e;for(;o>1&&i>1&&r.childCount==1&&r.firstChild.childCount==1;){o--,i--;let h=r.firstChild;n.push(h.type.name,h.attrs!=h.type.defaultAttrs?h.attrs:null),r=h.content}let s=t.someProp("clipboardSerializer")||it.fromSchema(t.state.schema),l=Uc(),a=l.createElement("div");a.appendChild(s.serializeFragment(r,{document:l}));let c=a.firstChild,u,d=0;for(;c&&c.nodeType==1&&(u=Kc[c.nodeName.toLowerCase()]);){for(let h=u.length-1;h>=0;h--){let p=l.createElement(u[h]);for(;a.firstChild;)p.appendChild(a.firstChild);a.appendChild(p),d++}c=a.firstChild}c&&c.nodeType==1&&c.setAttribute("data-pm-slice",`${o} ${i}${d?` -${d}`:""} ${JSON.stringify(n)}`);let f=t.someProp("clipboardTextSerializer",h=>h(e,t))||e.content.textBetween(0,e.content.size,`

`);return{dom:a,text:f,slice:e}}function Vc(t,e,n,r,o){let i=o.parent.type.spec.code,s,l;if(!n&&!e)return null;let a=e&&(r||i||!n);if(a){if(t.someProp("transformPastedText",f=>{e=f(e,i||r,t)}),i)return e?new M(w.from(t.state.schema.text(e.replace(/\r\n?/g,`
`))),0,0):M.empty;let d=t.someProp("clipboardTextParser",f=>f(e,o,r,t));if(d)l=d;else{let f=o.marks(),{schema:h}=t.state,p=it.fromSchema(h);s=document.createElement("div"),e.split(/(?:\r\n?|\n)+/).forEach(m=>{let g=s.appendChild(document.createElement("p"));m&&g.appendChild(p.serializeNode(h.text(m,f)))})}}else t.someProp("transformPastedHTML",d=>{n=d(n,t)}),s=gm(n),Yn&&ym(s);let c=s&&s.querySelector("[data-pm-slice]"),u=c&&/^(\d+) (\d+)(?: -(\d+))? (.*)/.exec(c.getAttribute("data-pm-slice")||"");if(u&&u[3])for(let d=+u[3];d>0;d--){let f=s.firstChild;for(;f&&f.nodeType!=1;)f=f.nextSibling;if(!f)break;s=f}if(l||(l=(t.someProp("clipboardParser")||t.someProp("domParser")||Ue.fromSchema(t.state.schema)).parseSlice(s,{preserveWhitespace:!!(a||u),context:o,ruleFromNode(f){return f.nodeName=="BR"&&!f.nextSibling&&f.parentNode&&!hm.test(f.parentNode.nodeName)?{ignore:!0}:null}})),u)l=bm(hc(l,+u[1],+u[2]),u[4]);else if(l=M.maxOpen(pm(l.content,o),!0),l.openStart||l.openEnd){let d=0,f=0;for(let h=l.content.firstChild;d<l.openStart&&!h.type.spec.isolating;d++,h=h.firstChild);for(let h=l.content.lastChild;f<l.openEnd&&!h.type.spec.isolating;f++,h=h.lastChild);l=hc(l,d,f)}return t.someProp("transformPasted",d=>{l=d(l,t)}),l}var hm=/^(a|abbr|acronym|b|cite|code|del|em|i|ins|kbd|label|output|q|ruby|s|samp|span|strong|sub|sup|time|u|tt|var)$/i;function pm(t,e){if(t.childCount<2)return t;for(let n=e.depth;n>=0;n--){let o=e.node(n).contentMatchAt(e.index(n)),i,s=[];if(t.forEach(l=>{if(!s)return;let a=o.findWrapping(l.type),c;if(!a)return s=null;if(c=s.length&&i.length&&_c(a,i,l,s[s.length-1],0))s[s.length-1]=c;else{s.length&&(s[s.length-1]=jc(s[s.length-1],i.length));let u=Wc(l,a);s.push(u),o=o.matchType(u.type),i=a}}),s)return w.from(s)}return t}function Wc(t,e,n=0){for(let r=e.length-1;r>=n;r--)t=e[r].create(null,w.from(t));return t}function _c(t,e,n,r,o){if(o<t.length&&o<e.length&&t[o]==e[o]){let i=_c(t,e,n,r.lastChild,o+1);if(i)return r.copy(r.content.replaceChild(r.childCount-1,i));if(r.contentMatchAt(r.childCount).matchType(o==t.length-1?n.type:t[o+1]))return r.copy(r.content.append(w.from(Wc(n,t,o+1))))}}function jc(t,e){if(e==0)return t;let n=t.content.replaceChild(t.childCount-1,jc(t.lastChild,e-1)),r=t.contentMatchAt(t.childCount).fillBefore(w.empty,!0);return t.copy(n.append(r))}function es(t,e,n,r,o,i){let s=e<0?t.firstChild:t.lastChild,l=s.content;return t.childCount>1&&(i=0),o<r-1&&(l=es(l,e,n,r,o+1,i)),o>=n&&(l=e<0?s.contentMatchAt(0).fillBefore(l,i<=o).append(l):l.append(s.contentMatchAt(s.childCount).fillBefore(w.empty,!0))),t.replaceChild(e<0?0:t.childCount-1,s.copy(l))}function hc(t,e,n){return e<t.openStart&&(t=new M(es(t.content,-1,e,t.openStart,0,t.openEnd),e,t.openEnd)),n<t.openEnd&&(t=new M(es(t.content,1,n,t.openEnd,0,0),t.openStart,n)),t}var Kc={thead:["table"],tbody:["table"],tfoot:["table"],caption:["table"],colgroup:["table"],col:["table","colgroup"],tr:["table","tbody"],td:["table","tbody","tr"],th:["table","tbody","tr"]},pc=null;function Uc(){return pc||(pc=document.implementation.createHTMLDocument("title"))}var $i=null;function mm(t){let e=window.trustedTypes;return e?($i||($i=e.defaultPolicy||e.createPolicy("ProseMirrorClipboard",{createHTML:n=>n})),$i.createHTML(t)):t}function gm(t){let e=/^(\s*<meta [^>]*>)*/.exec(t);e&&(t=t.slice(e[0].length));let n=Uc().createElement("div"),r=/<([a-z][^>\s]+)/i.exec(t),o;if((o=r&&Kc[r[1].toLowerCase()])&&(t=o.map(i=>"<"+i+">").join("")+t+o.map(i=>"</"+i+">").reverse().join("")),n.innerHTML=mm(t),o)for(let i=0;i<o.length;i++)n=n.querySelector(o[i])||n;return n}function ym(t){let e=t.querySelectorAll(ge?"span:not([class]):not([style])":"span.Apple-converted-space");for(let n=0;n<e.length;n++){let r=e[n];r.childNodes.length==1&&r.textContent=="\xA0"&&r.parentNode&&r.parentNode.replaceChild(t.ownerDocument.createTextNode(" "),r)}}function bm(t,e){if(!t.size)return t;let n=t.content.firstChild.type.schema,r;try{r=JSON.parse(e)}catch{return t}let{content:o,openStart:i,openEnd:s}=t;for(let l=r.length-2;l>=0;l-=2){let a=n.nodes[r[l]];if(!a||a.hasRequiredAttrs())break;o=w.from(a.create(r[l+1],o)),i++,s++}return new M(o,i,s)}var we={},Se={},xm={touchstart:!0,touchmove:!0},ts=class{constructor(){this.shiftKey=!1,this.mouseDown=null,this.lastKeyCode=null,this.lastKeyCodeTime=0,this.lastClick={time:0,x:0,y:0,type:"",button:0},this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastIOSEnter=0,this.lastIOSEnterFallbackTimeout=-1,this.lastFocus=0,this.lastTouch=0,this.lastChromeDelete=0,this.composing=!1,this.compositionNode=null,this.composingTimeout=-1,this.compositionNodes=[],this.compositionEndedAt=-2e8,this.compositionID=1,this.compositionPendingChanges=0,this.domChangeCount=0,this.eventHandlers=Object.create(null),this.hideSelectionGuard=null}};function wm(t){for(let e in we){let n=we[e];t.dom.addEventListener(e,t.input.eventHandlers[e]=r=>{km(t,r)&&!fs(t,r)&&(t.editable||!(r.type in Se))&&n(t,r)},xm[e]?{passive:!0}:void 0)}xe&&t.dom.addEventListener("input",()=>null),ns(t)}function vt(t,e){t.input.lastSelectionOrigin=e,t.input.lastSelectionTime=Date.now()}function Sm(t){t.domObserver.stop();for(let e in t.input.eventHandlers)t.dom.removeEventListener(e,t.input.eventHandlers[e]);clearTimeout(t.input.composingTimeout),clearTimeout(t.input.lastIOSEnterFallbackTimeout)}function ns(t){t.someProp("handleDOMEvents",e=>{for(let n in e)t.input.eventHandlers[n]||t.dom.addEventListener(n,t.input.eventHandlers[n]=r=>fs(t,r))})}function fs(t,e){return t.someProp("handleDOMEvents",n=>{let r=n[e.type];return r?r(t,e)||e.defaultPrevented:!1})}function km(t,e){if(!e.bubbles)return!0;if(e.defaultPrevented)return!1;for(let n=e.target;n!=t.dom;n=n.parentNode)if(!n||n.nodeType==11||n.pmViewDesc&&n.pmViewDesc.stopEvent(e))return!1;return!0}function Cm(t,e){!fs(t,e)&&we[e.type]&&(t.editable||!(e.type in Se))&&we[e.type](t,e)}Se.keydown=(t,e)=>{let n=e;if(t.input.shiftKey=n.keyCode==16||n.shiftKey,!qc(t,n)&&(t.input.lastKeyCode=n.keyCode,t.input.lastKeyCodeTime=Date.now(),!(dt&&ge&&n.keyCode==13)))if(n.keyCode!=229&&t.domObserver.forceFlush(),yn&&n.keyCode==13&&!n.ctrlKey&&!n.altKey&&!n.metaKey){let r=Date.now();t.input.lastIOSEnter=r,t.input.lastIOSEnterFallbackTimeout=setTimeout(()=>{t.input.lastIOSEnter==r&&(t.someProp("handleKeyDown",o=>o(t,Vt(13,"Enter"))),t.input.lastIOSEnter=0)},200)}else t.someProp("handleKeyDown",r=>r(t,n))||fm(t,n)?n.preventDefault():vt(t,"key")};Se.keyup=(t,e)=>{e.keyCode==16&&(t.input.shiftKey=!1)};Se.keypress=(t,e)=>{let n=e;if(qc(t,n)||!n.charCode||n.ctrlKey&&!n.altKey||Re&&n.metaKey)return;if(t.someProp("handleKeyPress",o=>o(t,n))){n.preventDefault();return}let r=t.state.selection;if(!(r instanceof A)||!r.$from.sameParent(r.$to)){let o=String.fromCharCode(n.charCode),i=()=>t.state.tr.insertText(o).scrollIntoView();!/[\r\n]/.test(o)&&!t.someProp("handleTextInput",s=>s(t,r.$from.pos,r.$to.pos,o,i))&&t.dispatch(i()),n.preventDefault()}};function Ur(t){return{left:t.clientX,top:t.clientY}}function Mm(t,e){let n=e.x-t.clientX,r=e.y-t.clientY;return n*n+r*r<100}function hs(t,e,n,r,o){if(r==-1)return!1;let i=t.state.doc.resolve(r);for(let s=i.depth+1;s>0;s--)if(t.someProp(e,l=>s>i.depth?l(t,n,i.nodeAfter,i.before(s),o,!0):l(t,n,i.node(s),i.before(s),o,!1)))return!0;return!1}function mn(t,e,n){if(t.focused||t.focus(),t.state.selection.eq(e))return;let r=t.state.tr.setSelection(e);n=="pointer"&&r.setMeta("pointer",!0),t.dispatch(r)}function vm(t,e){if(e==-1)return!1;let n=t.state.doc.resolve(e),r=n.nodeAfter;return r&&r.isAtom&&O.isSelectable(r)?(mn(t,new O(n),"pointer"),!0):!1}function Tm(t,e){if(e==-1)return!1;let n=t.state.selection,r,o;n instanceof O&&(r=n.node);let i=t.state.doc.resolve(e);for(let s=i.depth+1;s>0;s--){let l=s>i.depth?i.nodeAfter:i.node(s);if(O.isSelectable(l)){r&&n.$from.depth>0&&s>=n.$from.depth&&i.before(n.$from.depth+1)==n.$from.pos?o=i.before(n.$from.depth):o=i.before(s);break}}return o!=null?(mn(t,O.create(t.state.doc,o),"pointer"),!0):!1}function Am(t,e,n,r,o){return hs(t,"handleClickOn",e,n,r)||t.someProp("handleClick",i=>i(t,e,r))||(o?Tm(t,n):vm(t,n))}function Em(t,e,n,r){return hs(t,"handleDoubleClickOn",e,n,r)||t.someProp("handleDoubleClick",o=>o(t,e,r))}function Om(t,e,n,r){return hs(t,"handleTripleClickOn",e,n,r)||t.someProp("handleTripleClick",o=>o(t,e,r))||Nm(t,n,r)}function Nm(t,e,n){if(n.button!=0)return!1;let r=t.state.doc;if(e==-1)return r.inlineContent?(mn(t,A.create(r,0,r.content.size),"pointer"),!0):!1;let o=r.resolve(e);for(let i=o.depth+1;i>0;i--){let s=i>o.depth?o.nodeAfter:o.node(i),l=o.before(i);if(s.inlineContent)mn(t,A.create(r,l+1,l+1+s.content.size),"pointer");else if(O.isSelectable(s))mn(t,O.create(r,l),"pointer");else continue;return!0}}function ps(t){return $r(t)}var Jc=Re?"metaKey":"ctrlKey";we.mousedown=(t,e)=>{let n=e;t.input.shiftKey=n.shiftKey;let r=ps(t),o=Date.now(),i="singleClick";o-t.input.lastClick.time<500&&Mm(n,t.input.lastClick)&&!n[Jc]&&t.input.lastClick.button==n.button&&(t.input.lastClick.type=="singleClick"?i="doubleClick":t.input.lastClick.type=="doubleClick"&&(i="tripleClick")),t.input.lastClick={time:o,x:n.clientX,y:n.clientY,type:i,button:n.button};let s=t.posAtCoords(Ur(n));s&&(i=="singleClick"?(t.input.mouseDown&&t.input.mouseDown.done(),t.input.mouseDown=new rs(t,s,n,!!r)):(i=="doubleClick"?Em:Om)(t,s.pos,s.inside,n)?n.preventDefault():vt(t,"pointer"))};var rs=class{constructor(e,n,r,o){this.view=e,this.pos=n,this.event=r,this.flushed=o,this.delayedSelectionSync=!1,this.mightDrag=null,this.startDoc=e.state.doc,this.selectNode=!!r[Jc],this.allowDefault=r.shiftKey;let i,s;if(n.inside>-1)i=e.state.doc.nodeAt(n.inside),s=n.inside;else{let u=e.state.doc.resolve(n.pos);i=u.parent,s=u.depth?u.before():0}let l=o?null:r.target,a=l?e.docView.nearestDesc(l,!0):null;this.target=a&&a.dom.nodeType==1?a.dom:null;let{selection:c}=e.state;(r.button==0&&i.type.spec.draggable&&i.type.spec.selectable!==!1||c instanceof O&&c.from<=s&&c.to>s)&&(this.mightDrag={node:i,pos:s,addAttr:!!(this.target&&!this.target.draggable),setUneditable:!!(this.target&&Ve&&!this.target.hasAttribute("contentEditable"))}),this.target&&this.mightDrag&&(this.mightDrag.addAttr||this.mightDrag.setUneditable)&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&(this.target.draggable=!0),this.mightDrag.setUneditable&&setTimeout(()=>{this.view.input.mouseDown==this&&this.target.setAttribute("contentEditable","false")},20),this.view.domObserver.start()),e.root.addEventListener("mouseup",this.up=this.up.bind(this)),e.root.addEventListener("mousemove",this.move=this.move.bind(this)),vt(e,"pointer")}done(){this.view.root.removeEventListener("mouseup",this.up),this.view.root.removeEventListener("mousemove",this.move),this.mightDrag&&this.target&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&this.target.removeAttribute("draggable"),this.mightDrag.setUneditable&&this.target.removeAttribute("contentEditable"),this.view.domObserver.start()),this.delayedSelectionSync&&setTimeout(()=>ft(this.view)),this.view.input.mouseDown=null}up(e){if(this.done(),!this.view.dom.contains(e.target))return;let n=this.pos;this.view.state.doc!=this.startDoc&&(n=this.view.posAtCoords(Ur(e))),this.updateAllowDefault(e),this.allowDefault||!n?vt(this.view,"pointer"):Am(this.view,n.pos,n.inside,e,this.selectNode)?e.preventDefault():e.button==0&&(this.flushed||xe&&this.mightDrag&&!this.mightDrag.node.isAtom||ge&&!this.view.state.selection.visible&&Math.min(Math.abs(n.pos-this.view.state.selection.from),Math.abs(n.pos-this.view.state.selection.to))<=2)?(mn(this.view,E.near(this.view.state.doc.resolve(n.pos)),"pointer"),e.preventDefault()):vt(this.view,"pointer")}move(e){this.updateAllowDefault(e),vt(this.view,"pointer"),e.buttons==0&&this.done()}updateAllowDefault(e){!this.allowDefault&&(Math.abs(this.event.x-e.clientX)>4||Math.abs(this.event.y-e.clientY)>4)&&(this.allowDefault=!0)}};we.touchstart=t=>{t.input.lastTouch=Date.now(),ps(t),vt(t,"pointer")};we.touchmove=t=>{t.input.lastTouch=Date.now(),vt(t,"pointer")};we.contextmenu=t=>ps(t);function qc(t,e){return t.composing?!0:xe&&Math.abs(e.timeStamp-t.input.compositionEndedAt)<500?(t.input.compositionEndedAt=-2e8,!0):!1}var Rm=dt?5e3:-1;Se.compositionstart=Se.compositionupdate=t=>{if(!t.composing){t.domObserver.flush();let{state:e}=t,n=e.selection.$to;if(e.selection instanceof A&&(e.storedMarks||!n.textOffset&&n.parentOffset&&n.nodeBefore.marks.some(r=>r.type.spec.inclusive===!1)))t.markCursor=t.state.storedMarks||n.marks(),$r(t,!0),t.markCursor=null;else if($r(t,!e.selection.empty),Ve&&e.selection.empty&&n.parentOffset&&!n.textOffset&&n.nodeBefore.marks.length){let r=t.domSelectionRange();for(let o=r.focusNode,i=r.focusOffset;o&&o.nodeType==1&&i!=0;){let s=i<0?o.lastChild:o.childNodes[i-1];if(!s)break;if(s.nodeType==3){let l=t.domSelection();l&&l.collapse(s,s.nodeValue.length);break}else o=s,i=-1}}t.input.composing=!0}Gc(t,Rm)};Se.compositionend=(t,e)=>{t.composing&&(t.input.composing=!1,t.input.compositionEndedAt=e.timeStamp,t.input.compositionPendingChanges=t.domObserver.pendingRecords().length?t.input.compositionID:0,t.input.compositionNode=null,t.input.compositionPendingChanges&&Promise.resolve().then(()=>t.domObserver.flush()),t.input.compositionID++,Gc(t,20))};function Gc(t,e){clearTimeout(t.input.composingTimeout),e>-1&&(t.input.composingTimeout=setTimeout(()=>$r(t),e))}function Yc(t){for(t.composing&&(t.input.composing=!1,t.input.compositionEndedAt=Pm());t.input.compositionNodes.length>0;)t.input.compositionNodes.pop().markParentsDirty()}function Dm(t){let e=t.domSelectionRange();if(!e.focusNode)return null;let n=Op(e.focusNode,e.focusOffset),r=Np(e.focusNode,e.focusOffset);if(n&&r&&n!=r){let o=r.pmViewDesc,i=t.domObserver.lastChangedTextNode;if(n==i||r==i)return i;if(!o||!o.isText(r.nodeValue))return r;if(t.input.compositionNode==r){let s=n.pmViewDesc;if(!(!s||!s.isText(n.nodeValue)))return r}}return n||r}function Pm(){let t=document.createEvent("Event");return t.initEvent("event",!0,!0),t.timeStamp}function $r(t,e=!1){if(!(dt&&t.domObserver.flushingSoon>=0)){if(t.domObserver.forceFlush(),Yc(t),e||t.docView&&t.docView.dirty){let n=cs(t),r=t.state.selection;return n&&!n.eq(r)?t.dispatch(t.state.tr.setSelection(n)):(t.markCursor||e)&&!r.$from.node(r.$from.sharedDepth(r.to)).inlineContent?t.dispatch(t.state.tr.deleteSelection()):t.updateState(t.state),!0}return!1}}function Im(t,e){if(!t.dom.parentNode)return;let n=t.dom.parentNode.appendChild(document.createElement("div"));n.appendChild(e),n.style.cssText="position: fixed; left: -10000px; top: 10px";let r=getSelection(),o=document.createRange();o.selectNodeContents(e),t.dom.blur(),r.removeAllRanges(),r.addRange(o),setTimeout(()=>{n.parentNode&&n.parentNode.removeChild(n),t.focus()},50)}var Kn=ke&&Tt<15||yn&&Lp<604;we.copy=Se.cut=(t,e)=>{let n=e,r=t.state.selection,o=n.type=="cut";if(r.empty)return;let i=Kn?null:n.clipboardData,s=r.content(),{dom:l,text:a}=ds(t,s);i?(n.preventDefault(),i.clearData(),i.setData("text/html",l.innerHTML),i.setData("text/plain",a)):Im(t,l),o&&t.dispatch(t.state.tr.deleteSelection().scrollIntoView().setMeta("uiEvent","cut"))};function Lm(t){return t.openStart==0&&t.openEnd==0&&t.content.childCount==1?t.content.firstChild:null}function Bm(t,e){if(!t.dom.parentNode)return;let n=t.input.shiftKey||t.state.selection.$from.parent.type.spec.code,r=t.dom.parentNode.appendChild(document.createElement(n?"textarea":"div"));n||(r.contentEditable="true"),r.style.cssText="position: fixed; left: -10000px; top: 10px",r.focus();let o=t.input.shiftKey&&t.input.lastKeyCode!=45;setTimeout(()=>{t.focus(),r.parentNode&&r.parentNode.removeChild(r),n?Un(t,r.value,null,o,e):Un(t,r.textContent,r.innerHTML,o,e)},50)}function Un(t,e,n,r,o){let i=Vc(t,e,n,r,t.state.selection.$from);if(t.someProp("handlePaste",a=>a(t,o,i||M.empty)))return!0;if(!i)return!1;let s=Lm(i),l=s?t.state.tr.replaceSelectionWith(s,r):t.state.tr.replaceSelection(i);return t.dispatch(l.scrollIntoView().setMeta("paste",!0).setMeta("uiEvent","paste")),!0}function Xc(t){let e=t.getData("text/plain")||t.getData("Text");if(e)return e;let n=t.getData("text/uri-list");return n?n.replace(/\r?\n/g," "):""}Se.paste=(t,e)=>{let n=e;if(t.composing&&!dt)return;let r=Kn?null:n.clipboardData,o=t.input.shiftKey&&t.input.lastKeyCode!=45;r&&Un(t,Xc(r),r.getData("text/html"),o,n)?n.preventDefault():Bm(t,n)};var Vr=class{constructor(e,n,r){this.slice=e,this.move=n,this.node=r}},zm=Re?"altKey":"ctrlKey";function Qc(t,e){let n=t.someProp("dragCopies",r=>!r(e));return n??!e[zm]}we.dragstart=(t,e)=>{let n=e,r=t.input.mouseDown;if(r&&r.done(),!n.dataTransfer)return;let o=t.state.selection,i=o.empty?null:t.posAtCoords(Ur(n)),s;if(!(i&&i.pos>=o.from&&i.pos<=(o instanceof O?o.to-1:o.to))){if(r&&r.mightDrag)s=O.create(t.state.doc,r.mightDrag.pos);else if(n.target&&n.target.nodeType==1){let d=t.docView.nearestDesc(n.target,!0);d&&d.node.type.spec.draggable&&d!=t.docView&&(s=O.create(t.state.doc,d.posBefore))}}let l=(s||t.state.selection).content(),{dom:a,text:c,slice:u}=ds(t,l);(!n.dataTransfer.files.length||!ge||Ac>120)&&n.dataTransfer.clearData(),n.dataTransfer.setData(Kn?"Text":"text/html",a.innerHTML),n.dataTransfer.effectAllowed="copyMove",Kn||n.dataTransfer.setData("text/plain",c),t.dragging=new Vr(u,Qc(t,n),s)};we.dragend=t=>{let e=t.dragging;window.setTimeout(()=>{t.dragging==e&&(t.dragging=null)},50)};Se.dragover=Se.dragenter=(t,e)=>e.preventDefault();Se.drop=(t,e)=>{let n=e,r=t.dragging;if(t.dragging=null,!n.dataTransfer)return;let o=t.posAtCoords(Ur(n));if(!o)return;let i=t.state.doc.resolve(o.pos),s=r&&r.slice;s?t.someProp("transformPasted",p=>{s=p(s,t)}):s=Vc(t,Xc(n.dataTransfer),Kn?null:n.dataTransfer.getData("text/html"),!1,i);let l=!!(r&&Qc(t,n));if(t.someProp("handleDrop",p=>p(t,n,s||M.empty,l))){n.preventDefault();return}if(!s)return;n.preventDefault();let a=s?Rr(t.state.doc,i.pos,s):i.pos;a==null&&(a=i.pos);let c=t.state.tr;if(l){let{node:p}=r;p?p.replace(c):c.deleteSelection()}let u=c.mapping.map(a),d=s.openStart==0&&s.openEnd==0&&s.content.childCount==1,f=c.doc;if(d?c.replaceRangeWith(u,u,s.content.firstChild):c.replaceRange(u,u,s),c.doc.eq(f))return;let h=c.doc.resolve(u);if(d&&O.isSelectable(s.content.firstChild)&&h.nodeAfter&&h.nodeAfter.sameMarkup(s.content.firstChild))c.setSelection(new O(h));else{let p=c.mapping.map(a);c.mapping.maps[c.mapping.maps.length-1].forEach((m,g,y,b)=>p=b),c.setSelection(us(t,h,c.doc.resolve(p)))}t.focus(),t.dispatch(c.setMeta("uiEvent","drop"))};we.focus=t=>{t.input.lastFocus=Date.now(),t.focused||(t.domObserver.stop(),t.dom.classList.add("ProseMirror-focused"),t.domObserver.start(),t.focused=!0,setTimeout(()=>{t.docView&&t.hasFocus()&&!t.domObserver.currentSelection.eq(t.domSelectionRange())&&ft(t)},20))};we.blur=(t,e)=>{let n=e;t.focused&&(t.domObserver.stop(),t.dom.classList.remove("ProseMirror-focused"),t.domObserver.start(),n.relatedTarget&&t.dom.contains(n.relatedTarget)&&t.domObserver.currentSelection.clear(),t.focused=!1)};we.beforeinput=(t,e)=>{if(ge&&dt&&e.inputType=="deleteContentBackward"){t.domObserver.flushSoon();let{domChangeCount:r}=t.input;setTimeout(()=>{if(t.input.domChangeCount!=r||(t.dom.blur(),t.focus(),t.someProp("handleKeyDown",i=>i(t,Vt(8,"Backspace")))))return;let{$cursor:o}=t.state.selection;o&&o.pos>0&&t.dispatch(t.state.tr.delete(o.pos-1,o.pos).scrollIntoView())},50)}};for(let t in Se)we[t]=Se[t];function Jn(t,e){if(t==e)return!0;for(let n in t)if(t[n]!==e[n])return!1;for(let n in e)if(!(n in t))return!1;return!0}var Wr=class t{constructor(e,n){this.toDOM=e,this.spec=n||Kt,this.side=this.spec.side||0}map(e,n,r,o){let{pos:i,deleted:s}=e.mapResult(n.from+o,this.side<0?-1:1);return s?null:new Y(i-r,i-r,this)}valid(){return!0}eq(e){return this==e||e instanceof t&&(this.spec.key&&this.spec.key==e.spec.key||this.toDOM==e.toDOM&&Jn(this.spec,e.spec))}destroy(e){this.spec.destroy&&this.spec.destroy(e)}},jt=class t{constructor(e,n){this.attrs=e,this.spec=n||Kt}map(e,n,r,o){let i=e.map(n.from+o,this.spec.inclusiveStart?-1:1)-r,s=e.map(n.to+o,this.spec.inclusiveEnd?1:-1)-r;return i>=s?null:new Y(i,s,this)}valid(e,n){return n.from<n.to}eq(e){return this==e||e instanceof t&&Jn(this.attrs,e.attrs)&&Jn(this.spec,e.spec)}static is(e){return e.type instanceof t}destroy(){}},is=class t{constructor(e,n){this.attrs=e,this.spec=n||Kt}map(e,n,r,o){let i=e.mapResult(n.from+o,1);if(i.deleted)return null;let s=e.mapResult(n.to+o,-1);return s.deleted||s.pos<=i.pos?null:new Y(i.pos-r,s.pos-r,this)}valid(e,n){let{index:r,offset:o}=e.content.findIndex(n.from),i;return o==n.from&&!(i=e.child(r)).isText&&o+i.nodeSize==n.to}eq(e){return this==e||e instanceof t&&Jn(this.attrs,e.attrs)&&Jn(this.spec,e.spec)}destroy(){}},Y=class t{constructor(e,n,r){this.from=e,this.to=n,this.type=r}copy(e,n){return new t(e,n,this.type)}eq(e,n=0){return this.type.eq(e.type)&&this.from+n==e.from&&this.to+n==e.to}map(e,n,r){return this.type.map(e,this,n,r)}static widget(e,n,r){return new t(e,e,new Wr(n,r))}static inline(e,n,r,o){return new t(e,n,new jt(r,o))}static node(e,n,r,o){return new t(e,n,new is(r,o))}get spec(){return this.type.spec}get inline(){return this.type instanceof jt}get widget(){return this.type instanceof Wr}},pn=[],Kt={},J=class t{constructor(e,n){this.local=e.length?e:pn,this.children=n.length?n:pn}static create(e,n){return n.length?jr(n,e,0,Kt):me}find(e,n,r){let o=[];return this.findInner(e??0,n??1e9,o,0,r),o}findInner(e,n,r,o,i){for(let s=0;s<this.local.length;s++){let l=this.local[s];l.from<=n&&l.to>=e&&(!i||i(l.spec))&&r.push(l.copy(l.from+o,l.to+o))}for(let s=0;s<this.children.length;s+=3)if(this.children[s]<n&&this.children[s+1]>e){let l=this.children[s]+1;this.children[s+2].findInner(e-l,n-l,r,o+l,i)}}map(e,n,r){return this==me||e.maps.length==0?this:this.mapInner(e,n,0,0,r||Kt)}mapInner(e,n,r,o,i){let s;for(let l=0;l<this.local.length;l++){let a=this.local[l].map(e,r,o);a&&a.type.valid(n,a)?(s||(s=[])).push(a):i.onRemove&&i.onRemove(this.local[l].spec)}return this.children.length?Hm(this.children,s||[],e,n,r,o,i):s?new t(s.sort(Ut),pn):me}add(e,n){return n.length?this==me?t.create(e,n):this.addInner(e,n,0):this}addInner(e,n,r){let o,i=0;e.forEach((l,a)=>{let c=a+r,u;if(u=eu(n,l,c)){for(o||(o=this.children.slice());i<o.length&&o[i]<a;)i+=3;o[i]==a?o[i+2]=o[i+2].addInner(l,u,c+1):o.splice(i,0,a,a+l.nodeSize,jr(u,l,c+1,Kt)),i+=3}});let s=Zc(i?tu(n):n,-r);for(let l=0;l<s.length;l++)s[l].type.valid(e,s[l])||s.splice(l--,1);return new t(s.length?this.local.concat(s).sort(Ut):this.local,o||this.children)}remove(e){return e.length==0||this==me?this:this.removeInner(e,0)}removeInner(e,n){let r=this.children,o=this.local;for(let i=0;i<r.length;i+=3){let s,l=r[i]+n,a=r[i+1]+n;for(let u=0,d;u<e.length;u++)(d=e[u])&&d.from>l&&d.to<a&&(e[u]=null,(s||(s=[])).push(d));if(!s)continue;r==this.children&&(r=this.children.slice());let c=r[i+2].removeInner(s,l+1);c!=me?r[i+2]=c:(r.splice(i,3),i-=3)}if(o.length){for(let i=0,s;i<e.length;i++)if(s=e[i])for(let l=0;l<o.length;l++)o[l].eq(s,n)&&(o==this.local&&(o=this.local.slice()),o.splice(l--,1))}return r==this.children&&o==this.local?this:o.length||r.length?new t(o,r):me}forChild(e,n){if(this==me)return this;if(n.isLeaf)return t.empty;let r,o;for(let l=0;l<this.children.length;l+=3)if(this.children[l]>=e){this.children[l]==e&&(r=this.children[l+2]);break}let i=e+1,s=i+n.content.size;for(let l=0;l<this.local.length;l++){let a=this.local[l];if(a.from<s&&a.to>i&&a.type instanceof jt){let c=Math.max(i,a.from)-i,u=Math.min(s,a.to)-i;c<u&&(o||(o=[])).push(a.copy(c,u))}}if(o){let l=new t(o.sort(Ut),pn);return r?new _r([l,r]):l}return r||me}eq(e){if(this==e)return!0;if(!(e instanceof t)||this.local.length!=e.local.length||this.children.length!=e.children.length)return!1;for(let n=0;n<this.local.length;n++)if(!this.local[n].eq(e.local[n]))return!1;for(let n=0;n<this.children.length;n+=3)if(this.children[n]!=e.children[n]||this.children[n+1]!=e.children[n+1]||!this.children[n+2].eq(e.children[n+2]))return!1;return!0}locals(e){return ms(this.localsInner(e))}localsInner(e){if(this==me)return pn;if(e.inlineContent||!this.local.some(jt.is))return this.local;let n=[];for(let r=0;r<this.local.length;r++)this.local[r].type instanceof jt||n.push(this.local[r]);return n}forEachSet(e){e(this)}};J.empty=new J([],[]);J.removeOverlap=ms;var me=J.empty,_r=class t{constructor(e){this.members=e}map(e,n){let r=this.members.map(o=>o.map(e,n,Kt));return t.from(r)}forChild(e,n){if(n.isLeaf)return J.empty;let r=[];for(let o=0;o<this.members.length;o++){let i=this.members[o].forChild(e,n);i!=me&&(i instanceof t?r=r.concat(i.members):r.push(i))}return t.from(r)}eq(e){if(!(e instanceof t)||e.members.length!=this.members.length)return!1;for(let n=0;n<this.members.length;n++)if(!this.members[n].eq(e.members[n]))return!1;return!0}locals(e){let n,r=!0;for(let o=0;o<this.members.length;o++){let i=this.members[o].localsInner(e);if(i.length)if(!n)n=i;else{r&&(n=n.slice(),r=!1);for(let s=0;s<i.length;s++)n.push(i[s])}}return n?ms(r?n:n.sort(Ut)):pn}static from(e){switch(e.length){case 0:return me;case 1:return e[0];default:return new t(e.every(n=>n instanceof J)?e:e.reduce((n,r)=>n.concat(r instanceof J?r:r.members),[]))}}forEachSet(e){for(let n=0;n<this.members.length;n++)this.members[n].forEachSet(e)}};function Hm(t,e,n,r,o,i,s){let l=t.slice();for(let c=0,u=i;c<n.maps.length;c++){let d=0;n.maps[c].forEach((f,h,p,m)=>{let g=m-p-(h-f);for(let y=0;y<l.length;y+=3){let b=l[y+1];if(b<0||f>b+u-d)continue;let x=l[y]+u-d;h>=x?l[y+1]=f<=x?-2:-1:f>=u&&g&&(l[y]+=g,l[y+1]+=g)}d+=g}),u=n.maps[c].map(u,-1)}let a=!1;for(let c=0;c<l.length;c+=3)if(l[c+1]<0){if(l[c+1]==-2){a=!0,l[c+1]=-1;continue}let u=n.map(t[c]+i),d=u-o;if(d<0||d>=r.content.size){a=!0;continue}let f=n.map(t[c+1]+i,-1),h=f-o,{index:p,offset:m}=r.content.findIndex(d),g=r.maybeChild(p);if(g&&m==d&&m+g.nodeSize==h){let y=l[c+2].mapInner(n,g,u+1,t[c]+i+1,s);y!=me?(l[c]=d,l[c+1]=h,l[c+2]=y):(l[c+1]=-2,a=!0)}else a=!0}if(a){let c=Fm(l,t,e,n,o,i,s),u=jr(c,r,0,s);e=u.local;for(let d=0;d<l.length;d+=3)l[d+1]<0&&(l.splice(d,3),d-=3);for(let d=0,f=0;d<u.children.length;d+=3){let h=u.children[d];for(;f<l.length&&l[f]<h;)f+=3;l.splice(f,0,u.children[d],u.children[d+1],u.children[d+2])}}return new J(e.sort(Ut),l)}function Zc(t,e){if(!e||!t.length)return t;let n=[];for(let r=0;r<t.length;r++){let o=t[r];n.push(new Y(o.from+e,o.to+e,o.type))}return n}function Fm(t,e,n,r,o,i,s){function l(a,c){for(let u=0;u<a.local.length;u++){let d=a.local[u].map(r,o,c);d?n.push(d):s.onRemove&&s.onRemove(a.local[u].spec)}for(let u=0;u<a.children.length;u+=3)l(a.children[u+2],a.children[u]+c+1)}for(let a=0;a<t.length;a+=3)t[a+1]==-1&&l(t[a+2],e[a]+i+1);return n}function eu(t,e,n){if(e.isLeaf)return null;let r=n+e.nodeSize,o=null;for(let i=0,s;i<t.length;i++)(s=t[i])&&s.from>n&&s.to<r&&((o||(o=[])).push(s),t[i]=null);return o}function tu(t){let e=[];for(let n=0;n<t.length;n++)t[n]!=null&&e.push(t[n]);return e}function jr(t,e,n,r){let o=[],i=!1;e.forEach((l,a)=>{let c=eu(t,l,a+n);if(c){i=!0;let u=jr(c,l,n+a+1,r);u!=me&&o.push(a,a+l.nodeSize,u)}});let s=Zc(i?tu(t):t,-n).sort(Ut);for(let l=0;l<s.length;l++)s[l].type.valid(e,s[l])||(r.onRemove&&r.onRemove(s[l].spec),s.splice(l--,1));return s.length||o.length?new J(s,o):me}function Ut(t,e){return t.from-e.from||t.to-e.to}function ms(t){let e=t;for(let n=0;n<e.length-1;n++){let r=e[n];if(r.from!=r.to)for(let o=n+1;o<e.length;o++){let i=e[o];if(i.from==r.from){i.to!=r.to&&(e==t&&(e=t.slice()),e[o]=i.copy(i.from,r.to),mc(e,o+1,i.copy(r.to,i.to)));continue}else{i.from<r.to&&(e==t&&(e=t.slice()),e[n]=r.copy(r.from,i.from),mc(e,o,r.copy(i.from,r.to)));break}}}return e}function mc(t,e,n){for(;e<t.length&&Ut(n,t[e])>0;)e++;t.splice(e,0,n)}function Vi(t){let e=[];return t.someProp("decorations",n=>{let r=n(t.state);r&&r!=me&&e.push(r)}),t.cursorWrapper&&e.push(J.create(t.state.doc,[t.cursorWrapper.deco])),_r.from(e)}var $m={childList:!0,characterData:!0,characterDataOldValue:!0,attributes:!0,attributeOldValue:!0,subtree:!0},Vm=ke&&Tt<=11,ss=class{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}set(e){this.anchorNode=e.anchorNode,this.anchorOffset=e.anchorOffset,this.focusNode=e.focusNode,this.focusOffset=e.focusOffset}clear(){this.anchorNode=this.focusNode=null}eq(e){return e.anchorNode==this.anchorNode&&e.anchorOffset==this.anchorOffset&&e.focusNode==this.focusNode&&e.focusOffset==this.focusOffset}},ls=class{constructor(e,n){this.view=e,this.handleDOMChange=n,this.queue=[],this.flushingSoon=-1,this.observer=null,this.currentSelection=new ss,this.onCharData=null,this.suppressingSelectionUpdates=!1,this.lastChangedTextNode=null,this.observer=window.MutationObserver&&new window.MutationObserver(r=>{for(let o=0;o<r.length;o++)this.queue.push(r[o]);ke&&Tt<=11&&r.some(o=>o.type=="childList"&&o.removedNodes.length||o.type=="characterData"&&o.oldValue.length>o.target.nodeValue.length)?this.flushSoon():this.flush()}),Vm&&(this.onCharData=r=>{this.queue.push({target:r.target,type:"characterData",oldValue:r.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this)}flushSoon(){this.flushingSoon<0&&(this.flushingSoon=window.setTimeout(()=>{this.flushingSoon=-1,this.flush()},20))}forceFlush(){this.flushingSoon>-1&&(window.clearTimeout(this.flushingSoon),this.flushingSoon=-1,this.flush())}start(){this.observer&&(this.observer.takeRecords(),this.observer.observe(this.view.dom,$m)),this.onCharData&&this.view.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.connectSelection()}stop(){if(this.observer){let e=this.observer.takeRecords();if(e.length){for(let n=0;n<e.length;n++)this.queue.push(e[n]);window.setTimeout(()=>this.flush(),20)}this.observer.disconnect()}this.onCharData&&this.view.dom.removeEventListener("DOMCharacterDataModified",this.onCharData),this.disconnectSelection()}connectSelection(){this.view.dom.ownerDocument.addEventListener("selectionchange",this.onSelectionChange)}disconnectSelection(){this.view.dom.ownerDocument.removeEventListener("selectionchange",this.onSelectionChange)}suppressSelectionUpdates(){this.suppressingSelectionUpdates=!0,setTimeout(()=>this.suppressingSelectionUpdates=!1,50)}onSelectionChange(){if(lc(this.view)){if(this.suppressingSelectionUpdates)return ft(this.view);if(ke&&Tt<=11&&!this.view.state.selection.empty){let e=this.view.domSelectionRange();if(e.focusNode&&Jt(e.focusNode,e.focusOffset,e.anchorNode,e.anchorOffset))return this.flushSoon()}this.flush()}}setCurSelection(){this.currentSelection.set(this.view.domSelectionRange())}ignoreSelectionChange(e){if(!e.focusNode)return!0;let n=new Set,r;for(let i=e.focusNode;i;i=gn(i))n.add(i);for(let i=e.anchorNode;i;i=gn(i))if(n.has(i)){r=i;break}let o=r&&this.view.docView.nearestDesc(r);if(o&&o.ignoreMutation({type:"selection",target:r.nodeType==3?r.parentNode:r}))return this.setCurSelection(),!0}pendingRecords(){if(this.observer)for(let e of this.observer.takeRecords())this.queue.push(e);return this.queue}flush(){let{view:e}=this;if(!e.docView||this.flushingSoon>-1)return;let n=this.pendingRecords();n.length&&(this.queue=[]);let r=e.domSelectionRange(),o=!this.suppressingSelectionUpdates&&!this.currentSelection.eq(r)&&lc(e)&&!this.ignoreSelectionChange(r),i=-1,s=-1,l=!1,a=[];if(e.editable)for(let u=0;u<n.length;u++){let d=this.registerMutation(n[u],a);d&&(i=i<0?d.from:Math.min(d.from,i),s=s<0?d.to:Math.max(d.to,s),d.typeOver&&(l=!0))}if(Ve&&a.length){let u=a.filter(d=>d.nodeName=="BR");if(u.length==2){let[d,f]=u;d.parentNode&&d.parentNode.parentNode==f.parentNode?f.remove():d.remove()}else{let{focusNode:d}=this.currentSelection;for(let f of u){let h=f.parentNode;h&&h.nodeName=="LI"&&(!d||jm(e,d)!=h)&&f.remove()}}}let c=null;i<0&&o&&e.input.lastFocus>Date.now()-200&&Math.max(e.input.lastTouch,e.input.lastClick.time)<Date.now()-300&&Kr(r)&&(c=cs(e))&&c.eq(E.near(e.state.doc.resolve(0),1))?(e.input.lastFocus=0,ft(e),this.currentSelection.set(r),e.scrollToSelection()):(i>-1||o)&&(i>-1&&(e.docView.markDirty(i,s),Wm(e)),this.handleDOMChange(i,s,l,a),e.docView&&e.docView.dirty?e.updateState(e.state):this.currentSelection.eq(r)||ft(e),this.currentSelection.set(r))}registerMutation(e,n){if(n.indexOf(e.target)>-1)return null;let r=this.view.docView.nearestDesc(e.target);if(e.type=="attributes"&&(r==this.view.docView||e.attributeName=="contenteditable"||e.attributeName=="style"&&!e.oldValue&&!e.target.getAttribute("style"))||!r||r.ignoreMutation(e))return null;if(e.type=="childList"){for(let u=0;u<e.addedNodes.length;u++){let d=e.addedNodes[u];n.push(d),d.nodeType==3&&(this.lastChangedTextNode=d)}if(r.contentDOM&&r.contentDOM!=r.dom&&!r.contentDOM.contains(e.target))return{from:r.posBefore,to:r.posAfter};let o=e.previousSibling,i=e.nextSibling;if(ke&&Tt<=11&&e.addedNodes.length)for(let u=0;u<e.addedNodes.length;u++){let{previousSibling:d,nextSibling:f}=e.addedNodes[u];(!d||Array.prototype.indexOf.call(e.addedNodes,d)<0)&&(o=d),(!f||Array.prototype.indexOf.call(e.addedNodes,f)<0)&&(i=f)}let s=o&&o.parentNode==e.target?ue(o)+1:0,l=r.localPosFromDOM(e.target,s,-1),a=i&&i.parentNode==e.target?ue(i):e.target.childNodes.length,c=r.localPosFromDOM(e.target,a,1);return{from:l,to:c}}else return e.type=="attributes"?{from:r.posAtStart-r.border,to:r.posAtEnd+r.border}:(this.lastChangedTextNode=e.target,{from:r.posAtStart,to:r.posAtEnd,typeOver:e.target.nodeValue==e.oldValue})}},gc=new WeakMap,yc=!1;function Wm(t){if(!gc.has(t)&&(gc.set(t,null),["normal","nowrap","pre-line"].indexOf(getComputedStyle(t.dom).whiteSpace)!==-1)){if(t.requiresGeckoHackNode=Ve,yc)return;console.warn("ProseMirror expects the CSS white-space property to be set, preferably to 'pre-wrap'. It is recommended to load style/prosemirror.css from the prosemirror-view package."),yc=!0}}function bc(t,e){let n=e.startContainer,r=e.startOffset,o=e.endContainer,i=e.endOffset,s=t.domAtPos(t.state.selection.anchor);return Jt(s.node,s.offset,o,i)&&([n,r,o,i]=[o,i,n,r]),{anchorNode:n,anchorOffset:r,focusNode:o,focusOffset:i}}function _m(t,e){if(e.getComposedRanges){let o=e.getComposedRanges(t.root)[0];if(o)return bc(t,o)}let n;function r(o){o.preventDefault(),o.stopImmediatePropagation(),n=o.getTargetRanges()[0]}return t.dom.addEventListener("beforeinput",r,!0),document.execCommand("indent"),t.dom.removeEventListener("beforeinput",r,!0),n?bc(t,n):null}function jm(t,e){for(let n=e.parentNode;n&&n!=t.dom;n=n.parentNode){let r=t.docView.nearestDesc(n,!0);if(r&&r.node.isBlock)return n}return null}function Km(t,e,n){let{node:r,fromOffset:o,toOffset:i,from:s,to:l}=t.docView.parseRange(e,n),a=t.domSelectionRange(),c,u=a.anchorNode;if(u&&t.dom.contains(u.nodeType==1?u:u.parentNode)&&(c=[{node:u,offset:a.anchorOffset}],Kr(a)||c.push({node:a.focusNode,offset:a.focusOffset})),ge&&t.input.lastKeyCode===8)for(let g=i;g>o;g--){let y=r.childNodes[g-1],b=y.pmViewDesc;if(y.nodeName=="BR"&&!b){i=g;break}if(!b||b.size)break}let d=t.state.doc,f=t.someProp("domParser")||Ue.fromSchema(t.state.schema),h=d.resolve(s),p=null,m=f.parse(r,{topNode:h.parent,topMatch:h.parent.contentMatchAt(h.index()),topOpen:!0,from:o,to:i,preserveWhitespace:h.parent.type.whitespace=="pre"?"full":!0,findPositions:c,ruleFromNode:Um,context:h});if(c&&c[0].pos!=null){let g=c[0].pos,y=c[1]&&c[1].pos;y==null&&(y=g),p={anchor:g+s,head:y+s}}return{doc:m,sel:p,from:s,to:l}}function Um(t){let e=t.pmViewDesc;if(e)return e.parseRule();if(t.nodeName=="BR"&&t.parentNode){if(xe&&/^(ul|ol)$/i.test(t.parentNode.nodeName)){let n=document.createElement("div");return n.appendChild(document.createElement("li")),{skip:n}}else if(t.parentNode.lastChild==t||xe&&/^(tr|table)$/i.test(t.parentNode.nodeName))return{ignore:!0}}else if(t.nodeName=="IMG"&&t.getAttribute("mark-placeholder"))return{ignore:!0};return null}var Jm=/^(a|abbr|acronym|b|bd[io]|big|br|button|cite|code|data(list)?|del|dfn|em|i|ins|kbd|label|map|mark|meter|output|q|ruby|s|samp|small|span|strong|su[bp]|time|u|tt|var)$/i;function qm(t,e,n,r,o){let i=t.input.compositionPendingChanges||(t.composing?t.input.compositionID:0);if(t.input.compositionPendingChanges=0,e<0){let v=t.input.lastSelectionTime>Date.now()-50?t.input.lastSelectionOrigin:null,B=cs(t,v);if(B&&!t.state.selection.eq(B)){if(ge&&dt&&t.input.lastKeyCode===13&&Date.now()-100<t.input.lastKeyCodeTime&&t.someProp("handleKeyDown",_=>_(t,Vt(13,"Enter"))))return;let D=t.state.tr.setSelection(B);v=="pointer"?D.setMeta("pointer",!0):v=="key"&&D.scrollIntoView(),i&&D.setMeta("composition",i),t.dispatch(D)}return}let s=t.state.doc.resolve(e),l=s.sharedDepth(n);e=s.before(l+1),n=t.state.doc.resolve(n).after(l+1);let a=t.state.selection,c=Km(t,e,n),u=t.state.doc,d=u.slice(c.from,c.to),f,h;t.input.lastKeyCode===8&&Date.now()-100<t.input.lastKeyCodeTime?(f=t.state.selection.to,h="end"):(f=t.state.selection.from,h="start"),t.input.lastKeyCode=null;let p=Xm(d.content,c.doc.content,c.from,f,h);if(p&&t.input.domChangeCount++,(yn&&t.input.lastIOSEnter>Date.now()-225||dt)&&o.some(v=>v.nodeType==1&&!Jm.test(v.nodeName))&&(!p||p.endA>=p.endB)&&t.someProp("handleKeyDown",v=>v(t,Vt(13,"Enter")))){t.input.lastIOSEnter=0;return}if(!p)if(r&&a instanceof A&&!a.empty&&a.$head.sameParent(a.$anchor)&&!t.composing&&!(c.sel&&c.sel.anchor!=c.sel.head))p={start:a.from,endA:a.to,endB:a.to};else{if(c.sel){let v=xc(t,t.state.doc,c.sel);if(v&&!v.eq(t.state.selection)){let B=t.state.tr.setSelection(v);i&&B.setMeta("composition",i),t.dispatch(B)}}return}t.state.selection.from<t.state.selection.to&&p.start==p.endB&&t.state.selection instanceof A&&(p.start>t.state.selection.from&&p.start<=t.state.selection.from+2&&t.state.selection.from>=c.from?p.start=t.state.selection.from:p.endA<t.state.selection.to&&p.endA>=t.state.selection.to-2&&t.state.selection.to<=c.to&&(p.endB+=t.state.selection.to-p.endA,p.endA=t.state.selection.to)),ke&&Tt<=11&&p.endB==p.start+1&&p.endA==p.start&&p.start>c.from&&c.doc.textBetween(p.start-c.from-1,p.start-c.from+1)==" \xA0"&&(p.start--,p.endA--,p.endB--);let m=c.doc.resolveNoCache(p.start-c.from),g=c.doc.resolveNoCache(p.endB-c.from),y=u.resolve(p.start),b=m.sameParent(g)&&m.parent.inlineContent&&y.end()>=p.endA,x;if((yn&&t.input.lastIOSEnter>Date.now()-225&&(!b||o.some(v=>v.nodeName=="DIV"||v.nodeName=="P"))||!b&&m.pos<c.doc.content.size&&(!m.sameParent(g)||!m.parent.inlineContent)&&!/\S/.test(c.doc.textBetween(m.pos,g.pos,"",""))&&(x=E.findFrom(c.doc.resolve(m.pos+1),1,!0))&&x.head>m.pos)&&t.someProp("handleKeyDown",v=>v(t,Vt(13,"Enter")))){t.input.lastIOSEnter=0;return}if(t.state.selection.anchor>p.start&&Ym(u,p.start,p.endA,m,g)&&t.someProp("handleKeyDown",v=>v(t,Vt(8,"Backspace")))){dt&&ge&&t.domObserver.suppressSelectionUpdates();return}ge&&p.endB==p.start&&(t.input.lastChromeDelete=Date.now()),dt&&!b&&m.start()!=g.start()&&g.parentOffset==0&&m.depth==g.depth&&c.sel&&c.sel.anchor==c.sel.head&&c.sel.head==p.endA&&(p.endB-=2,g=c.doc.resolveNoCache(p.endB-c.from),setTimeout(()=>{t.someProp("handleKeyDown",function(v){return v(t,Vt(13,"Enter"))})},20));let C=p.start,S=p.endA,R=v=>{let B=v||t.state.tr.replace(C,S,c.doc.slice(p.start-c.from,p.endB-c.from));if(c.sel){let D=xc(t,B.doc,c.sel);D&&!(ge&&t.composing&&D.empty&&(p.start!=p.endB||t.input.lastChromeDelete<Date.now()-100)&&(D.head==C||D.head==B.mapping.map(S)-1)||ke&&D.empty&&D.head==C)&&B.setSelection(D)}return i&&B.setMeta("composition",i),B.scrollIntoView()},P;if(b){if(m.pos==g.pos){ke&&Tt<=11&&m.parentOffset==0&&(t.domObserver.suppressSelectionUpdates(),setTimeout(()=>ft(t),20));let v=R(t.state.tr.delete(C,S)),B=u.resolve(p.start).marksAcross(u.resolve(p.endA));B&&v.ensureMarks(B),t.dispatch(v)}else if(p.endA==p.endB&&(P=Gm(m.parent.content.cut(m.parentOffset,g.parentOffset),y.parent.content.cut(y.parentOffset,p.endA-y.start())))){let v=R(t.state.tr);P.type=="add"?v.addMark(C,S,P.mark):v.removeMark(C,S,P.mark),t.dispatch(v)}else if(m.parent.child(m.index()).isText&&m.index()==g.index()-(g.textOffset?0:1)){let v=m.parent.textBetween(m.parentOffset,g.parentOffset),B=()=>R(t.state.tr.insertText(v,C,S));t.someProp("handleTextInput",D=>D(t,C,S,v,B))||t.dispatch(B())}}else t.dispatch(R())}function xc(t,e,n){return Math.max(n.anchor,n.head)>e.content.size?null:us(t,e.resolve(n.anchor),e.resolve(n.head))}function Gm(t,e){let n=t.firstChild.marks,r=e.firstChild.marks,o=n,i=r,s,l,a;for(let u=0;u<r.length;u++)o=r[u].removeFromSet(o);for(let u=0;u<n.length;u++)i=n[u].removeFromSet(i);if(o.length==1&&i.length==0)l=o[0],s="add",a=u=>u.mark(l.addToSet(u.marks));else if(o.length==0&&i.length==1)l=i[0],s="remove",a=u=>u.mark(l.removeFromSet(u.marks));else return null;let c=[];for(let u=0;u<e.childCount;u++)c.push(a(e.child(u)));if(w.from(c).eq(t))return{mark:l,type:s}}function Ym(t,e,n,r,o){if(n-e<=o.pos-r.pos||Wi(r,!0,!1)<o.pos)return!1;let i=t.resolve(e);if(!r.parent.isTextblock){let l=i.nodeAfter;return l!=null&&n==e+l.nodeSize}if(i.parentOffset<i.parent.content.size||!i.parent.isTextblock)return!1;let s=t.resolve(Wi(i,!0,!0));return!s.parent.isTextblock||s.pos>n||Wi(s,!0,!1)<n?!1:r.parent.content.cut(r.parentOffset).eq(s.parent.content)}function Wi(t,e,n){let r=t.depth,o=e?t.end():t.pos;for(;r>0&&(e||t.indexAfter(r)==t.node(r).childCount);)r--,o++,e=!1;if(n){let i=t.node(r).maybeChild(t.indexAfter(r));for(;i&&!i.isLeaf;)i=i.firstChild,o++}return o}function Xm(t,e,n,r,o){let i=t.findDiffStart(e,n);if(i==null)return null;let{a:s,b:l}=t.findDiffEnd(e,n+t.size,n+e.size);if(o=="end"){let a=Math.max(0,i-Math.min(s,l));r-=s+a-i}if(s<i&&t.size<e.size){let a=r<=i&&r>=s?i-r:0;i-=a,i&&i<e.size&&wc(e.textBetween(i-1,i+1))&&(i+=a?1:-1),l=i+(l-s),s=i}else if(l<i){let a=r<=i&&r>=l?i-r:0;i-=a,i&&i<t.size&&wc(t.textBetween(i-1,i+1))&&(i+=a?1:-1),s=i+(s-l),l=i}return{start:i,endA:s,endB:l}}function wc(t){if(t.length!=2)return!1;let e=t.charCodeAt(0),n=t.charCodeAt(1);return e>=56320&&e<=57343&&n>=55296&&n<=56319}var qn=class{constructor(e,n){this._root=null,this.focused=!1,this.trackWrites=null,this.mounted=!1,this.markCursor=null,this.cursorWrapper=null,this.lastSelectedViewDesc=void 0,this.input=new ts,this.prevDirectPlugins=[],this.pluginViews=[],this.requiresGeckoHackNode=!1,this.dragging=null,this._props=n,this.state=n.state,this.directPlugins=n.plugins||[],this.directPlugins.forEach(vc),this.dispatch=this.dispatch.bind(this),this.dom=e&&e.mount||document.createElement("div"),e&&(e.appendChild?e.appendChild(this.dom):typeof e=="function"?e(this.dom):e.mount&&(this.mounted=!0)),this.editable=Cc(this),kc(this),this.nodeViews=Mc(this),this.docView=tc(this.state.doc,Sc(this),Vi(this),this.dom,this),this.domObserver=new ls(this,(r,o,i,s)=>qm(this,r,o,i,s)),this.domObserver.start(),wm(this),this.updatePluginViews()}get composing(){return this.input.composing}get props(){if(this._props.state!=this.state){let e=this._props;this._props={};for(let n in e)this._props[n]=e[n];this._props.state=this.state}return this._props}update(e){e.handleDOMEvents!=this._props.handleDOMEvents&&ns(this);let n=this._props;this._props=e,e.plugins&&(e.plugins.forEach(vc),this.directPlugins=e.plugins),this.updateStateInner(e.state,n)}setProps(e){let n={};for(let r in this._props)n[r]=this._props[r];n.state=this.state;for(let r in e)n[r]=e[r];this.update(n)}updateState(e){this.updateStateInner(e,this._props)}updateStateInner(e,n){var r;let o=this.state,i=!1,s=!1;e.storedMarks&&this.composing&&(Yc(this),s=!0),this.state=e;let l=o.plugins!=e.plugins||this._props.plugins!=n.plugins;if(l||this._props.plugins!=n.plugins||this._props.nodeViews!=n.nodeViews){let h=Mc(this);Zm(h,this.nodeViews)&&(this.nodeViews=h,i=!0)}(l||n.handleDOMEvents!=this._props.handleDOMEvents)&&ns(this),this.editable=Cc(this),kc(this);let a=Vi(this),c=Sc(this),u=o.plugins!=e.plugins&&!o.doc.eq(e.doc)?"reset":e.scrollToSelection>o.scrollToSelection?"to selection":"preserve",d=i||!this.docView.matchesNode(e.doc,c,a);(d||!e.selection.eq(o.selection))&&(s=!0);let f=u=="preserve"&&s&&this.dom.style.overflowAnchor==null&&Hp(this);if(s){this.domObserver.stop();let h=d&&(ke||ge)&&!this.composing&&!o.selection.empty&&!e.selection.empty&&Qm(o.selection,e.selection);if(d){let p=ge?this.trackWrites=this.domSelectionRange().focusNode:null;this.composing&&(this.input.compositionNode=Dm(this)),(i||!this.docView.update(e.doc,c,a,this))&&(this.docView.updateOuterDeco(c),this.docView.destroy(),this.docView=tc(e.doc,c,a,this.dom,this)),p&&!this.trackWrites&&(h=!0)}h||!(this.input.mouseDown&&this.domObserver.currentSelection.eq(this.domSelectionRange())&&im(this))?ft(this,h):(Hc(this,e.selection),this.domObserver.setCurSelection()),this.domObserver.start()}this.updatePluginViews(o),!((r=this.dragging)===null||r===void 0)&&r.node&&!o.doc.eq(e.doc)&&this.updateDraggedNode(this.dragging,o),u=="reset"?this.dom.scrollTop=0:u=="to selection"?this.scrollToSelection():f&&Fp(f)}scrollToSelection(){let e=this.domSelectionRange().focusNode;if(!(!e||!this.dom.contains(e.nodeType==1?e:e.parentNode))){if(!this.someProp("handleScrollToSelection",n=>n(this)))if(this.state.selection instanceof O){let n=this.docView.domAfterPos(this.state.selection.from);n.nodeType==1&&Ga(this,n.getBoundingClientRect(),e)}else Ga(this,this.coordsAtPos(this.state.selection.head,1),e)}}destroyPluginViews(){let e;for(;e=this.pluginViews.pop();)e.destroy&&e.destroy()}updatePluginViews(e){if(!e||e.plugins!=this.state.plugins||this.directPlugins!=this.prevDirectPlugins){this.prevDirectPlugins=this.directPlugins,this.destroyPluginViews();for(let n=0;n<this.directPlugins.length;n++){let r=this.directPlugins[n];r.spec.view&&this.pluginViews.push(r.spec.view(this))}for(let n=0;n<this.state.plugins.length;n++){let r=this.state.plugins[n];r.spec.view&&this.pluginViews.push(r.spec.view(this))}}else for(let n=0;n<this.pluginViews.length;n++){let r=this.pluginViews[n];r.update&&r.update(this,e)}}updateDraggedNode(e,n){let r=e.node,o=-1;if(this.state.doc.nodeAt(r.from)==r.node)o=r.from;else{let i=r.from+(this.state.doc.content.size-n.doc.content.size);(i>0&&this.state.doc.nodeAt(i))==r.node&&(o=i)}this.dragging=new Vr(e.slice,e.move,o<0?void 0:O.create(this.state.doc,o))}someProp(e,n){let r=this._props&&this._props[e],o;if(r!=null&&(o=n?n(r):r))return o;for(let s=0;s<this.directPlugins.length;s++){let l=this.directPlugins[s].props[e];if(l!=null&&(o=n?n(l):l))return o}let i=this.state.plugins;if(i)for(let s=0;s<i.length;s++){let l=i[s].props[e];if(l!=null&&(o=n?n(l):l))return o}}hasFocus(){if(ke){let e=this.root.activeElement;if(e==this.dom)return!0;if(!e||!this.dom.contains(e))return!1;for(;e&&this.dom!=e&&this.dom.contains(e);){if(e.contentEditable=="false")return!1;e=e.parentElement}return!0}return this.root.activeElement==this.dom}focus(){this.domObserver.stop(),this.editable&&$p(this.dom),ft(this),this.domObserver.start()}get root(){let e=this._root;if(e==null){for(let n=this.dom.parentNode;n;n=n.parentNode)if(n.nodeType==9||n.nodeType==11&&n.host)return n.getSelection||(Object.getPrototypeOf(n).getSelection=()=>n.ownerDocument.getSelection()),this._root=n}return e||document}updateRoot(){this._root=null}posAtCoords(e){return Kp(this,e)}coordsAtPos(e,n=1){return Dc(this,e,n)}domAtPos(e,n=0){return this.docView.domFromPos(e,n)}nodeDOM(e){let n=this.docView.descAt(e);return n?n.nodeDOM:null}posAtDOM(e,n,r=-1){let o=this.docView.posFromDOM(e,n,r);if(o==null)throw new RangeError("DOM position not inside the editor");return o}endOfTextblock(e,n){return Yp(this,n||this.state,e)}pasteHTML(e,n){return Un(this,"",e,!1,n||new ClipboardEvent("paste"))}pasteText(e,n){return Un(this,e,null,!0,n||new ClipboardEvent("paste"))}serializeForClipboard(e){return ds(this,e)}destroy(){this.docView&&(Sm(this),this.destroyPluginViews(),this.mounted?(this.docView.update(this.state.doc,[],Vi(this),this),this.dom.textContent=""):this.dom.parentNode&&this.dom.parentNode.removeChild(this.dom),this.docView.destroy(),this.docView=null,Ap())}get isDestroyed(){return this.docView==null}dispatchEvent(e){return Cm(this,e)}domSelectionRange(){let e=this.domSelection();return e?xe&&this.root.nodeType===11&&Dp(this.dom.ownerDocument)==this.dom&&_m(this,e)||e:{focusNode:null,focusOffset:0,anchorNode:null,anchorOffset:0}}domSelection(){return this.root.getSelection()}};qn.prototype.dispatch=function(t){let e=this._props.dispatchTransaction;e?e.call(this,t):this.updateState(this.state.apply(t))};function Sc(t){let e=Object.create(null);return e.class="ProseMirror",e.contenteditable=String(t.editable),t.someProp("attributes",n=>{if(typeof n=="function"&&(n=n(t.state)),n)for(let r in n)r=="class"?e.class+=" "+n[r]:r=="style"?e.style=(e.style?e.style+";":"")+n[r]:!e[r]&&r!="contenteditable"&&r!="nodeName"&&(e[r]=String(n[r]))}),e.translate||(e.translate="no"),[Y.node(0,t.state.doc.content.size,e)]}function kc(t){if(t.markCursor){let e=document.createElement("img");e.className="ProseMirror-separator",e.setAttribute("mark-placeholder","true"),e.setAttribute("alt",""),t.cursorWrapper={dom:e,deco:Y.widget(t.state.selection.from,e,{raw:!0,marks:t.markCursor})}}else t.cursorWrapper=null}function Cc(t){return!t.someProp("editable",e=>e(t.state)===!1)}function Qm(t,e){let n=Math.min(t.$anchor.sharedDepth(t.head),e.$anchor.sharedDepth(e.head));return t.$anchor.start(n)!=e.$anchor.start(n)}function Mc(t){let e=Object.create(null);function n(r){for(let o in r)Object.prototype.hasOwnProperty.call(e,o)||(e[o]=r[o])}return t.someProp("nodeViews",n),t.someProp("markViews",n),e}function Zm(t,e){let n=0,r=0;for(let o in t){if(t[o]!=e[o])return!0;n++}for(let o in e)r++;return n!=r}function vc(t){if(t.spec.state||t.spec.filterTransaction||t.spec.appendTransaction)throw new RangeError("Plugins passed directly to the view must not have a state component")}var ht={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},qr={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},eg=typeof navigator<"u"&&/Mac/.test(navigator.platform),tg=typeof navigator<"u"&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent);for(re=0;re<10;re++)ht[48+re]=ht[96+re]=String(re);var re;for(re=1;re<=24;re++)ht[re+111]="F"+re;var re;for(re=65;re<=90;re++)ht[re]=String.fromCharCode(re+32),qr[re]=String.fromCharCode(re);var re;for(Jr in ht)qr.hasOwnProperty(Jr)||(qr[Jr]=ht[Jr]);var Jr;function nu(t){var e=eg&&t.metaKey&&t.shiftKey&&!t.ctrlKey&&!t.altKey||tg&&t.shiftKey&&t.key&&t.key.length==1||t.key=="Unidentified",n=!e&&t.key||(t.shiftKey?qr:ht)[t.keyCode]||t.key||"Unidentified";return n=="Esc"&&(n="Escape"),n=="Del"&&(n="Delete"),n=="Left"&&(n="ArrowLeft"),n=="Up"&&(n="ArrowUp"),n=="Right"&&(n="ArrowRight"),n=="Down"&&(n="ArrowDown"),n}var ng=typeof navigator<"u"&&/Mac|iP(hone|[oa]d)/.test(navigator.platform),rg=typeof navigator<"u"&&/Win/.test(navigator.platform);function og(t){let e=t.split(/-(?!$)/),n=e[e.length-1];n=="Space"&&(n=" ");let r,o,i,s;for(let l=0;l<e.length-1;l++){let a=e[l];if(/^(cmd|meta|m)$/i.test(a))s=!0;else if(/^a(lt)?$/i.test(a))r=!0;else if(/^(c|ctrl|control)$/i.test(a))o=!0;else if(/^s(hift)?$/i.test(a))i=!0;else if(/^mod$/i.test(a))ng?s=!0:o=!0;else throw new Error("Unrecognized modifier name: "+a)}return r&&(n="Alt-"+n),o&&(n="Ctrl-"+n),s&&(n="Meta-"+n),i&&(n="Shift-"+n),n}function ig(t){let e=Object.create(null);for(let n in t)e[og(n)]=t[n];return e}function gs(t,e,n=!0){return e.altKey&&(t="Alt-"+t),e.ctrlKey&&(t="Ctrl-"+t),e.metaKey&&(t="Meta-"+t),n&&e.shiftKey&&(t="Shift-"+t),t}function ru(t){return new N({props:{handleKeyDown:Xn(t)}})}function Xn(t){let e=ig(t);return function(n,r){let o=nu(r),i,s=e[gs(o,r)];if(s&&s(n.state,n.dispatch,n))return!0;if(o.length==1&&o!=" "){if(r.shiftKey){let l=e[gs(o,r,!1)];if(l&&l(n.state,n.dispatch,n))return!0}if((r.altKey||r.metaKey||r.ctrlKey)&&!(rg&&r.ctrlKey&&r.altKey)&&(i=ht[r.keyCode])&&i!=o){let l=e[gs(i,r)];if(l&&l(n.state,n.dispatch,n))return!0}}return!1}}var Gr=(t,e)=>t.selection.empty?!1:(e&&e(t.tr.deleteSelection().scrollIntoView()),!0);function iu(t,e){let{$cursor:n}=t.selection;return!n||(e?!e.endOfTextblock("backward",t):n.parentOffset>0)?null:n}var bs=(t,e,n)=>{let r=iu(t,n);if(!r)return!1;let o=ws(r);if(!o){let s=r.blockRange(),l=s&&at(s);return l==null?!1:(e&&e(t.tr.lift(s,l).scrollIntoView()),!0)}let i=o.nodeBefore;if(pu(t,o,e,-1))return!0;if(r.parent.content.size==0&&(xn(i,"end")||O.isSelectable(i)))for(let s=r.depth;;s--){let l=$n(t.doc,r.before(s),r.after(s),M.empty);if(l&&l.slice.size<l.to-l.from){if(e){let a=t.tr.step(l);a.setSelection(xn(i,"end")?E.findFrom(a.doc.resolve(a.mapping.map(o.pos,-1)),-1):O.create(a.doc,o.pos-i.nodeSize)),e(a.scrollIntoView())}return!0}if(s==1||r.node(s-1).childCount>1)break}return i.isAtom&&o.depth==r.depth-1?(e&&e(t.tr.delete(o.pos-i.nodeSize,o.pos).scrollIntoView()),!0):!1},su=(t,e,n)=>{let r=iu(t,n);if(!r)return!1;let o=ws(r);return o?au(t,o,e):!1},lu=(t,e,n)=>{let r=cu(t,n);if(!r)return!1;let o=Cs(r);return o?au(t,o,e):!1};function au(t,e,n){let r=e.nodeBefore,o=r,i=e.pos-1;for(;!o.isTextblock;i--){if(o.type.spec.isolating)return!1;let u=o.lastChild;if(!u)return!1;o=u}let s=e.nodeAfter,l=s,a=e.pos+1;for(;!l.isTextblock;a++){if(l.type.spec.isolating)return!1;let u=l.firstChild;if(!u)return!1;l=u}let c=$n(t.doc,i,a,M.empty);if(!c||c.from!=i||c instanceof pe&&c.slice.size>=a-i)return!1;if(n){let u=t.tr.step(c);u.setSelection(A.create(u.doc,i)),n(u.scrollIntoView())}return!0}function xn(t,e,n=!1){for(let r=t;r;r=e=="start"?r.firstChild:r.lastChild){if(r.isTextblock)return!0;if(n&&r.childCount!=1)return!1}return!1}var xs=(t,e,n)=>{let{$head:r,empty:o}=t.selection,i=r;if(!o)return!1;if(r.parent.isTextblock){if(n?!n.endOfTextblock("backward",t):r.parentOffset>0)return!1;i=ws(r)}let s=i&&i.nodeBefore;return!s||!O.isSelectable(s)?!1:(e&&e(t.tr.setSelection(O.create(t.doc,i.pos-s.nodeSize)).scrollIntoView()),!0)};function ws(t){if(!t.parent.type.spec.isolating)for(let e=t.depth-1;e>=0;e--){if(t.index(e)>0)return t.doc.resolve(t.before(e+1));if(t.node(e).type.spec.isolating)break}return null}function cu(t,e){let{$cursor:n}=t.selection;return!n||(e?!e.endOfTextblock("forward",t):n.parentOffset<n.parent.content.size)?null:n}var Ss=(t,e,n)=>{let r=cu(t,n);if(!r)return!1;let o=Cs(r);if(!o)return!1;let i=o.nodeAfter;if(pu(t,o,e,1))return!0;if(r.parent.content.size==0&&(xn(i,"start")||O.isSelectable(i))){let s=$n(t.doc,r.before(),r.after(),M.empty);if(s&&s.slice.size<s.to-s.from){if(e){let l=t.tr.step(s);l.setSelection(xn(i,"start")?E.findFrom(l.doc.resolve(l.mapping.map(o.pos)),1):O.create(l.doc,l.mapping.map(o.pos))),e(l.scrollIntoView())}return!0}}return i.isAtom&&o.depth==r.depth-1?(e&&e(t.tr.delete(o.pos,o.pos+i.nodeSize).scrollIntoView()),!0):!1},ks=(t,e,n)=>{let{$head:r,empty:o}=t.selection,i=r;if(!o)return!1;if(r.parent.isTextblock){if(n?!n.endOfTextblock("forward",t):r.parentOffset<r.parent.content.size)return!1;i=Cs(r)}let s=i&&i.nodeAfter;return!s||!O.isSelectable(s)?!1:(e&&e(t.tr.setSelection(O.create(t.doc,i.pos)).scrollIntoView()),!0)};function Cs(t){if(!t.parent.type.spec.isolating)for(let e=t.depth-1;e>=0;e--){let n=t.node(e);if(t.index(e)+1<n.childCount)return t.doc.resolve(t.after(e+1));if(n.type.spec.isolating)break}return null}var uu=(t,e)=>{let n=t.selection,r=n instanceof O,o;if(r){if(n.node.isTextblock||!Ne(t.doc,n.from))return!1;o=n.from}else if(o=Ft(t.doc,n.from,-1),o==null)return!1;if(e){let i=t.tr.join(o);r&&i.setSelection(O.create(i.doc,o-t.doc.resolve(o).nodeBefore.nodeSize)),e(i.scrollIntoView())}return!0},du=(t,e)=>{let n=t.selection,r;if(n instanceof O){if(n.node.isTextblock||!Ne(t.doc,n.to))return!1;r=n.to}else if(r=Ft(t.doc,n.to,1),r==null)return!1;return e&&e(t.tr.join(r).scrollIntoView()),!0},fu=(t,e)=>{let{$from:n,$to:r}=t.selection,o=n.blockRange(r),i=o&&at(o);return i==null?!1:(e&&e(t.tr.lift(o,i).scrollIntoView()),!0)},Ms=(t,e)=>{let{$head:n,$anchor:r}=t.selection;return!n.parent.type.spec.code||!n.sameParent(r)?!1:(e&&e(t.tr.insertText(`
`).scrollIntoView()),!0)};function vs(t){for(let e=0;e<t.edgeCount;e++){let{type:n}=t.edge(e);if(n.isTextblock&&!n.hasRequiredAttrs())return n}return null}var Ts=(t,e)=>{let{$head:n,$anchor:r}=t.selection;if(!n.parent.type.spec.code||!n.sameParent(r))return!1;let o=n.node(-1),i=n.indexAfter(-1),s=vs(o.contentMatchAt(i));if(!s||!o.canReplaceWith(i,i,s))return!1;if(e){let l=n.after(),a=t.tr.replaceWith(l,l,s.createAndFill());a.setSelection(E.near(a.doc.resolve(l),1)),e(a.scrollIntoView())}return!0},As=(t,e)=>{let n=t.selection,{$from:r,$to:o}=n;if(n instanceof be||r.parent.inlineContent||o.parent.inlineContent)return!1;let i=vs(o.parent.contentMatchAt(o.indexAfter()));if(!i||!i.isTextblock)return!1;if(e){let s=(!r.parentOffset&&o.index()<o.parent.childCount?r:o).pos,l=t.tr.insert(s,i.createAndFill());l.setSelection(A.create(l.doc,s+1)),e(l.scrollIntoView())}return!0},Es=(t,e)=>{let{$cursor:n}=t.selection;if(!n||n.parent.content.size)return!1;if(n.depth>1&&n.after()!=n.end(-1)){let i=n.before();if(Ae(t.doc,i))return e&&e(t.tr.split(i).scrollIntoView()),!0}let r=n.blockRange(),o=r&&at(r);return o==null?!1:(e&&e(t.tr.lift(r,o).scrollIntoView()),!0)};function sg(t){return(e,n)=>{let{$from:r,$to:o}=e.selection;if(e.selection instanceof O&&e.selection.node.isBlock)return!r.parentOffset||!Ae(e.doc,r.pos)?!1:(n&&n(e.tr.split(r.pos).scrollIntoView()),!0);if(!r.depth)return!1;let i=[],s,l,a=!1,c=!1;for(let h=r.depth;;h--)if(r.node(h).isBlock){a=r.end(h)==r.pos+(r.depth-h),c=r.start(h)==r.pos-(r.depth-h),l=vs(r.node(h-1).contentMatchAt(r.indexAfter(h-1)));let m=t&&t(o.parent,a,r);i.unshift(m||(a&&l?{type:l}:null)),s=h;break}else{if(h==1)return!1;i.unshift(null)}let u=e.tr;(e.selection instanceof A||e.selection instanceof be)&&u.deleteSelection();let d=u.mapping.map(r.pos),f=Ae(u.doc,d,i.length,i);if(f||(i[0]=l?{type:l}:null,f=Ae(u.doc,d,i.length,i)),!f)return!1;if(u.split(d,i.length,i),!a&&c&&r.node(s).type!=l){let h=u.mapping.map(r.before(s)),p=u.doc.resolve(h);l&&r.node(s-1).canReplaceWith(p.index(),p.index()+1,l)&&u.setNodeMarkup(u.mapping.map(r.before(s)),l)}return n&&n(u.scrollIntoView()),!0}}var lg=sg();var hu=(t,e)=>{let{$from:n,to:r}=t.selection,o,i=n.sharedDepth(r);return i==0?!1:(o=n.before(i),e&&e(t.tr.setSelection(O.create(t.doc,o))),!0)},ag=(t,e)=>(e&&e(t.tr.setSelection(new be(t.doc))),!0);function cg(t,e,n){let r=e.nodeBefore,o=e.nodeAfter,i=e.index();return!r||!o||!r.type.compatibleContent(o.type)?!1:!r.content.size&&e.parent.canReplace(i-1,i)?(n&&n(t.tr.delete(e.pos-r.nodeSize,e.pos).scrollIntoView()),!0):!e.parent.canReplace(i,i+1)||!(o.isTextblock||Ne(t.doc,e.pos))?!1:(n&&n(t.tr.join(e.pos).scrollIntoView()),!0)}function pu(t,e,n,r){let o=e.nodeBefore,i=e.nodeAfter,s,l,a=o.type.spec.isolating||i.type.spec.isolating;if(!a&&cg(t,e,n))return!0;let c=!a&&e.parent.canReplace(e.index(),e.index()+1);if(c&&(s=(l=o.contentMatchAt(o.childCount)).findWrapping(i.type))&&l.matchType(s[0]||i.type).validEnd){if(n){let h=e.pos+i.nodeSize,p=w.empty;for(let y=s.length-1;y>=0;y--)p=w.from(s[y].create(null,p));p=w.from(o.copy(p));let m=t.tr.step(new ne(e.pos-1,h,e.pos,h,new M(p,1,0),s.length,!0)),g=m.doc.resolve(h+2*s.length);g.nodeAfter&&g.nodeAfter.type==o.type&&Ne(m.doc,g.pos)&&m.join(g.pos),n(m.scrollIntoView())}return!0}let u=i.type.spec.isolating||r>0&&a?null:E.findFrom(e,1),d=u&&u.$from.blockRange(u.$to),f=d&&at(d);if(f!=null&&f>=e.depth)return n&&n(t.tr.lift(d,f).scrollIntoView()),!0;if(c&&xn(i,"start",!0)&&xn(o,"end")){let h=o,p=[];for(;p.push(h),!h.isTextblock;)h=h.lastChild;let m=i,g=1;for(;!m.isTextblock;m=m.firstChild)g++;if(h.canReplace(h.childCount,h.childCount,m.content)){if(n){let y=w.empty;for(let x=p.length-1;x>=0;x--)y=w.from(p[x].copy(y));let b=t.tr.step(new ne(e.pos-p.length,e.pos+i.nodeSize,e.pos+g,e.pos+i.nodeSize-g,new M(y,p.length,0),0,!0));n(b.scrollIntoView())}return!0}}return!1}function mu(t){return function(e,n){let r=e.selection,o=t<0?r.$from:r.$to,i=o.depth;for(;o.node(i).isInline;){if(!i)return!1;i--}return o.node(i).isTextblock?(n&&n(e.tr.setSelection(A.create(e.doc,t<0?o.start(i):o.end(i)))),!0):!1}}var Os=mu(-1),Ns=mu(1);function gu(t,e=null){return function(n,r){let{$from:o,$to:i}=n.selection,s=o.blockRange(i),l=s&&cn(s,t,e);return l?(r&&r(n.tr.wrap(s,l).scrollIntoView()),!0):!1}}function Rs(t,e=null){return function(n,r){let o=!1;for(let i=0;i<n.selection.ranges.length&&!o;i++){let{$from:{pos:s},$to:{pos:l}}=n.selection.ranges[i];n.doc.nodesBetween(s,l,(a,c)=>{if(o)return!1;if(!(!a.isTextblock||a.hasMarkup(t,e)))if(a.type==t)o=!0;else{let u=n.doc.resolve(c),d=u.index();o=u.parent.canReplaceWith(d,d+1,t)}})}if(!o)return!1;if(r){let i=n.tr;for(let s=0;s<n.selection.ranges.length;s++){let{$from:{pos:l},$to:{pos:a}}=n.selection.ranges[s];i.setBlockType(l,a,t,e)}r(i.scrollIntoView())}return!0}}function Ds(...t){return function(e,n,r){for(let o=0;o<t.length;o++)if(t[o](e,n,r))return!0;return!1}}var ys=Ds(Gr,bs,xs),ou=Ds(Gr,Ss,ks),Ot={Enter:Ds(Ms,As,Es,lg),"Mod-Enter":Ts,Backspace:ys,"Mod-Backspace":ys,"Shift-Backspace":ys,Delete:ou,"Mod-Delete":ou,"Mod-a":ag},ug={"Ctrl-h":Ot.Backspace,"Alt-Backspace":Ot["Mod-Backspace"],"Ctrl-d":Ot.Delete,"Ctrl-Alt-Backspace":Ot["Mod-Delete"],"Alt-Delete":Ot["Mod-Delete"],"Alt-d":Ot["Mod-Delete"],"Ctrl-a":Os,"Ctrl-e":Ns};for(let t in Ot)ug[t]=Ot[t];var Hw=typeof navigator<"u"?/Mac|iP(hone|[oa]d)/.test(navigator.platform):typeof os<"u"&&os.platform?os.platform()=="darwin":!1;function yu(t,e=null){return function(n,r){let{$from:o,$to:i}=n.selection,s=o.blockRange(i);if(!s)return!1;let l=r?n.tr:null;return dg(l,s,t,e)?(r&&r(l.scrollIntoView()),!0):!1}}function dg(t,e,n,r=null){let o=!1,i=e,s=e.$from.doc;if(e.depth>=2&&e.$from.node(e.depth-1).type.compatibleContent(n)&&e.startIndex==0){if(e.$from.index(e.depth-1)==0)return!1;let a=s.resolve(e.start-2);i=new zt(a,a,e.depth),e.endIndex<e.parent.childCount&&(e=new zt(e.$from,s.resolve(e.$to.end(e.depth)),e.depth)),o=!0}let l=cn(i,n,r,e);return l?(t&&fg(t,e,l,o,n),!0):!1}function fg(t,e,n,r,o){let i=w.empty;for(let u=n.length-1;u>=0;u--)i=w.from(n[u].type.create(n[u].attrs,i));t.step(new ne(e.start-(r?2:0),e.end,e.start,e.end,new M(i,0,0),n.length,!0));let s=0;for(let u=0;u<n.length;u++)n[u].type==o&&(s=u+1);let l=n.length-s,a=e.start+n.length-(r?2:0),c=e.parent;for(let u=e.startIndex,d=e.endIndex,f=!0;u<d;u++,f=!1)!f&&Ae(t.doc,a,l)&&(t.split(a,l),a+=2*l),a+=c.child(u).nodeSize;return t}function bu(t){return function(e,n){let{$from:r,$to:o}=e.selection,i=r.blockRange(o,s=>s.childCount>0&&s.firstChild.type==t);return i?n?r.node(i.depth-1).type==t?hg(e,n,t,i):pg(e,n,i):!0:!1}}function hg(t,e,n,r){let o=t.tr,i=r.end,s=r.$to.end(r.depth);i<s&&(o.step(new ne(i-1,s,i,s,new M(w.from(n.create(null,r.parent.copy())),1,0),1,!0)),r=new zt(o.doc.resolve(r.$from.pos),o.doc.resolve(s),r.depth));let l=at(r);if(l==null)return!1;o.lift(r,l);let a=o.doc.resolve(o.mapping.map(i,-1)-1);return Ne(o.doc,a.pos)&&a.nodeBefore.type==a.nodeAfter.type&&o.join(a.pos),e(o.scrollIntoView()),!0}function pg(t,e,n){let r=t.tr,o=n.parent;for(let h=n.end,p=n.endIndex-1,m=n.startIndex;p>m;p--)h-=o.child(p).nodeSize,r.delete(h-1,h+1);let i=r.doc.resolve(n.start),s=i.nodeAfter;if(r.mapping.map(n.end)!=n.start+i.nodeAfter.nodeSize)return!1;let l=n.startIndex==0,a=n.endIndex==o.childCount,c=i.node(-1),u=i.index(-1);if(!c.canReplace(u+(l?0:1),u+1,s.content.append(a?w.empty:w.from(o))))return!1;let d=i.pos,f=d+s.nodeSize;return r.step(new ne(d-(l?1:0),f+(a?1:0),d+1,f-1,new M((l?w.empty:w.from(o.copy(w.empty))).append(a?w.empty:w.from(o.copy(w.empty))),l?0:1,a?0:1),l?0:1)),e(r.scrollIntoView()),!0}function xu(t){return function(e,n){let{$from:r,$to:o}=e.selection,i=r.blockRange(o,c=>c.childCount>0&&c.firstChild.type==t);if(!i)return!1;let s=i.startIndex;if(s==0)return!1;let l=i.parent,a=l.child(s-1);if(a.type!=t)return!1;if(n){let c=a.lastChild&&a.lastChild.type==l.type,u=w.from(c?t.create():null),d=new M(w.from(t.create(null,w.from(l.type.create(null,u)))),c?3:1,0),f=i.start,h=i.end;n(e.tr.step(new ne(f-(c?3:1),h,f,h,d,1,!0)).scrollIntoView())}return!0}}var mg=Object.defineProperty,Au=(t,e)=>{for(var n in e)mg(t,n,{get:e[n],enumerable:!0})};function ro(t){let{state:e,transaction:n}=t,{selection:r}=n,{doc:o}=n,{storedMarks:i}=n;return{...e,apply:e.apply.bind(e),applyTransaction:e.applyTransaction.bind(e),plugins:e.plugins,schema:e.schema,reconfigure:e.reconfigure.bind(e),toJSON:e.toJSON.bind(e),get storedMarks(){return i},get selection(){return r},get doc(){return o},get tr(){return r=n.selection,o=n.doc,i=n.storedMarks,n}}}var oo=class{constructor(t){this.editor=t.editor,this.rawCommands=this.editor.extensionManager.commands,this.customState=t.state}get hasCustomState(){return!!this.customState}get state(){return this.customState||this.editor.state}get commands(){let{rawCommands:t,editor:e,state:n}=this,{view:r}=e,{tr:o}=n,i=this.buildProps(o);return Object.fromEntries(Object.entries(t).map(([s,l])=>[s,(...c)=>{let u=l(...c)(i);return!o.getMeta("preventDispatch")&&!this.hasCustomState&&r.dispatch(o),u}]))}get chain(){return()=>this.createChain()}get can(){return()=>this.createCan()}createChain(t,e=!0){let{rawCommands:n,editor:r,state:o}=this,{view:i}=r,s=[],l=!!t,a=t||o.tr,c=()=>(!l&&e&&!a.getMeta("preventDispatch")&&!this.hasCustomState&&i.dispatch(a),s.every(d=>d===!0)),u={...Object.fromEntries(Object.entries(n).map(([d,f])=>[d,(...p)=>{let m=this.buildProps(a,e),g=f(...p)(m);return s.push(g),u}])),run:c};return u}createCan(t){let{rawCommands:e,state:n}=this,r=!1,o=t||n.tr,i=this.buildProps(o,r);return{...Object.fromEntries(Object.entries(e).map(([l,a])=>[l,(...c)=>a(...c)({...i,dispatch:void 0})])),chain:()=>this.createChain(o,r)}}buildProps(t,e=!0){let{rawCommands:n,editor:r,state:o}=this,{view:i}=r,s={tr:t,editor:r,view:i,state:ro({state:o,transaction:t}),dispatch:e?()=>{}:void 0,chain:()=>this.createChain(t,e),can:()=>this.createCan(t),get commands(){return Object.fromEntries(Object.entries(n).map(([l,a])=>[l,(...c)=>a(...c)(s)]))}};return s}},gg=class{constructor(){this.callbacks={}}on(t,e){return this.callbacks[t]||(this.callbacks[t]=[]),this.callbacks[t].push(e),this}emit(t,...e){let n=this.callbacks[t];return n&&n.forEach(r=>r.apply(this,e)),this}off(t,e){let n=this.callbacks[t];return n&&(e?this.callbacks[t]=n.filter(r=>r!==e):delete this.callbacks[t]),this}once(t,e){let n=(...r)=>{this.off(t,n),e.apply(this,r)};return this.on(t,n)}removeAllListeners(){this.callbacks={}}};function Hs(t,e){let n=new kt(t);return e.forEach(r=>{r.steps.forEach(o=>{n.step(o)})}),n}var Eu=t=>{let e=t.childNodes;for(let n=e.length-1;n>=0;n-=1){let r=e[n];r.nodeType===3&&r.nodeValue&&/^(\n\s\s|\n)$/.test(r.nodeValue)?t.removeChild(r):r.nodeType===1&&Eu(r)}return t};function Yr(t){if(typeof window>"u")throw new Error("[tiptap error]: there is no window object available, so this function cannot be used");let e=`<body>${t}</body>`,n=new window.DOMParser().parseFromString(e,"text/html").body;return Eu(n)}function Qn(t,e,n){if(t instanceof le||t instanceof w)return t;n={slice:!0,parseOptions:{},...n};let r=typeof t=="object"&&t!==null,o=typeof t=="string";if(r)try{if(Array.isArray(t)&&t.length>0)return w.fromArray(t.map(l=>e.nodeFromJSON(l)));let s=e.nodeFromJSON(t);return n.errorOnInvalidContent&&s.check(),s}catch(i){if(n.errorOnInvalidContent)throw new Error("[tiptap error]: Invalid JSON content",{cause:i});return console.warn("[tiptap warn]: Invalid content.","Passed value:",t,"Error:",i),Qn("",e,n)}if(o){if(n.errorOnInvalidContent){let s=!1,l="",a=new sn({topNode:e.spec.topNode,marks:e.spec.marks,nodes:e.spec.nodes.append({__tiptap__private__unknown__catch__all__node:{content:"inline*",group:"block",parseDOM:[{tag:"*",getAttrs:c=>(s=!0,l=typeof c=="string"?c:c.outerHTML,null)}]}})});if(n.slice?Ue.fromSchema(a).parseSlice(Yr(t),n.parseOptions):Ue.fromSchema(a).parse(Yr(t),n.parseOptions),n.errorOnInvalidContent&&s)throw new Error("[tiptap error]: Invalid HTML content",{cause:new Error(`Invalid element found: ${l}`)})}let i=Ue.fromSchema(e);return n.slice?i.parseSlice(Yr(t),n.parseOptions).content:i.parse(Yr(t),n.parseOptions)}return Qn("",e,n)}function Bs(t,e,n={},r={}){return Qn(t,e,{slice:!1,parseOptions:n,errorOnInvalidContent:r.errorOnInvalidContent})}function er(t){for(let e=0;e<t.edgeCount;e+=1){let{type:n}=t.edge(e);if(n.isTextblock&&!n.hasRequiredAttrs())return n}return null}function Sn(t,e){let n=[];return t.descendants((r,o)=>{e(r)&&n.push({node:r,pos:o})}),n}function Ou(t,e,n){let r=[];return t.nodesBetween(e.from,e.to,(o,i)=>{n(o)&&r.push({node:o,pos:i})}),r}function Fs(t,e){for(let n=t.depth;n>0;n-=1){let r=t.node(n);if(e(r))return{pos:n>0?t.before(n):0,start:t.start(n),depth:n,node:r}}}function Ye(t){return e=>Fs(e.$from,t)}function I(t,e,n){return t.config[e]===void 0&&t.parent?I(t.parent,e,n):typeof t.config[e]=="function"?t.config[e].bind({...n,parent:t.parent?I(t.parent,e,n):null}):t.config[e]}function $s(t){return t.map(e=>{let n={name:e.name,options:e.options,storage:e.storage},r=I(e,"addExtensions",n);return r?[e,...$s(r())]:e}).flat(10)}function Vs(t,e){let n=it.fromSchema(e).serializeFragment(t),o=document.implementation.createHTMLDocument().createElement("div");return o.appendChild(n),o.innerHTML}function Nu(t){return typeof t=="function"}function W(t,e=void 0,...n){return Nu(t)?e?t.bind(e)(...n):t(...n):t}function yg(t={}){return Object.keys(t).length===0&&t.constructor===Object}function Zn(t){let e=t.filter(o=>o.type==="extension"),n=t.filter(o=>o.type==="node"),r=t.filter(o=>o.type==="mark");return{baseExtensions:e,nodeExtensions:n,markExtensions:r}}function Ru(t){let e=[],{nodeExtensions:n,markExtensions:r}=Zn(t),o=[...n,...r],i={default:null,validate:void 0,rendered:!0,renderHTML:null,parseHTML:null,keepOnSplit:!0,isRequired:!1};return t.forEach(s=>{let l={name:s.name,options:s.options,storage:s.storage,extensions:o},a=I(s,"addGlobalAttributes",l);if(!a)return;a().forEach(u=>{u.types.forEach(d=>{Object.entries(u.attributes).forEach(([f,h])=>{e.push({type:d,name:f,attribute:{...i,...h}})})})})}),o.forEach(s=>{let l={name:s.name,options:s.options,storage:s.storage},a=I(s,"addAttributes",l);if(!a)return;let c=a();Object.entries(c).forEach(([u,d])=>{let f={...i,...d};typeof f?.default=="function"&&(f.default=f.default()),f?.isRequired&&f?.default===void 0&&delete f.default,e.push({type:s.name,name:u,attribute:f})})}),e}function T(...t){return t.filter(e=>!!e).reduce((e,n)=>{let r={...e};return Object.entries(n).forEach(([o,i])=>{if(!r[o]){r[o]=i;return}if(o==="class"){let l=i?String(i).split(" "):[],a=r[o]?r[o].split(" "):[],c=l.filter(u=>!a.includes(u));r[o]=[...a,...c].join(" ")}else if(o==="style"){let l=i?i.split(";").map(u=>u.trim()).filter(Boolean):[],a=r[o]?r[o].split(";").map(u=>u.trim()).filter(Boolean):[],c=new Map;a.forEach(u=>{let[d,f]=u.split(":").map(h=>h.trim());c.set(d,f)}),l.forEach(u=>{let[d,f]=u.split(":").map(h=>h.trim());c.set(d,f)}),r[o]=Array.from(c.entries()).map(([u,d])=>`${u}: ${d}`).join("; ")}else r[o]=i}),r},{})}function to(t,e){return e.filter(n=>n.type===t.type.name).filter(n=>n.attribute.rendered).map(n=>n.attribute.renderHTML?n.attribute.renderHTML(t.attrs)||{}:{[n.name]:t.attrs[n.name]}).reduce((n,r)=>T(n,r),{})}function bg(t){return typeof t!="string"?t:t.match(/^[+-]?(?:\d*\.)?\d+$/)?Number(t):t==="true"?!0:t==="false"?!1:t}function wu(t,e){return"style"in t?t:{...t,getAttrs:n=>{let r=t.getAttrs?t.getAttrs(n):t.attrs;if(r===!1)return!1;let o=e.reduce((i,s)=>{let l=s.attribute.parseHTML?s.attribute.parseHTML(n):bg(n.getAttribute(s.name));return l==null?i:{...i,[s.name]:l}},{});return{...r,...o}}}}function Su(t){return Object.fromEntries(Object.entries(t).filter(([e,n])=>e==="attrs"&&yg(n)?!1:n!=null))}function xg(t,e){var n;let r=Ru(t),{nodeExtensions:o,markExtensions:i}=Zn(t),s=(n=o.find(c=>I(c,"topNode")))==null?void 0:n.name,l=Object.fromEntries(o.map(c=>{let u=r.filter(y=>y.type===c.name),d={name:c.name,options:c.options,storage:c.storage,editor:e},f=t.reduce((y,b)=>{let x=I(b,"extendNodeSchema",d);return{...y,...x?x(c):{}}},{}),h=Su({...f,content:W(I(c,"content",d)),marks:W(I(c,"marks",d)),group:W(I(c,"group",d)),inline:W(I(c,"inline",d)),atom:W(I(c,"atom",d)),selectable:W(I(c,"selectable",d)),draggable:W(I(c,"draggable",d)),code:W(I(c,"code",d)),whitespace:W(I(c,"whitespace",d)),linebreakReplacement:W(I(c,"linebreakReplacement",d)),defining:W(I(c,"defining",d)),isolating:W(I(c,"isolating",d)),attrs:Object.fromEntries(u.map(y=>{var b,x;return[y.name,{default:(b=y?.attribute)==null?void 0:b.default,validate:(x=y?.attribute)==null?void 0:x.validate}]}))}),p=W(I(c,"parseHTML",d));p&&(h.parseDOM=p.map(y=>wu(y,u)));let m=I(c,"renderHTML",d);m&&(h.toDOM=y=>m({node:y,HTMLAttributes:to(y,u)}));let g=I(c,"renderText",d);return g&&(h.toText=g),[c.name,h]})),a=Object.fromEntries(i.map(c=>{let u=r.filter(g=>g.type===c.name),d={name:c.name,options:c.options,storage:c.storage,editor:e},f=t.reduce((g,y)=>{let b=I(y,"extendMarkSchema",d);return{...g,...b?b(c):{}}},{}),h=Su({...f,inclusive:W(I(c,"inclusive",d)),excludes:W(I(c,"excludes",d)),group:W(I(c,"group",d)),spanning:W(I(c,"spanning",d)),code:W(I(c,"code",d)),attrs:Object.fromEntries(u.map(g=>{var y,b;return[g.name,{default:(y=g?.attribute)==null?void 0:y.default,validate:(b=g?.attribute)==null?void 0:b.validate}]}))}),p=W(I(c,"parseHTML",d));p&&(h.parseDOM=p.map(g=>wu(g,u)));let m=I(c,"renderHTML",d);return m&&(h.toDOM=g=>m({mark:g,HTMLAttributes:to(g,u)})),[c.name,h]}));return new sn({topNode:s,nodes:l,marks:a})}function wg(t){let e=t.filter((n,r)=>t.indexOf(n)!==r);return Array.from(new Set(e))}function Ws(t){return t.sort((n,r)=>{let o=I(n,"priority")||100,i=I(r,"priority")||100;return o>i?-1:o<i?1:0})}function Du(t){let e=Ws($s(t)),n=wg(e.map(r=>r.name));return n.length&&console.warn(`[tiptap warn]: Duplicate extension names found: [${n.map(r=>`'${r}'`).join(", ")}]. This can lead to issues.`),e}function Pu(t,e,n){let{from:r,to:o}=e,{blockSeparator:i=`

`,textSerializers:s={}}=n||{},l="";return t.nodesBetween(r,o,(a,c,u,d)=>{var f;a.isBlock&&c>r&&(l+=i);let h=s?.[a.type.name];if(h)return u&&(l+=h({node:a,pos:c,parent:u,index:d,range:e})),!1;a.isText&&(l+=(f=a?.text)==null?void 0:f.slice(Math.max(r,c)-c,o-c))}),l}function Sg(t,e){let n={from:0,to:t.content.size};return Pu(t,n,e)}function Iu(t){return Object.fromEntries(Object.entries(t.nodes).filter(([,e])=>e.spec.toText).map(([e,n])=>[e,n.spec.toText]))}function mt(t,e){if(typeof t=="string"){if(!e.marks[t])throw Error(`There is no mark type named '${t}'. Maybe you forgot to add the extension?`);return e.marks[t]}return t}function Lu(t,e){let n=mt(e,t.schema),{from:r,to:o,empty:i}=t.selection,s=[];i?(t.storedMarks&&s.push(...t.storedMarks),s.push(...t.selection.$head.marks())):t.doc.nodesBetween(r,o,a=>{s.push(...a.marks)});let l=s.find(a=>a.type.name===n.name);return l?{...l.attrs}:{}}function Q(t,e){if(typeof t=="string"){if(!e.nodes[t])throw Error(`There is no node type named '${t}'. Maybe you forgot to add the extension?`);return e.nodes[t]}return t}function kg(t,e){let n=Q(e,t.schema),{from:r,to:o}=t.selection,i=[];t.doc.nodesBetween(r,o,l=>{i.push(l)});let s=i.reverse().find(l=>l.type.name===n.name);return s?{...s.attrs}:{}}function io(t,e){return e.nodes[t]?"node":e.marks[t]?"mark":null}function _s(t,e){let n=io(typeof e=="string"?e:e.name,t.schema);return n==="node"?kg(t,e):n==="mark"?Lu(t,e):{}}function Cg(t,e=JSON.stringify){let n={};return t.filter(r=>{let o=e(r);return Object.prototype.hasOwnProperty.call(n,o)?!1:n[o]=!0})}function Mg(t){let e=Cg(t);return e.length===1?e:e.filter((n,r)=>!e.filter((i,s)=>s!==r).some(i=>n.oldRange.from>=i.oldRange.from&&n.oldRange.to<=i.oldRange.to&&n.newRange.from>=i.newRange.from&&n.newRange.to<=i.newRange.to))}function js(t){let{mapping:e,steps:n}=t,r=[];return e.maps.forEach((o,i)=>{let s=[];if(o.ranges.length)o.forEach((l,a)=>{s.push({from:l,to:a})});else{let{from:l,to:a}=n[i];if(l===void 0||a===void 0)return;s.push({from:l,to:a})}s.forEach(({from:l,to:a})=>{let c=e.slice(i).map(l,-1),u=e.slice(i).map(a),d=e.invert().map(c,-1),f=e.invert().map(u);r.push({oldRange:{from:d,to:f},newRange:{from:c,to:u}})})}),Mg(r)}function Ks(t){return Object.prototype.toString.call(t)==="[object RegExp]"}function no(t,e,n={strict:!0}){let r=Object.keys(e);return r.length?r.every(o=>n.strict?e[o]===t[o]:Ks(e[o])?e[o].test(t[o]):e[o]===t[o]):!0}function Bu(t,e,n={}){return t.find(r=>r.type===e&&no(Object.fromEntries(Object.keys(n).map(o=>[o,r.attrs[o]])),n))}function ku(t,e,n={}){return!!Bu(t,e,n)}function Us(t,e,n){var r;if(!t||!e)return;let o=t.parent.childAfter(t.parentOffset);if((!o.node||!o.node.marks.some(u=>u.type===e))&&(o=t.parent.childBefore(t.parentOffset)),!o.node||!o.node.marks.some(u=>u.type===e)||(n=n||((r=o.node.marks[0])==null?void 0:r.attrs),!Bu([...o.node.marks],e,n)))return;let s=o.index,l=t.start()+o.offset,a=s+1,c=l+o.node.nodeSize;for(;s>0&&ku([...t.parent.child(s-1).marks],e,n);)s-=1,l-=t.parent.child(s).nodeSize;for(;a<t.parent.childCount&&ku([...t.parent.child(a).marks],e,n);)c+=t.parent.child(a).nodeSize,a+=1;return{from:l,to:c}}function so(t,e,n){let r=[];return t===e?n.resolve(t).marks().forEach(o=>{let i=n.resolve(t),s=Us(i,o.type);s&&r.push({mark:o,...s})}):n.nodesBetween(t,e,(o,i)=>{!o||o?.nodeSize===void 0||r.push(...o.marks.map(s=>({from:i,to:i+o.nodeSize,mark:s})))}),r}var zu=(t,e,n,r=20)=>{let o=t.doc.resolve(n),i=r,s=null;for(;i>0&&s===null;){let l=o.node(i);l?.type.name===e?s=l:i-=1}return[s,i]};function Ps(t,e){return e.nodes[t]||e.marks[t]||null}function eo(t,e,n){return Object.fromEntries(Object.entries(n).filter(([r])=>{let o=t.find(i=>i.type===e&&i.name===r);return o?o.attribute.keepOnSplit:!1}))}var vg=(t,e=500)=>{let n="",r=t.parentOffset;return t.parent.nodesBetween(Math.max(0,r-e),r,(o,i,s,l)=>{var a,c;let u=((c=(a=o.type.spec).toText)==null?void 0:c.call(a,{node:o,pos:i,parent:s,index:l}))||o.textContent||"%leaf%";n+=o.isAtom&&!o.isText?u:u.slice(0,Math.max(0,r-i))}),n};function zs(t,e,n={}){let{empty:r,ranges:o}=t.selection,i=e?mt(e,t.schema):null;if(r)return!!(t.storedMarks||t.selection.$from.marks()).filter(d=>i?i.name===d.type.name:!0).find(d=>no(d.attrs,n,{strict:!1}));let s=0,l=[];if(o.forEach(({$from:d,$to:f})=>{let h=d.pos,p=f.pos;t.doc.nodesBetween(h,p,(m,g)=>{if(!m.isText&&!m.marks.length)return;let y=Math.max(h,g),b=Math.min(p,g+m.nodeSize),x=b-y;s+=x,l.push(...m.marks.map(C=>({mark:C,from:y,to:b})))})}),s===0)return!1;let a=l.filter(d=>i?i.name===d.mark.type.name:!0).filter(d=>no(d.mark.attrs,n,{strict:!1})).reduce((d,f)=>d+f.to-f.from,0),c=l.filter(d=>i?d.mark.type!==i&&d.mark.type.excludes(i):!0).reduce((d,f)=>d+f.to-f.from,0);return(a>0?a+c:a)>=s}function Ge(t,e,n={}){let{from:r,to:o,empty:i}=t.selection,s=e?Q(e,t.schema):null,l=[];t.doc.nodesBetween(r,o,(d,f)=>{if(d.isText)return;let h=Math.max(r,f),p=Math.min(o,f+d.nodeSize);l.push({node:d,from:h,to:p})});let a=o-r,c=l.filter(d=>s?s.name===d.node.type.name:!0).filter(d=>no(d.node.attrs,n,{strict:!1}));return i?!!c.length:c.reduce((d,f)=>d+f.to-f.from,0)>=a}function Js(t,e,n={}){if(!e)return Ge(t,null,n)||zs(t,null,n);let r=io(e,t.schema);return r==="node"?Ge(t,e,n):r==="mark"?zs(t,e,n):!1}var Hu=(t,e)=>{let{$from:n,$to:r,$anchor:o}=t.selection;if(e){let i=Ye(l=>l.type.name===e)(t.selection);if(!i)return!1;let s=t.doc.resolve(i.pos+1);return o.pos+1===s.end()}return!(r.parentOffset<r.parent.nodeSize-2||n.pos!==r.pos)},Fu=t=>{let{$from:e,$to:n}=t.selection;return!(e.parentOffset>0||e.pos!==n.pos)};function Cu(t,e){return Array.isArray(e)?e.some(n=>(typeof n=="string"?n:n.name)===t.name):e}function Mu(t,e){let{nodeExtensions:n}=Zn(e),r=n.find(s=>s.name===t);if(!r)return!1;let o={name:r.name,options:r.options,storage:r.storage},i=W(I(r,"group",o));return typeof i!="string"?!1:i.split(" ").includes("list")}function tr(t,{checkChildren:e=!0,ignoreWhitespace:n=!1}={}){var r;if(n){if(t.type.name==="hardBreak")return!0;if(t.isText)return/^\s*$/m.test((r=t.text)!=null?r:"")}if(t.isText)return!t.text;if(t.isAtom||t.isLeaf)return!1;if(t.content.childCount===0)return!0;if(e){let o=!0;return t.content.forEach(i=>{o!==!1&&(tr(i,{ignoreWhitespace:n,checkChildren:e})||(o=!1))}),o}return!1}function lo(t){return t instanceof O}function ao(t){return t instanceof A}function pt(t=0,e=0,n=0){return Math.min(Math.max(t,e),n)}function $u(t,e,n){let o=t.state.doc.content.size,i=pt(e,0,o),s=pt(n,0,o),l=t.coordsAtPos(i),a=t.coordsAtPos(s,-1),c=Math.min(l.top,a.top),u=Math.max(l.bottom,a.bottom),d=Math.min(l.left,a.left),f=Math.max(l.right,a.right),h=f-d,p=u-c,y={top:c,bottom:u,left:d,right:f,width:h,height:p,x:d,y:c};return{...y,toJSON:()=>y}}function Vu(t,e=null){if(!e)return null;let n=E.atStart(t),r=E.atEnd(t);if(e==="start"||e===!0)return n;if(e==="end")return r;let o=n.from,i=r.to;return e==="all"?A.create(t,pt(0,o,i),pt(t.content.size,o,i)):A.create(t,pt(e,o,i),pt(e,o,i))}function Tg(t,e,n){let r=t.steps.length-1;if(r<e)return;let o=t.steps[r];if(!(o instanceof pe||o instanceof ne))return;let i=t.mapping.maps[r],s=0;i.forEach((l,a,c,u)=>{s===0&&(s=u)}),t.setSelection(E.near(t.doc.resolve(s),n))}var co=class{constructor(t){this.find=t.find,this.handler=t.handler}},Ag=(t,e)=>{if(Ks(e))return e.exec(t);let n=e(t);if(!n)return null;let r=[n.text];return r.index=n.index,r.input=t,r.data=n.data,n.replaceWith&&(n.text.includes(n.replaceWith)||console.warn('[tiptap warn]: "inputRuleMatch.replaceWith" must be part of "inputRuleMatch.text".'),r.push(n.replaceWith)),r};function Xr(t){var e;let{editor:n,from:r,to:o,text:i,rules:s,plugin:l}=t,{view:a}=n;if(a.composing)return!1;let c=a.state.doc.resolve(r);if(c.parent.type.spec.code||(e=c.nodeBefore||c.nodeAfter)!=null&&e.marks.find(f=>f.type.spec.code))return!1;let u=!1,d=vg(c)+i;return s.forEach(f=>{if(u)return;let h=Ag(d,f.find);if(!h)return;let p=a.state.tr,m=ro({state:a.state,transaction:p}),g={from:r-(h[0].length-i.length),to:o},{commands:y,chain:b,can:x}=new oo({editor:n,state:m});f.handler({state:m,range:g,match:h,commands:y,chain:b,can:x})===null||!p.steps.length||(p.setMeta(l,{transform:p,from:r,to:o,text:i}),a.dispatch(p),u=!0)}),u}function Eg(t){let{editor:e,rules:n}=t,r=new N({state:{init(){return null},apply(o,i,s){let l=o.getMeta(r);if(l)return l;let a=o.getMeta("applyInputRules");return!!a&&setTimeout(()=>{let{text:u}=a;typeof u=="string"?u=u:u=Vs(w.from(u),s.schema);let{from:d}=a,f=d+u.length;Xr({editor:e,from:d,to:f,text:u,rules:n,plugin:r})}),o.selectionSet||o.docChanged?null:i}},props:{handleTextInput(o,i,s,l){return Xr({editor:e,from:i,to:s,text:l,rules:n,plugin:r})},handleDOMEvents:{compositionend:o=>(setTimeout(()=>{let{$cursor:i}=o.state.selection;i&&Xr({editor:e,from:i.pos,to:i.pos,text:"",rules:n,plugin:r})}),!1)},handleKeyDown(o,i){if(i.key!=="Enter")return!1;let{$cursor:s}=o.state.selection;return s?Xr({editor:e,from:s.pos,to:s.pos,text:`
`,rules:n,plugin:r}):!1}},isInputRules:!0});return r}function Og(t){return Object.prototype.toString.call(t).slice(8,-1)}function Qr(t){return Og(t)!=="Object"?!1:t.constructor===Object&&Object.getPrototypeOf(t)===Object.prototype}function Wu(t,e){let n={...t};return Qr(t)&&Qr(e)&&Object.keys(e).forEach(r=>{Qr(e[r])&&Qr(t[r])?n[r]=Wu(t[r],e[r]):n[r]=e[r]}),n}var qs=class{constructor(t={}){this.type="extendable",this.parent=null,this.child=null,this.name="",this.config={name:this.name},this.config={...this.config,...t},this.name=this.config.name}get options(){return{...W(I(this,"addOptions",{name:this.name}))||{}}}get storage(){return{...W(I(this,"addStorage",{name:this.name,options:this.options}))||{}}}configure(t={}){let e=this.extend({...this.config,addOptions:()=>Wu(this.options,t)});return e.name=this.name,e.parent=this.parent,e}extend(t={}){let e=new this.constructor({...this.config,...t});return e.parent=this,this.child=e,e.name="name"in t?t.name:e.parent.name,e}},te=class _u extends qs{constructor(){super(...arguments),this.type="mark"}static create(e={}){let n=typeof e=="function"?e():e;return new _u(n)}static handleExit({editor:e,mark:n}){let{tr:r}=e.state,o=e.state.selection.$from;if(o.pos===o.end()){let s=o.marks();if(!!!s.find(c=>c?.type.name===n.name))return!1;let a=s.find(c=>c?.type.name===n.name);return a&&r.removeStoredMark(a),r.insertText(" ",o.pos),e.view.dispatch(r),!0}return!1}configure(e){return super.configure(e)}extend(e){let n=typeof e=="function"?e():e;return super.extend(n)}};function Ng(t){return typeof t=="number"}var Rg=class{constructor(t){this.find=t.find,this.handler=t.handler}},Dg=(t,e,n)=>{if(Ks(e))return[...t.matchAll(e)];let r=e(t,n);return r?r.map(o=>{let i=[o.text];return i.index=o.index,i.input=t,i.data=o.data,o.replaceWith&&(o.text.includes(o.replaceWith)||console.warn('[tiptap warn]: "pasteRuleMatch.replaceWith" must be part of "pasteRuleMatch.text".'),i.push(o.replaceWith)),i}):[]};function Pg(t){let{editor:e,state:n,from:r,to:o,rule:i,pasteEvent:s,dropEvent:l}=t,{commands:a,chain:c,can:u}=new oo({editor:e,state:n}),d=[];return n.doc.nodesBetween(r,o,(h,p)=>{if(!h.isTextblock||h.type.spec.code)return;let m=Math.max(r,p),g=Math.min(o,p+h.content.size),y=h.textBetween(m-p,g-p,void 0,"\uFFFC");Dg(y,i.find,s).forEach(x=>{if(x.index===void 0)return;let C=m+x.index+1,S=C+x[0].length,R={from:n.tr.mapping.map(C),to:n.tr.mapping.map(S)},P=i.handler({state:n,range:R,match:x,commands:a,chain:c,can:u,pasteEvent:s,dropEvent:l});d.push(P)})}),d.every(h=>h!==null)}var Zr=null,Ig=t=>{var e;let n=new ClipboardEvent("paste",{clipboardData:new DataTransfer});return(e=n.clipboardData)==null||e.setData("text/html",t),n};function Lg(t){let{editor:e,rules:n}=t,r=null,o=!1,i=!1,s=typeof ClipboardEvent<"u"?new ClipboardEvent("paste"):null,l;try{l=typeof DragEvent<"u"?new DragEvent("drop"):null}catch{l=null}let a=({state:u,from:d,to:f,rule:h,pasteEvt:p})=>{let m=u.tr,g=ro({state:u,transaction:m});if(!(!Pg({editor:e,state:g,from:Math.max(d-1,0),to:f.b-1,rule:h,pasteEvent:p,dropEvent:l})||!m.steps.length)){try{l=typeof DragEvent<"u"?new DragEvent("drop"):null}catch{l=null}return s=typeof ClipboardEvent<"u"?new ClipboardEvent("paste"):null,m}};return n.map(u=>new N({view(d){let f=p=>{var m;r=(m=d.dom.parentElement)!=null&&m.contains(p.target)?d.dom.parentElement:null,r&&(Zr=e)},h=()=>{Zr&&(Zr=null)};return window.addEventListener("dragstart",f),window.addEventListener("dragend",h),{destroy(){window.removeEventListener("dragstart",f),window.removeEventListener("dragend",h)}}},props:{handleDOMEvents:{drop:(d,f)=>{if(i=r===d.dom.parentElement,l=f,!i){let h=Zr;h?.isEditable&&setTimeout(()=>{let p=h.state.selection;p&&h.commands.deleteRange({from:p.from,to:p.to})},10)}return!1},paste:(d,f)=>{var h;let p=(h=f.clipboardData)==null?void 0:h.getData("text/html");return s=f,o=!!p?.includes("data-pm-slice"),!1}}},appendTransaction:(d,f,h)=>{let p=d[0],m=p.getMeta("uiEvent")==="paste"&&!o,g=p.getMeta("uiEvent")==="drop"&&!i,y=p.getMeta("applyPasteRules"),b=!!y;if(!m&&!g&&!b)return;if(b){let{text:S}=y;typeof S=="string"?S=S:S=Vs(w.from(S),h.schema);let{from:R}=y,P=R+S.length,v=Ig(S);return a({rule:u,state:h,from:R,to:{b:P},pasteEvt:v})}let x=f.doc.content.findDiffStart(h.doc.content),C=f.doc.content.findDiffEnd(h.doc.content);if(!(!Ng(x)||!C||x===C.b))return a({rule:u,state:h,from:x,to:C,pasteEvt:s})}}))}var uo=class{constructor(t,e){this.splittableMarks=[],this.editor=e,this.extensions=Du(t),this.schema=xg(this.extensions,e),this.setupExtensions()}get commands(){return this.extensions.reduce((t,e)=>{let n={name:e.name,options:e.options,storage:this.editor.extensionStorage[e.name],editor:this.editor,type:Ps(e.name,this.schema)},r=I(e,"addCommands",n);return r?{...t,...r()}:t},{})}get plugins(){let{editor:t}=this,e=Ws([...this.extensions].reverse()),n=[],r=[],o=e.map(i=>{let s={name:i.name,options:i.options,storage:this.editor.extensionStorage[i.name],editor:t,type:Ps(i.name,this.schema)},l=[],a=I(i,"addKeyboardShortcuts",s),c={};if(i.type==="mark"&&I(i,"exitable",s)&&(c.ArrowRight=()=>te.handleExit({editor:t,mark:i})),a){let p=Object.fromEntries(Object.entries(a()).map(([m,g])=>[m,()=>g({editor:t})]));c={...c,...p}}let u=ru(c);l.push(u);let d=I(i,"addInputRules",s);Cu(i,t.options.enableInputRules)&&d&&n.push(...d());let f=I(i,"addPasteRules",s);Cu(i,t.options.enablePasteRules)&&f&&r.push(...f());let h=I(i,"addProseMirrorPlugins",s);if(h){let p=h();l.push(...p)}return l}).flat();return[Eg({editor:t,rules:n}),...Lg({editor:t,rules:r}),...o]}get attributes(){return Ru(this.extensions)}get nodeViews(){let{editor:t}=this,{nodeExtensions:e}=Zn(this.extensions);return Object.fromEntries(e.filter(n=>!!I(n,"addNodeView")).map(n=>{let r=this.attributes.filter(l=>l.type===n.name),o={name:n.name,options:n.options,storage:this.editor.extensionStorage[n.name],editor:t,type:Q(n.name,this.schema)},i=I(n,"addNodeView",o);if(!i)return[];let s=(l,a,c,u,d)=>{let f=to(l,r);return i()({node:l,view:a,getPos:c,decorations:u,innerDecorations:d,editor:t,extension:n,HTMLAttributes:f})};return[n.name,s]}))}get markViews(){let{editor:t}=this,{markExtensions:e}=Zn(this.extensions);return Object.fromEntries(e.filter(n=>!!I(n,"addMarkView")).map(n=>{let r=this.attributes.filter(l=>l.type===n.name),o={name:n.name,options:n.options,storage:this.editor.extensionStorage[n.name],editor:t,type:mt(n.name,this.schema)},i=I(n,"addMarkView",o);if(!i)return[];let s=(l,a,c)=>{let u=to(l,r);return i()({mark:l,view:a,inline:c,editor:t,extension:n,HTMLAttributes:u,updateAttributes:d=>{qy(l,t,d)}})};return[n.name,s]}))}setupExtensions(){let t=this.extensions;this.editor.extensionStorage=Object.fromEntries(t.map(e=>[e.name,e.storage])),t.forEach(e=>{var n;let r={name:e.name,options:e.options,storage:this.editor.extensionStorage[e.name],editor:this.editor,type:Ps(e.name,this.schema)};e.type==="mark"&&((n=W(I(e,"keepOnSplit",r)))==null||n)&&this.splittableMarks.push(e.name);let o=I(e,"onBeforeCreate",r),i=I(e,"onCreate",r),s=I(e,"onUpdate",r),l=I(e,"onSelectionUpdate",r),a=I(e,"onTransaction",r),c=I(e,"onFocus",r),u=I(e,"onBlur",r),d=I(e,"onDestroy",r);o&&this.editor.on("beforeCreate",o),i&&this.editor.on("create",i),s&&this.editor.on("update",s),l&&this.editor.on("selectionUpdate",l),a&&this.editor.on("transaction",a),c&&this.editor.on("focus",c),u&&this.editor.on("blur",u),d&&this.editor.on("destroy",d)})}};uo.resolve=Du;uo.sort=Ws;uo.flatten=$s;var Bg={};Au(Bg,{ClipboardTextSerializer:()=>Ku,Commands:()=>qu,Delete:()=>Gu,Drop:()=>Yu,Editable:()=>Xu,FocusEvents:()=>Zu,Keymap:()=>ed,Paste:()=>td,Tabindex:()=>nd,focusEventsPluginKey:()=>Qu});var $=class ju extends qs{constructor(){super(...arguments),this.type="extension"}static create(e={}){let n=typeof e=="function"?e():e;return new ju(n)}configure(e){return super.configure(e)}extend(e){let n=typeof e=="function"?e():e;return super.extend(n)}},Ku=$.create({name:"clipboardTextSerializer",addOptions(){return{blockSeparator:void 0}},addProseMirrorPlugins(){return[new N({key:new L("clipboardTextSerializer"),props:{clipboardTextSerializer:()=>{let{editor:t}=this,{state:e,schema:n}=t,{doc:r,selection:o}=e,{ranges:i}=o,s=Math.min(...i.map(u=>u.$from.pos)),l=Math.max(...i.map(u=>u.$to.pos)),a=Iu(n);return Pu(r,{from:s,to:l},{...this.options.blockSeparator!==void 0?{blockSeparator:this.options.blockSeparator}:{},textSerializers:a})}}})]}}),Uu={};Au(Uu,{blur:()=>zg,clearContent:()=>Hg,clearNodes:()=>Fg,command:()=>$g,createParagraphNear:()=>Vg,cut:()=>Wg,deleteCurrentNode:()=>_g,deleteNode:()=>jg,deleteRange:()=>Kg,deleteSelection:()=>Ug,enter:()=>Jg,exitCode:()=>qg,extendMarkRange:()=>Gg,first:()=>Yg,focus:()=>Qg,forEach:()=>Zg,insertContent:()=>ey,insertContentAt:()=>ny,joinBackward:()=>iy,joinDown:()=>oy,joinForward:()=>sy,joinItemBackward:()=>ly,joinItemForward:()=>ay,joinTextblockBackward:()=>cy,joinTextblockForward:()=>uy,joinUp:()=>ry,keyboardShortcut:()=>fy,lift:()=>hy,liftEmptyBlock:()=>py,liftListItem:()=>my,newlineInCode:()=>gy,resetAttributes:()=>yy,scrollIntoView:()=>by,selectAll:()=>xy,selectNodeBackward:()=>wy,selectNodeForward:()=>Sy,selectParentNode:()=>ky,selectTextblockEnd:()=>Cy,selectTextblockStart:()=>My,setContent:()=>vy,setMark:()=>Ay,setMeta:()=>Ey,setNode:()=>Oy,setNodeSelection:()=>Ny,setTextSelection:()=>Ry,sinkListItem:()=>Dy,splitBlock:()=>Py,splitListItem:()=>Iy,toggleList:()=>Ly,toggleMark:()=>By,toggleNode:()=>zy,toggleWrap:()=>Hy,undoInputRule:()=>Fy,unsetAllMarks:()=>$y,unsetMark:()=>Vy,updateAttributes:()=>Wy,wrapIn:()=>_y,wrapInList:()=>jy});var zg=()=>({editor:t,view:e})=>(requestAnimationFrame(()=>{var n;t.isDestroyed||(e.dom.blur(),(n=window?.getSelection())==null||n.removeAllRanges())}),!0),Hg=(t=!0)=>({commands:e})=>e.setContent("",{emitUpdate:t}),Fg=()=>({state:t,tr:e,dispatch:n})=>{let{selection:r}=e,{ranges:o}=r;return n&&o.forEach(({$from:i,$to:s})=>{t.doc.nodesBetween(i.pos,s.pos,(l,a)=>{if(l.type.isText)return;let{doc:c,mapping:u}=e,d=c.resolve(u.map(a)),f=c.resolve(u.map(a+l.nodeSize)),h=d.blockRange(f);if(!h)return;let p=at(h);if(l.type.isTextblock){let{defaultType:m}=d.parent.contentMatchAt(d.index());e.setNodeMarkup(h.start,m)}(p||p===0)&&e.lift(h,p)})}),!0},$g=t=>e=>t(e),Vg=()=>({state:t,dispatch:e})=>As(t,e),Wg=(t,e)=>({editor:n,tr:r})=>{let{state:o}=n,i=o.doc.slice(t.from,t.to);r.deleteRange(t.from,t.to);let s=r.mapping.map(e);return r.insert(s,i.content),r.setSelection(new A(r.doc.resolve(Math.max(s-1,0)))),!0},_g=()=>({tr:t,dispatch:e})=>{let{selection:n}=t,r=n.$anchor.node();if(r.content.size>0)return!1;let o=t.selection.$anchor;for(let i=o.depth;i>0;i-=1)if(o.node(i).type===r.type){if(e){let l=o.before(i),a=o.after(i);t.delete(l,a).scrollIntoView()}return!0}return!1},jg=t=>({tr:e,state:n,dispatch:r})=>{let o=Q(t,n.schema),i=e.selection.$anchor;for(let s=i.depth;s>0;s-=1)if(i.node(s).type===o){if(r){let a=i.before(s),c=i.after(s);e.delete(a,c).scrollIntoView()}return!0}return!1},Kg=t=>({tr:e,dispatch:n})=>{let{from:r,to:o}=t;return n&&e.delete(r,o),!0},Ug=()=>({state:t,dispatch:e})=>Gr(t,e),Jg=()=>({commands:t})=>t.keyboardShortcut("Enter"),qg=()=>({state:t,dispatch:e})=>Ts(t,e),Gg=(t,e={})=>({tr:n,state:r,dispatch:o})=>{let i=mt(t,r.schema),{doc:s,selection:l}=n,{$from:a,from:c,to:u}=l;if(o){let d=Us(a,i,e);if(d&&d.from<=c&&d.to>=u){let f=A.create(s,d.from,d.to);n.setSelection(f)}}return!0},Yg=t=>e=>{let n=typeof t=="function"?t(e):t;for(let r=0;r<n.length;r+=1)if(n[r](e))return!0;return!1};function Xg(){return navigator.platform==="Android"||/android/i.test(navigator.userAgent)}function Gs(){return["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document}var Qg=(t=null,e={})=>({editor:n,view:r,tr:o,dispatch:i})=>{e={scrollIntoView:!0,...e};let s=()=>{(Gs()||Xg())&&r.dom.focus(),requestAnimationFrame(()=>{n.isDestroyed||(r.focus(),e?.scrollIntoView&&n.commands.scrollIntoView())})};if(r.hasFocus()&&t===null||t===!1)return!0;if(i&&t===null&&!ao(n.state.selection))return s(),!0;let l=Vu(o.doc,t)||n.state.selection,a=n.state.selection.eq(l);return i&&(a||o.setSelection(l),a&&o.storedMarks&&o.setStoredMarks(o.storedMarks),s()),!0},Zg=(t,e)=>n=>t.every((r,o)=>e(r,{...n,index:o})),ey=(t,e)=>({tr:n,commands:r})=>r.insertContentAt({from:n.selection.from,to:n.selection.to},t,e),ty=t=>!("type"in t),ny=(t,e,n)=>({tr:r,dispatch:o,editor:i})=>{var s;if(o){n={parseOptions:i.options.parseOptions,updateSelection:!0,applyInputRules:!1,applyPasteRules:!1,...n};let l,{selection:a}=i.state,c=y=>{i.emit("contentError",{editor:i,error:y,disableCollaboration:()=>{"collaboration"in i.storage&&typeof i.storage.collaboration=="object"&&i.storage.collaboration&&(i.storage.collaboration.isDisabled=!0)}})},u={preserveWhitespace:"full",...n.parseOptions};if(!n.errorOnInvalidContent&&!i.options.enableContentCheck&&i.options.emitContentError)try{Qn(e,i.schema,{parseOptions:u,errorOnInvalidContent:!0})}catch(y){c(y)}try{l=Qn(e,i.schema,{parseOptions:u,errorOnInvalidContent:(s=n.errorOnInvalidContent)!=null?s:i.options.enableContentCheck})}catch(y){return c(y),!1}let{from:d,to:f}=typeof t=="number"?{from:t,to:t}:{from:t.from,to:t.to},h=!0,p=!0;if((ty(l)?l:[l]).forEach(y=>{y.check(),h=h?y.isText&&y.marks.length===0:!1,p=p?y.isBlock:!1}),d===f&&p){let{parent:y}=r.doc.resolve(d);y.isTextblock&&!y.type.spec.code&&!y.childCount&&(d-=1,f+=1)}let g;if(h){if(Array.isArray(e))g=e.map(y=>y.text||"").join("");else if(e instanceof w){let y="";e.forEach(b=>{b.text&&(y+=b.text)}),g=y}else typeof e=="object"&&e&&e.text?g=e.text:g=e;r.insertText(g,d,f)}else{g=l;let y=a.$from.parentOffset===0,b=a.$from.node().isText||a.$from.node().isTextblock,x=a.$from.node().content.size>0;y&&b&&x&&(d=Math.max(0,d-1)),r.replaceWith(d,f,g)}n.updateSelection&&Tg(r,r.steps.length-1,-1),n.applyInputRules&&r.setMeta("applyInputRules",{from:d,text:g}),n.applyPasteRules&&r.setMeta("applyPasteRules",{from:d,text:g})}return!0},ry=()=>({state:t,dispatch:e})=>uu(t,e),oy=()=>({state:t,dispatch:e})=>du(t,e),iy=()=>({state:t,dispatch:e})=>bs(t,e),sy=()=>({state:t,dispatch:e})=>Ss(t,e),ly=()=>({state:t,dispatch:e,tr:n})=>{try{let r=Ft(t.doc,t.selection.$from.pos,-1);return r==null?!1:(n.join(r,2),e&&e(n),!0)}catch{return!1}},ay=()=>({state:t,dispatch:e,tr:n})=>{try{let r=Ft(t.doc,t.selection.$from.pos,1);return r==null?!1:(n.join(r,2),e&&e(n),!0)}catch{return!1}},cy=()=>({state:t,dispatch:e})=>su(t,e),uy=()=>({state:t,dispatch:e})=>lu(t,e);function Ju(){return typeof navigator<"u"?/Mac/.test(navigator.platform):!1}function dy(t){let e=t.split(/-(?!$)/),n=e[e.length-1];n==="Space"&&(n=" ");let r,o,i,s;for(let l=0;l<e.length-1;l+=1){let a=e[l];if(/^(cmd|meta|m)$/i.test(a))s=!0;else if(/^a(lt)?$/i.test(a))r=!0;else if(/^(c|ctrl|control)$/i.test(a))o=!0;else if(/^s(hift)?$/i.test(a))i=!0;else if(/^mod$/i.test(a))Gs()||Ju()?s=!0:o=!0;else throw new Error(`Unrecognized modifier name: ${a}`)}return r&&(n=`Alt-${n}`),o&&(n=`Ctrl-${n}`),s&&(n=`Meta-${n}`),i&&(n=`Shift-${n}`),n}var fy=t=>({editor:e,view:n,tr:r,dispatch:o})=>{let i=dy(t).split(/-(?!$)/),s=i.find(c=>!["Alt","Ctrl","Meta","Shift"].includes(c)),l=new KeyboardEvent("keydown",{key:s==="Space"?" ":s,altKey:i.includes("Alt"),ctrlKey:i.includes("Ctrl"),metaKey:i.includes("Meta"),shiftKey:i.includes("Shift"),bubbles:!0,cancelable:!0}),a=e.captureTransaction(()=>{n.someProp("handleKeyDown",c=>c(n,l))});return a?.steps.forEach(c=>{let u=c.map(r.mapping);u&&o&&r.maybeStep(u)}),!0},hy=(t,e={})=>({state:n,dispatch:r})=>{let o=Q(t,n.schema);return Ge(n,o,e)?fu(n,r):!1},py=()=>({state:t,dispatch:e})=>Es(t,e),my=t=>({state:e,dispatch:n})=>{let r=Q(t,e.schema);return bu(r)(e,n)},gy=()=>({state:t,dispatch:e})=>Ms(t,e);function vu(t,e){let n=typeof e=="string"?[e]:e;return Object.keys(t).reduce((r,o)=>(n.includes(o)||(r[o]=t[o]),r),{})}var yy=(t,e)=>({tr:n,state:r,dispatch:o})=>{let i=null,s=null,l=io(typeof t=="string"?t:t.name,r.schema);return l?(l==="node"&&(i=Q(t,r.schema)),l==="mark"&&(s=mt(t,r.schema)),o&&n.selection.ranges.forEach(a=>{r.doc.nodesBetween(a.$from.pos,a.$to.pos,(c,u)=>{i&&i===c.type&&n.setNodeMarkup(u,void 0,vu(c.attrs,e)),s&&c.marks.length&&c.marks.forEach(d=>{s===d.type&&n.addMark(u,u+c.nodeSize,s.create(vu(d.attrs,e)))})})}),!0):!1},by=()=>({tr:t,dispatch:e})=>(e&&t.scrollIntoView(),!0),xy=()=>({tr:t,dispatch:e})=>{if(e){let n=new be(t.doc);t.setSelection(n)}return!0},wy=()=>({state:t,dispatch:e})=>xs(t,e),Sy=()=>({state:t,dispatch:e})=>ks(t,e),ky=()=>({state:t,dispatch:e})=>hu(t,e),Cy=()=>({state:t,dispatch:e})=>Ns(t,e),My=()=>({state:t,dispatch:e})=>Os(t,e),vy=(t,{errorOnInvalidContent:e,emitUpdate:n=!0,parseOptions:r={}}={})=>({editor:o,tr:i,dispatch:s,commands:l})=>{let{doc:a}=i;if(r.preserveWhitespace!=="full"){let c=Bs(t,o.schema,r,{errorOnInvalidContent:e??o.options.enableContentCheck});return s&&i.replaceWith(0,a.content.size,c).setMeta("preventUpdate",!n),!0}return s&&i.setMeta("preventUpdate",!n),l.insertContentAt({from:0,to:a.content.size},t,{parseOptions:r,errorOnInvalidContent:e??o.options.enableContentCheck})};function Ty(t,e,n){var r;let{selection:o}=e,i=null;if(ao(o)&&(i=o.$cursor),i){let l=(r=t.storedMarks)!=null?r:i.marks();return!!n.isInSet(l)||!l.some(a=>a.type.excludes(n))}let{ranges:s}=o;return s.some(({$from:l,$to:a})=>{let c=l.depth===0?t.doc.inlineContent&&t.doc.type.allowsMarkType(n):!1;return t.doc.nodesBetween(l.pos,a.pos,(u,d,f)=>{if(c)return!1;if(u.isInline){let h=!f||f.type.allowsMarkType(n),p=!!n.isInSet(u.marks)||!u.marks.some(m=>m.type.excludes(n));c=h&&p}return!c}),c})}var Ay=(t,e={})=>({tr:n,state:r,dispatch:o})=>{let{selection:i}=n,{empty:s,ranges:l}=i,a=mt(t,r.schema);if(o)if(s){let c=Lu(r,a);n.addStoredMark(a.create({...c,...e}))}else l.forEach(c=>{let u=c.$from.pos,d=c.$to.pos;r.doc.nodesBetween(u,d,(f,h)=>{let p=Math.max(h,u),m=Math.min(h+f.nodeSize,d);f.marks.find(y=>y.type===a)?f.marks.forEach(y=>{a===y.type&&n.addMark(p,m,a.create({...y.attrs,...e}))}):n.addMark(p,m,a.create(e))})});return Ty(r,n,a)},Ey=(t,e)=>({tr:n})=>(n.setMeta(t,e),!0),Oy=(t,e={})=>({state:n,dispatch:r,chain:o})=>{let i=Q(t,n.schema),s;return n.selection.$anchor.sameParent(n.selection.$head)&&(s=n.selection.$anchor.parent.attrs),i.isTextblock?o().command(({commands:l})=>Rs(i,{...s,...e})(n)?!0:l.clearNodes()).command(({state:l})=>Rs(i,{...s,...e})(l,r)).run():(console.warn('[tiptap warn]: Currently "setNode()" only supports text block nodes.'),!1)},Ny=t=>({tr:e,dispatch:n})=>{if(n){let{doc:r}=e,o=pt(t,0,r.content.size),i=O.create(r,o);e.setSelection(i)}return!0},Ry=t=>({tr:e,dispatch:n})=>{if(n){let{doc:r}=e,{from:o,to:i}=typeof t=="number"?{from:t,to:t}:t,s=A.atStart(r).from,l=A.atEnd(r).to,a=pt(o,s,l),c=pt(i,s,l),u=A.create(r,a,c);e.setSelection(u)}return!0},Dy=t=>({state:e,dispatch:n})=>{let r=Q(t,e.schema);return xu(r)(e,n)};function Tu(t,e){let n=t.storedMarks||t.selection.$to.parentOffset&&t.selection.$from.marks();if(n){let r=n.filter(o=>e?.includes(o.type.name));t.tr.ensureMarks(r)}}var Py=({keepMarks:t=!0}={})=>({tr:e,state:n,dispatch:r,editor:o})=>{let{selection:i,doc:s}=e,{$from:l,$to:a}=i,c=o.extensionManager.attributes,u=eo(c,l.node().type.name,l.node().attrs);if(i instanceof O&&i.node.isBlock)return!l.parentOffset||!Ae(s,l.pos)?!1:(r&&(t&&Tu(n,o.extensionManager.splittableMarks),e.split(l.pos).scrollIntoView()),!0);if(!l.parent.isBlock)return!1;let d=a.parentOffset===a.parent.content.size,f=l.depth===0?void 0:er(l.node(-1).contentMatchAt(l.indexAfter(-1))),h=d&&f?[{type:f,attrs:u}]:void 0,p=Ae(e.doc,e.mapping.map(l.pos),1,h);if(!h&&!p&&Ae(e.doc,e.mapping.map(l.pos),1,f?[{type:f}]:void 0)&&(p=!0,h=f?[{type:f,attrs:u}]:void 0),r){if(p&&(i instanceof A&&e.deleteSelection(),e.split(e.mapping.map(l.pos),1,h),f&&!d&&!l.parentOffset&&l.parent.type!==f)){let m=e.mapping.map(l.before()),g=e.doc.resolve(m);l.node(-1).canReplaceWith(g.index(),g.index()+1,f)&&e.setNodeMarkup(e.mapping.map(l.before()),f)}t&&Tu(n,o.extensionManager.splittableMarks),e.scrollIntoView()}return p},Iy=(t,e={})=>({tr:n,state:r,dispatch:o,editor:i})=>{var s;let l=Q(t,r.schema),{$from:a,$to:c}=r.selection,u=r.selection.node;if(u&&u.isBlock||a.depth<2||!a.sameParent(c))return!1;let d=a.node(-1);if(d.type!==l)return!1;let f=i.extensionManager.attributes;if(a.parent.content.size===0&&a.node(-1).childCount===a.indexAfter(-1)){if(a.depth===2||a.node(-3).type!==l||a.index(-2)!==a.node(-2).childCount-1)return!1;if(o){let y=w.empty,b=a.index(-1)?1:a.index(-2)?2:3;for(let v=a.depth-b;v>=a.depth-3;v-=1)y=w.from(a.node(v).copy(y));let x=a.indexAfter(-1)<a.node(-2).childCount?1:a.indexAfter(-2)<a.node(-3).childCount?2:3,C={...eo(f,a.node().type.name,a.node().attrs),...e},S=((s=l.contentMatch.defaultType)==null?void 0:s.createAndFill(C))||void 0;y=y.append(w.from(l.createAndFill(null,S)||void 0));let R=a.before(a.depth-(b-1));n.replace(R,a.after(-x),new M(y,4-b,0));let P=-1;n.doc.nodesBetween(R,n.doc.content.size,(v,B)=>{if(P>-1)return!1;v.isTextblock&&v.content.size===0&&(P=B+1)}),P>-1&&n.setSelection(A.near(n.doc.resolve(P))),n.scrollIntoView()}return!0}let h=c.pos===a.end()?d.contentMatchAt(0).defaultType:null,p={...eo(f,d.type.name,d.attrs),...e},m={...eo(f,a.node().type.name,a.node().attrs),...e};n.delete(a.pos,c.pos);let g=h?[{type:l,attrs:p},{type:h,attrs:m}]:[{type:l,attrs:p}];if(!Ae(n.doc,a.pos,2))return!1;if(o){let{selection:y,storedMarks:b}=r,{splittableMarks:x}=i.extensionManager,C=b||y.$to.parentOffset&&y.$from.marks();if(n.split(a.pos,2,g).scrollIntoView(),!C||!o)return!0;let S=C.filter(R=>x.includes(R.type.name));n.ensureMarks(S)}return!0},Is=(t,e)=>{let n=Ye(s=>s.type===e)(t.selection);if(!n)return!0;let r=t.doc.resolve(Math.max(0,n.pos-1)).before(n.depth);if(r===void 0)return!0;let o=t.doc.nodeAt(r);return n.node.type===o?.type&&Ne(t.doc,n.pos)&&t.join(n.pos),!0},Ls=(t,e)=>{let n=Ye(s=>s.type===e)(t.selection);if(!n)return!0;let r=t.doc.resolve(n.start).after(n.depth);if(r===void 0)return!0;let o=t.doc.nodeAt(r);return n.node.type===o?.type&&Ne(t.doc,r)&&t.join(r),!0},Ly=(t,e,n,r={})=>({editor:o,tr:i,state:s,dispatch:l,chain:a,commands:c,can:u})=>{let{extensions:d,splittableMarks:f}=o.extensionManager,h=Q(t,s.schema),p=Q(e,s.schema),{selection:m,storedMarks:g}=s,{$from:y,$to:b}=m,x=y.blockRange(b),C=g||m.$to.parentOffset&&m.$from.marks();if(!x)return!1;let S=Ye(R=>Mu(R.type.name,d))(m);if(x.depth>=1&&S&&x.depth-S.depth<=1){if(S.node.type===h)return c.liftListItem(p);if(Mu(S.node.type.name,d)&&h.validContent(S.node.content)&&l)return a().command(()=>(i.setNodeMarkup(S.pos,h),!0)).command(()=>Is(i,h)).command(()=>Ls(i,h)).run()}return!n||!C||!l?a().command(()=>u().wrapInList(h,r)?!0:c.clearNodes()).wrapInList(h,r).command(()=>Is(i,h)).command(()=>Ls(i,h)).run():a().command(()=>{let R=u().wrapInList(h,r),P=C.filter(v=>f.includes(v.type.name));return i.ensureMarks(P),R?!0:c.clearNodes()}).wrapInList(h,r).command(()=>Is(i,h)).command(()=>Ls(i,h)).run()},By=(t,e={},n={})=>({state:r,commands:o})=>{let{extendEmptyMarkRange:i=!1}=n,s=mt(t,r.schema);return zs(r,s,e)?o.unsetMark(s,{extendEmptyMarkRange:i}):o.setMark(s,e)},zy=(t,e,n={})=>({state:r,commands:o})=>{let i=Q(t,r.schema),s=Q(e,r.schema),l=Ge(r,i,n),a;return r.selection.$anchor.sameParent(r.selection.$head)&&(a=r.selection.$anchor.parent.attrs),l?o.setNode(s,a):o.setNode(i,{...a,...n})},Hy=(t,e={})=>({state:n,commands:r})=>{let o=Q(t,n.schema);return Ge(n,o,e)?r.lift(o):r.wrapIn(o,e)},Fy=()=>({state:t,dispatch:e})=>{let n=t.plugins;for(let r=0;r<n.length;r+=1){let o=n[r],i;if(o.spec.isInputRules&&(i=o.getState(t))){if(e){let s=t.tr,l=i.transform;for(let a=l.steps.length-1;a>=0;a-=1)s.step(l.steps[a].invert(l.docs[a]));if(i.text){let a=s.doc.resolve(i.from).marks();s.replaceWith(i.from,i.to,t.schema.text(i.text,a))}else s.delete(i.from,i.to)}return!0}}return!1},$y=()=>({tr:t,dispatch:e})=>{let{selection:n}=t,{empty:r,ranges:o}=n;return r||e&&o.forEach(i=>{t.removeMark(i.$from.pos,i.$to.pos)}),!0},Vy=(t,e={})=>({tr:n,state:r,dispatch:o})=>{var i;let{extendEmptyMarkRange:s=!1}=e,{selection:l}=n,a=mt(t,r.schema),{$from:c,empty:u,ranges:d}=l;if(!o)return!0;if(u&&s){let{from:f,to:h}=l,p=(i=c.marks().find(g=>g.type===a))==null?void 0:i.attrs,m=Us(c,a,p);m&&(f=m.from,h=m.to),n.removeMark(f,h,a)}else d.forEach(f=>{n.removeMark(f.$from.pos,f.$to.pos,a)});return n.removeStoredMark(a),!0},Wy=(t,e={})=>({tr:n,state:r,dispatch:o})=>{let i=null,s=null,l=io(typeof t=="string"?t:t.name,r.schema);return l?(l==="node"&&(i=Q(t,r.schema)),l==="mark"&&(s=mt(t,r.schema)),o&&n.selection.ranges.forEach(a=>{let c=a.$from.pos,u=a.$to.pos,d,f,h,p;n.selection.empty?r.doc.nodesBetween(c,u,(m,g)=>{i&&i===m.type&&(h=Math.max(g,c),p=Math.min(g+m.nodeSize,u),d=g,f=m)}):r.doc.nodesBetween(c,u,(m,g)=>{g<c&&i&&i===m.type&&(h=Math.max(g,c),p=Math.min(g+m.nodeSize,u),d=g,f=m),g>=c&&g<=u&&(i&&i===m.type&&n.setNodeMarkup(g,void 0,{...m.attrs,...e}),s&&m.marks.length&&m.marks.forEach(y=>{if(s===y.type){let b=Math.max(g,c),x=Math.min(g+m.nodeSize,u);n.addMark(b,x,s.create({...y.attrs,...e}))}}))}),f&&(d!==void 0&&n.setNodeMarkup(d,void 0,{...f.attrs,...e}),s&&f.marks.length&&f.marks.forEach(m=>{s===m.type&&n.addMark(h,p,s.create({...m.attrs,...e}))}))}),!0):!1},_y=(t,e={})=>({state:n,dispatch:r})=>{let o=Q(t,n.schema);return gu(o,e)(n,r)},jy=(t,e={})=>({state:n,dispatch:r})=>{let o=Q(t,n.schema);return yu(o,e)(n,r)},qu=$.create({name:"commands",addCommands(){return{...Uu}}}),Gu=$.create({name:"delete",onUpdate({transaction:t,appendedTransactions:e}){var n,r,o;let i=()=>{var s,l,a,c;if((c=(a=(l=(s=this.editor.options.coreExtensionOptions)==null?void 0:s.delete)==null?void 0:l.filterTransaction)==null?void 0:a.call(l,t))!=null?c:t.getMeta("y-sync$"))return;let u=Hs(t.before,[t,...e]);js(u).forEach(h=>{u.mapping.mapResult(h.oldRange.from).deletedAfter&&u.mapping.mapResult(h.oldRange.to).deletedBefore&&u.before.nodesBetween(h.oldRange.from,h.oldRange.to,(p,m)=>{let g=m+p.nodeSize-2,y=h.oldRange.from<=m&&g<=h.oldRange.to;this.editor.emit("delete",{type:"node",node:p,from:m,to:g,newFrom:u.mapping.map(m),newTo:u.mapping.map(g),deletedRange:h.oldRange,newRange:h.newRange,partial:!y,editor:this.editor,transaction:t,combinedTransform:u})})});let f=u.mapping;u.steps.forEach((h,p)=>{var m,g;if(h instanceof lt){let y=f.slice(p).map(h.from,-1),b=f.slice(p).map(h.to),x=f.invert().map(y,-1),C=f.invert().map(b),S=(m=u.doc.nodeAt(y-1))==null?void 0:m.marks.some(P=>P.eq(h.mark)),R=(g=u.doc.nodeAt(b))==null?void 0:g.marks.some(P=>P.eq(h.mark));this.editor.emit("delete",{type:"mark",mark:h.mark,from:h.from,to:h.to,deletedRange:{from:x,to:C},newRange:{from:y,to:b},partial:!!(R||S),editor:this.editor,transaction:t,combinedTransform:u})}})};(o=(r=(n=this.editor.options.coreExtensionOptions)==null?void 0:n.delete)==null?void 0:r.async)==null||o?setTimeout(i,0):i()}}),Yu=$.create({name:"drop",addProseMirrorPlugins(){return[new N({key:new L("tiptapDrop"),props:{handleDrop:(t,e,n,r)=>{this.editor.emit("drop",{editor:this.editor,event:e,slice:n,moved:r})}}})]}}),Xu=$.create({name:"editable",addProseMirrorPlugins(){return[new N({key:new L("editable"),props:{editable:()=>this.editor.options.editable}})]}}),Qu=new L("focusEvents"),Zu=$.create({name:"focusEvents",addProseMirrorPlugins(){let{editor:t}=this;return[new N({key:Qu,props:{handleDOMEvents:{focus:(e,n)=>{t.isFocused=!0;let r=t.state.tr.setMeta("focus",{event:n}).setMeta("addToHistory",!1);return e.dispatch(r),!1},blur:(e,n)=>{t.isFocused=!1;let r=t.state.tr.setMeta("blur",{event:n}).setMeta("addToHistory",!1);return e.dispatch(r),!1}}}})]}}),ed=$.create({name:"keymap",addKeyboardShortcuts(){let t=()=>this.editor.commands.first(({commands:s})=>[()=>s.undoInputRule(),()=>s.command(({tr:l})=>{let{selection:a,doc:c}=l,{empty:u,$anchor:d}=a,{pos:f,parent:h}=d,p=d.parent.isTextblock&&f>0?l.doc.resolve(f-1):d,m=p.parent.type.spec.isolating,g=d.pos-d.parentOffset,y=m&&p.parent.childCount===1?g===d.pos:E.atStart(c).from===f;return!u||!h.type.isTextblock||h.textContent.length||!y||y&&d.parent.type.name==="paragraph"?!1:s.clearNodes()}),()=>s.deleteSelection(),()=>s.joinBackward(),()=>s.selectNodeBackward()]),e=()=>this.editor.commands.first(({commands:s})=>[()=>s.deleteSelection(),()=>s.deleteCurrentNode(),()=>s.joinForward(),()=>s.selectNodeForward()]),r={Enter:()=>this.editor.commands.first(({commands:s})=>[()=>s.newlineInCode(),()=>s.createParagraphNear(),()=>s.liftEmptyBlock(),()=>s.splitBlock()]),"Mod-Enter":()=>this.editor.commands.exitCode(),Backspace:t,"Mod-Backspace":t,"Shift-Backspace":t,Delete:e,"Mod-Delete":e,"Mod-a":()=>this.editor.commands.selectAll()},o={...r},i={...r,"Ctrl-h":t,"Alt-Backspace":t,"Ctrl-d":e,"Ctrl-Alt-Backspace":e,"Alt-Delete":e,"Alt-d":e,"Ctrl-a":()=>this.editor.commands.selectTextblockStart(),"Ctrl-e":()=>this.editor.commands.selectTextblockEnd()};return Gs()||Ju()?i:o},addProseMirrorPlugins(){return[new N({key:new L("clearDocument"),appendTransaction:(t,e,n)=>{if(t.some(m=>m.getMeta("composition")))return;let r=t.some(m=>m.docChanged)&&!e.doc.eq(n.doc),o=t.some(m=>m.getMeta("preventClearDocument"));if(!r||o)return;let{empty:i,from:s,to:l}=e.selection,a=E.atStart(e.doc).from,c=E.atEnd(e.doc).to;if(i||!(s===a&&l===c)||!tr(n.doc))return;let f=n.tr,h=ro({state:n,transaction:f}),{commands:p}=new oo({editor:this.editor,state:h});if(p.clearNodes(),!!f.steps.length)return f}})]}}),td=$.create({name:"paste",addProseMirrorPlugins(){return[new N({key:new L("tiptapPaste"),props:{handlePaste:(t,e,n)=>{this.editor.emit("paste",{editor:this.editor,event:e,slice:n})}}})]}}),nd=$.create({name:"tabindex",addProseMirrorPlugins(){return[new N({key:new L("tabindex"),props:{attributes:()=>this.editor.isEditable?{tabindex:"0"}:{}}})]}}),Ky=class wn{constructor(e,n,r=!1,o=null){this.currentNode=null,this.actualDepth=null,this.isBlock=r,this.resolvedPos=e,this.editor=n,this.currentNode=o}get name(){return this.node.type.name}get node(){return this.currentNode||this.resolvedPos.node()}get element(){return this.editor.view.domAtPos(this.pos).node}get depth(){var e;return(e=this.actualDepth)!=null?e:this.resolvedPos.depth}get pos(){return this.resolvedPos.pos}get content(){return this.node.content}set content(e){let n=this.from,r=this.to;if(this.isBlock){if(this.content.size===0){console.error(`You can\u2019t set content on a block node. Tried to set content on ${this.name} at ${this.pos}`);return}n=this.from+1,r=this.to-1}this.editor.commands.insertContentAt({from:n,to:r},e)}get attributes(){return this.node.attrs}get textContent(){return this.node.textContent}get size(){return this.node.nodeSize}get from(){return this.isBlock?this.pos:this.resolvedPos.start(this.resolvedPos.depth)}get range(){return{from:this.from,to:this.to}}get to(){return this.isBlock?this.pos+this.size:this.resolvedPos.end(this.resolvedPos.depth)+(this.node.isText?0:1)}get parent(){if(this.depth===0)return null;let e=this.resolvedPos.start(this.resolvedPos.depth-1),n=this.resolvedPos.doc.resolve(e);return new wn(n,this.editor)}get before(){let e=this.resolvedPos.doc.resolve(this.from-(this.isBlock?1:2));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.from-3)),new wn(e,this.editor)}get after(){let e=this.resolvedPos.doc.resolve(this.to+(this.isBlock?2:1));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.to+3)),new wn(e,this.editor)}get children(){let e=[];return this.node.content.forEach((n,r)=>{let o=n.isBlock&&!n.isTextblock,i=n.isAtom&&!n.isText,s=this.pos+r+(i?0:1);if(s<0||s>this.resolvedPos.doc.nodeSize-2)return;let l=this.resolvedPos.doc.resolve(s);if(!o&&l.depth<=this.depth)return;let a=new wn(l,this.editor,o,o?n:null);o&&(a.actualDepth=this.depth+1),e.push(new wn(l,this.editor,o,o?n:null))}),e}get firstChild(){return this.children[0]||null}get lastChild(){let e=this.children;return e[e.length-1]||null}closest(e,n={}){let r=null,o=this.parent;for(;o&&!r;){if(o.node.type.name===e)if(Object.keys(n).length>0){let i=o.node.attrs,s=Object.keys(n);for(let l=0;l<s.length;l+=1){let a=s[l];if(i[a]!==n[a])break}}else r=o;o=o.parent}return r}querySelector(e,n={}){return this.querySelectorAll(e,n,!0)[0]||null}querySelectorAll(e,n={},r=!1){let o=[];if(!this.children||this.children.length===0)return o;let i=Object.keys(n);return this.children.forEach(s=>{r&&o.length>0||(s.node.type.name===e&&i.every(a=>n[a]===s.node.attrs[a])&&o.push(s),!(r&&o.length>0)&&(o=o.concat(s.querySelectorAll(e,n,r))))}),o}setAttribute(e){let{tr:n}=this.editor.state;n.setNodeMarkup(this.from,void 0,{...this.node.attrs,...e}),this.editor.view.dispatch(n)}},Uy=`.ProseMirror {
  position: relative;
}

.ProseMirror {
  word-wrap: break-word;
  white-space: pre-wrap;
  white-space: break-spaces;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-feature-settings: "liga" 0; /* the above doesn't seem to work in Edge */
}

.ProseMirror [contenteditable="false"] {
  white-space: normal;
}

.ProseMirror [contenteditable="false"] [contenteditable="true"] {
  white-space: pre-wrap;
}

.ProseMirror pre {
  white-space: pre-wrap;
}

img.ProseMirror-separator {
  display: inline !important;
  border: none !important;
  margin: 0 !important;
  width: 0 !important;
  height: 0 !important;
}

.ProseMirror-gapcursor {
  display: none;
  pointer-events: none;
  position: absolute;
  margin: 0;
}

.ProseMirror-gapcursor:after {
  content: "";
  display: block;
  position: absolute;
  top: -2px;
  width: 20px;
  border-top: 1px solid black;
  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;
}

@keyframes ProseMirror-cursor-blink {
  to {
    visibility: hidden;
  }
}

.ProseMirror-hideselection *::selection {
  background: transparent;
}

.ProseMirror-hideselection *::-moz-selection {
  background: transparent;
}

.ProseMirror-hideselection * {
  caret-color: transparent;
}

.ProseMirror-focused .ProseMirror-gapcursor {
  display: block;
}`;function Jy(t,e,n){let r=document.querySelector(`style[data-tiptap-style${n?`-${n}`:""}]`);if(r!==null)return r;let o=document.createElement("style");return e&&o.setAttribute("nonce",e),o.setAttribute(`data-tiptap-style${n?`-${n}`:""}`,""),o.innerHTML=t,document.getElementsByTagName("head")[0].appendChild(o),o}var rd=class extends gg{constructor(t={}){super(),this.css=null,this.editorView=null,this.isFocused=!1,this.isInitialized=!1,this.extensionStorage={},this.instanceId=Math.random().toString(36).slice(2,9),this.options={element:typeof document<"u"?document.createElement("div"):null,content:"",injectCSS:!0,injectNonce:void 0,extensions:[],autofocus:!1,editable:!0,editorProps:{},parseOptions:{},coreExtensionOptions:{},enableInputRules:!0,enablePasteRules:!0,enableCoreExtensions:!0,enableContentCheck:!1,emitContentError:!1,onBeforeCreate:()=>null,onCreate:()=>null,onUpdate:()=>null,onSelectionUpdate:()=>null,onTransaction:()=>null,onFocus:()=>null,onBlur:()=>null,onDestroy:()=>null,onContentError:({error:r})=>{throw r},onPaste:()=>null,onDrop:()=>null,onDelete:()=>null},this.isCapturingTransaction=!1,this.capturedTransaction=null,this.setOptions(t),this.createExtensionManager(),this.createCommandManager(),this.createSchema(),this.on("beforeCreate",this.options.onBeforeCreate),this.emit("beforeCreate",{editor:this}),this.on("contentError",this.options.onContentError),this.on("create",this.options.onCreate),this.on("update",this.options.onUpdate),this.on("selectionUpdate",this.options.onSelectionUpdate),this.on("transaction",this.options.onTransaction),this.on("focus",this.options.onFocus),this.on("blur",this.options.onBlur),this.on("destroy",this.options.onDestroy),this.on("drop",({event:r,slice:o,moved:i})=>this.options.onDrop(r,o,i)),this.on("paste",({event:r,slice:o})=>this.options.onPaste(r,o)),this.on("delete",this.options.onDelete);let e=this.createDoc(),n=Vu(e,this.options.autofocus);this.editorState=Ir.create({doc:e,schema:this.schema,selection:n||void 0}),this.options.element&&this.mount(this.options.element)}mount(t){if(typeof document>"u")throw new Error("[tiptap error]: The editor cannot be mounted because there is no 'document' defined in this environment.");this.createView(t),window.setTimeout(()=>{this.isDestroyed||(this.commands.focus(this.options.autofocus),this.emit("create",{editor:this}),this.isInitialized=!0)},0)}unmount(){var t;if(this.editorView){let e=this.editorView.dom;e?.editor&&delete e.editor,this.editorView.destroy()}this.editorView=null,this.isInitialized=!1,(t=this.css)==null||t.remove(),this.css=null}get storage(){return this.extensionStorage}get commands(){return this.commandManager.commands}chain(){return this.commandManager.chain()}can(){return this.commandManager.can()}injectCSS(){this.options.injectCSS&&typeof document<"u"&&(this.css=Jy(Uy,this.options.injectNonce))}setOptions(t={}){this.options={...this.options,...t},!(!this.editorView||!this.state||this.isDestroyed)&&(this.options.editorProps&&this.view.setProps(this.options.editorProps),this.view.updateState(this.state))}setEditable(t,e=!0){this.setOptions({editable:t}),e&&this.emit("update",{editor:this,transaction:this.state.tr,appendedTransactions:[]})}get isEditable(){return this.options.editable&&this.view&&this.view.editable}get view(){return this.editorView?this.editorView:new Proxy({state:this.editorState,updateState:t=>{this.editorState=t},dispatch:t=>{this.editorState=this.state.apply(t)},composing:!1,dragging:null,editable:!0,isDestroyed:!1},{get:(t,e)=>{if(e==="state")return this.editorState;if(e in t)return Reflect.get(t,e);throw new Error(`[tiptap error]: The editor view is not available. Cannot access view['${e}']. The editor may not be mounted yet.`)}})}get state(){return this.editorView&&(this.editorState=this.view.state),this.editorState}registerPlugin(t,e){let n=Nu(e)?e(t,[...this.state.plugins]):[...this.state.plugins,t],r=this.state.reconfigure({plugins:n});return this.view.updateState(r),r}unregisterPlugin(t){if(this.isDestroyed)return;let e=this.state.plugins,n=e;if([].concat(t).forEach(o=>{let i=typeof o=="string"?`${o}$`:o.key;n=n.filter(s=>!s.key.startsWith(i))}),e.length===n.length)return;let r=this.state.reconfigure({plugins:n});return this.view.updateState(r),r}createExtensionManager(){var t,e;let r=[...this.options.enableCoreExtensions?[Xu,Ku.configure({blockSeparator:(e=(t=this.options.coreExtensionOptions)==null?void 0:t.clipboardTextSerializer)==null?void 0:e.blockSeparator}),qu,Zu,ed,nd,Yu,td,Gu].filter(o=>typeof this.options.enableCoreExtensions=="object"?this.options.enableCoreExtensions[o.name]!==!1:!0):[],...this.options.extensions].filter(o=>["extension","node","mark"].includes(o?.type));this.extensionManager=new uo(r,this)}createCommandManager(){this.commandManager=new oo({editor:this})}createSchema(){this.schema=this.extensionManager.schema}createDoc(){let t;try{t=Bs(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:this.options.enableContentCheck})}catch(e){if(!(e instanceof Error)||!["[tiptap error]: Invalid JSON content","[tiptap error]: Invalid HTML content"].includes(e.message))throw e;this.emit("contentError",{editor:this,error:e,disableCollaboration:()=>{"collaboration"in this.storage&&typeof this.storage.collaboration=="object"&&this.storage.collaboration&&(this.storage.collaboration.isDisabled=!0),this.options.extensions=this.options.extensions.filter(n=>n.name!=="collaboration"),this.createExtensionManager()}}),t=Bs(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:!1})}return t}createView(t){var e;this.editorView=new qn(t,{...this.options.editorProps,attributes:{role:"textbox",...(e=this.options.editorProps)==null?void 0:e.attributes},dispatchTransaction:this.dispatchTransaction.bind(this),state:this.editorState});let n=this.state.reconfigure({plugins:this.extensionManager.plugins});this.view.updateState(n),this.createNodeViews(),this.prependClass(),this.injectCSS();let r=this.view.dom;r.editor=this}createNodeViews(){this.view.isDestroyed||this.view.setProps({markViews:this.extensionManager.markViews,nodeViews:this.extensionManager.nodeViews})}prependClass(){this.view.dom.className=`tiptap ${this.view.dom.className}`}captureTransaction(t){this.isCapturingTransaction=!0,t(),this.isCapturingTransaction=!1;let e=this.capturedTransaction;return this.capturedTransaction=null,e}dispatchTransaction(t){if(this.view.isDestroyed)return;if(this.isCapturingTransaction){if(!this.capturedTransaction){this.capturedTransaction=t;return}t.steps.forEach(c=>{var u;return(u=this.capturedTransaction)==null?void 0:u.step(c)});return}let{state:e,transactions:n}=this.state.applyTransaction(t),r=!this.state.selection.eq(e.selection),o=n.includes(t),i=this.state;if(this.emit("beforeTransaction",{editor:this,transaction:t,nextState:e}),!o)return;this.view.updateState(e),this.emit("transaction",{editor:this,transaction:t,appendedTransactions:n.slice(1)}),r&&this.emit("selectionUpdate",{editor:this,transaction:t});let s=n.findLast(c=>c.getMeta("focus")||c.getMeta("blur")),l=s?.getMeta("focus"),a=s?.getMeta("blur");l&&this.emit("focus",{editor:this,event:l.event,transaction:s}),a&&this.emit("blur",{editor:this,event:a.event,transaction:s}),!(t.getMeta("preventUpdate")||!n.some(c=>c.docChanged)||i.doc.eq(e.doc))&&this.emit("update",{editor:this,transaction:t,appendedTransactions:n.slice(1)})}getAttributes(t){return _s(this.state,t)}isActive(t,e){let n=typeof t=="string"?t:null,r=typeof t=="string"?e:t;return Js(this.state,n,r)}getJSON(){return this.state.doc.toJSON()}getHTML(){return Vs(this.state.doc.content,this.schema)}getText(t){let{blockSeparator:e=`

`,textSerializers:n={}}=t||{};return Sg(this.state.doc,{blockSeparator:e,textSerializers:{...Iu(this.schema),...n}})}get isEmpty(){return tr(this.state.doc)}destroy(){this.emit("destroy"),this.unmount(),this.removeAllListeners()}get isDestroyed(){var t,e;return(e=(t=this.editorView)==null?void 0:t.isDestroyed)!=null?e:!0}$node(t,e){var n;return((n=this.$doc)==null?void 0:n.querySelector(t,e))||null}$nodes(t,e){var n;return((n=this.$doc)==null?void 0:n.querySelectorAll(t,e))||null}$pos(t){let e=this.state.doc.resolve(t);return new Ky(e,this)}get $doc(){return this.$pos(0)}};function Ie(t){return new co({find:t.find,handler:({state:e,range:n,match:r})=>{let o=W(t.getAttributes,void 0,r);if(o===!1||o===null)return null;let{tr:i}=e,s=r[r.length-1],l=r[0];if(s){let a=l.search(/\S/),c=n.from+l.indexOf(s),u=c+s.length;if(so(n.from,n.to,e.doc).filter(h=>h.mark.type.excluded.find(m=>m===t.type&&m!==h.mark.type)).filter(h=>h.to>c).length)return null;u<n.to&&i.delete(u,n.to),c>n.from&&i.delete(n.from+a,c);let f=n.from+a+s.length;i.addMark(n.from+a,f,t.type.create(o||{})),i.removeStoredMark(t.type)}}})}function fo(t){return new co({find:t.find,handler:({state:e,range:n,match:r})=>{let o=W(t.getAttributes,void 0,r)||{},{tr:i}=e,s=n.from,l=n.to,a=t.type.create(o);if(r[1]){let c=r[0].lastIndexOf(r[1]),u=s+c;u>l?u=l:l=u+r[1].length;let d=r[0][r[0].length-1];i.insertText(d,s+r[0].length-1),i.replaceWith(u,l,a)}else if(r[0]){let c=t.type.isInline?s:s-1;i.insert(c,t.type.create(o)).delete(i.mapping.map(s),i.mapping.map(l))}i.scrollIntoView()}})}function nr(t){return new co({find:t.find,handler:({state:e,range:n,match:r})=>{let o=e.doc.resolve(n.from),i=W(t.getAttributes,void 0,r)||{};if(!o.node(-1).canReplaceWith(o.index(-1),o.indexAfter(-1),t.type))return null;e.tr.delete(n.from,n.to).setBlockType(n.from,n.from,t.type,i)}})}function Xe(t){return new co({find:t.find,handler:({state:e,range:n,match:r,chain:o})=>{let i=W(t.getAttributes,void 0,r)||{},s=e.tr.delete(n.from,n.to),a=s.doc.resolve(n.from).blockRange(),c=a&&cn(a,t.type,i);if(!c)return null;if(s.wrap(a,c),t.keepMarks&&t.editor){let{selection:d,storedMarks:f}=e,{splittableMarks:h}=t.editor.extensionManager,p=f||d.$to.parentOffset&&d.$from.marks();if(p){let m=p.filter(g=>h.includes(g.type.name));s.ensureMarks(m)}}if(t.keepAttributes){let d=t.type.name==="bulletList"||t.type.name==="orderedList"?"listItem":"taskList";o().updateAttributes(d,i).run()}let u=s.doc.resolve(n.from-1).nodeBefore;u&&u.type===t.type&&Ne(s.doc,n.from-1)&&(!t.joinPredicate||t.joinPredicate(r,u))&&s.join(n.from-1)}})}function od(t,e){let{selection:n}=t,{$from:r}=n;if(n instanceof O){let i=r.index();return r.parent.canReplaceWith(i,i+1,e)}let o=r.depth;for(;o>=0;){let i=r.index(o);if(r.node(o).contentMatchAt(i).matchType(e))return!0;o-=1}return!1}function id(t){return t.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&")}function qy(t,e,n={}){let{state:r}=e,{doc:o,tr:i}=r,s=t;o.descendants((l,a)=>{let c=i.mapping.map(a),u=i.mapping.map(a)+l.nodeSize,d=null;if(l.marks.forEach(h=>{if(h!==s)return!1;d=h}),!d)return;let f=!1;if(Object.keys(n).forEach(h=>{n[h]!==d.attrs[h]&&(f=!0)}),f){let h=t.type.create({...t.attrs,...n});i.removeMark(c,u,t.type),i.addMark(c,u,h)}}),i.docChanged&&e.view.dispatch(i)}var z=class sd extends qs{constructor(){super(...arguments),this.type="node"}static create(e={}){let n=typeof e=="function"?e():e;return new sd(n)}configure(e){return super.configure(e)}extend(e){let n=typeof e=="function"?e():e;return super.extend(n)}};function Ce(t){return new Rg({find:t.find,handler:({state:e,range:n,match:r,pasteEvent:o})=>{let i=W(t.getAttributes,void 0,r,o);if(i===!1||i===null)return null;let{tr:s}=e,l=r[r.length-1],a=r[0],c=n.to;if(l){let u=a.search(/\S/),d=n.from+a.indexOf(l),f=d+l.length;if(so(n.from,n.to,e.doc).filter(p=>p.mark.type.excluded.find(g=>g===t.type&&g!==p.mark.type)).filter(p=>p.to>d).length)return null;f<n.to&&s.delete(f,n.to),d>n.from&&s.delete(n.from+u,d),c=n.from+u+l.length,s.addMark(n.from+u,c,t.type.create(i||{})),s.removeStoredMark(t.type)}}})}function ld(t={}){return new N({view(e){return new Ys(e,t)}})}var Ys=class{constructor(e,n){var r;this.editorView=e,this.cursorPos=null,this.element=null,this.timeout=-1,this.width=(r=n.width)!==null&&r!==void 0?r:1,this.color=n.color===!1?void 0:n.color||"black",this.class=n.class,this.handlers=["dragover","dragend","drop","dragleave"].map(o=>{let i=s=>{this[o](s)};return e.dom.addEventListener(o,i),{name:o,handler:i}})}destroy(){this.handlers.forEach(({name:e,handler:n})=>this.editorView.dom.removeEventListener(e,n))}update(e,n){this.cursorPos!=null&&n.doc!=e.state.doc&&(this.cursorPos>e.state.doc.content.size?this.setCursor(null):this.updateOverlay())}setCursor(e){e!=this.cursorPos&&(this.cursorPos=e,e==null?(this.element.parentNode.removeChild(this.element),this.element=null):this.updateOverlay())}updateOverlay(){let e=this.editorView.state.doc.resolve(this.cursorPos),n=!e.parent.inlineContent,r,o=this.editorView.dom,i=o.getBoundingClientRect(),s=i.width/o.offsetWidth,l=i.height/o.offsetHeight;if(n){let d=e.nodeBefore,f=e.nodeAfter;if(d||f){let h=this.editorView.nodeDOM(this.cursorPos-(d?d.nodeSize:0));if(h){let p=h.getBoundingClientRect(),m=d?p.bottom:p.top;d&&f&&(m=(m+this.editorView.nodeDOM(this.cursorPos).getBoundingClientRect().top)/2);let g=this.width/2*l;r={left:p.left,right:p.right,top:m-g,bottom:m+g}}}}if(!r){let d=this.editorView.coordsAtPos(this.cursorPos),f=this.width/2*s;r={left:d.left-f,right:d.left+f,top:d.top,bottom:d.bottom}}let a=this.editorView.dom.offsetParent;this.element||(this.element=a.appendChild(document.createElement("div")),this.class&&(this.element.className=this.class),this.element.style.cssText="position: absolute; z-index: 50; pointer-events: none;",this.color&&(this.element.style.backgroundColor=this.color)),this.element.classList.toggle("prosemirror-dropcursor-block",n),this.element.classList.toggle("prosemirror-dropcursor-inline",!n);let c,u;if(!a||a==document.body&&getComputedStyle(a).position=="static")c=-pageXOffset,u=-pageYOffset;else{let d=a.getBoundingClientRect(),f=d.width/a.offsetWidth,h=d.height/a.offsetHeight;c=d.left-a.scrollLeft*f,u=d.top-a.scrollTop*h}this.element.style.left=(r.left-c)/s+"px",this.element.style.top=(r.top-u)/l+"px",this.element.style.width=(r.right-r.left)/s+"px",this.element.style.height=(r.bottom-r.top)/l+"px"}scheduleRemoval(e){clearTimeout(this.timeout),this.timeout=setTimeout(()=>this.setCursor(null),e)}dragover(e){if(!this.editorView.editable)return;let n=this.editorView.posAtCoords({left:e.clientX,top:e.clientY}),r=n&&n.inside>=0&&this.editorView.state.doc.nodeAt(n.inside),o=r&&r.type.spec.disableDropCursor,i=typeof o=="function"?o(this.editorView,n,e):o;if(n&&!i){let s=n.pos;if(this.editorView.dragging&&this.editorView.dragging.slice){let l=Rr(this.editorView.state.doc,s,this.editorView.dragging.slice);l!=null&&(s=l)}this.setCursor(s),this.scheduleRemoval(5e3)}}dragend(){this.scheduleRemoval(20)}drop(){this.scheduleRemoval(20)}dragleave(e){this.editorView.dom.contains(e.relatedTarget)||this.setCursor(null)}};var se=class t extends E{constructor(e){super(e,e)}map(e,n){let r=e.resolve(n.map(this.head));return t.valid(r)?new t(r):E.near(r)}content(){return M.empty}eq(e){return e instanceof t&&e.head==this.head}toJSON(){return{type:"gapcursor",pos:this.head}}static fromJSON(e,n){if(typeof n.pos!="number")throw new RangeError("Invalid input for GapCursor.fromJSON");return new t(e.resolve(n.pos))}getBookmark(){return new Xs(this.anchor)}static valid(e){let n=e.parent;if(n.isTextblock||!Gy(e)||!Yy(e))return!1;let r=n.type.spec.allowGapCursor;if(r!=null)return r;let o=n.contentMatchAt(e.index()).defaultType;return o&&o.isTextblock}static findGapCursorFrom(e,n,r=!1){e:for(;;){if(!r&&t.valid(e))return e;let o=e.pos,i=null;for(let s=e.depth;;s--){let l=e.node(s);if(n>0?e.indexAfter(s)<l.childCount:e.index(s)>0){i=l.child(n>0?e.indexAfter(s):e.index(s)-1);break}else if(s==0)return null;o+=n;let a=e.doc.resolve(o);if(t.valid(a))return a}for(;;){let s=n>0?i.firstChild:i.lastChild;if(!s){if(i.isAtom&&!i.isText&&!O.isSelectable(i)){e=e.doc.resolve(o+i.nodeSize*n),r=!1;continue e}break}i=s,o+=n;let l=e.doc.resolve(o);if(t.valid(l))return l}return null}}};se.prototype.visible=!1;se.findFrom=se.findGapCursorFrom;E.jsonID("gapcursor",se);var Xs=class t{constructor(e){this.pos=e}map(e){return new t(e.map(this.pos))}resolve(e){let n=e.resolve(this.pos);return se.valid(n)?new se(n):E.near(n)}};function Gy(t){for(let e=t.depth;e>=0;e--){let n=t.index(e),r=t.node(e);if(n==0){if(r.type.spec.isolating)return!0;continue}for(let o=r.child(n-1);;o=o.lastChild){if(o.childCount==0&&!o.inlineContent||o.isAtom||o.type.spec.isolating)return!0;if(o.inlineContent)return!1}}return!0}function Yy(t){for(let e=t.depth;e>=0;e--){let n=t.indexAfter(e),r=t.node(e);if(n==r.childCount){if(r.type.spec.isolating)return!0;continue}for(let o=r.child(n);;o=o.firstChild){if(o.childCount==0&&!o.inlineContent||o.isAtom||o.type.spec.isolating)return!0;if(o.inlineContent)return!1}}return!0}function ad(){return new N({props:{decorations:e0,createSelectionBetween(t,e,n){return e.pos==n.pos&&se.valid(n)?new se(n):null},handleClick:Qy,handleKeyDown:Xy,handleDOMEvents:{beforeinput:Zy}}})}var Xy=Xn({ArrowLeft:ho("horiz",-1),ArrowRight:ho("horiz",1),ArrowUp:ho("vert",-1),ArrowDown:ho("vert",1)});function ho(t,e){let n=t=="vert"?e>0?"down":"up":e>0?"right":"left";return function(r,o,i){let s=r.selection,l=e>0?s.$to:s.$from,a=s.empty;if(s instanceof A){if(!i.endOfTextblock(n)||l.depth==0)return!1;a=!1,l=r.doc.resolve(e>0?l.after():l.before())}let c=se.findGapCursorFrom(l,e,a);return c?(o&&o(r.tr.setSelection(new se(c))),!0):!1}}function Qy(t,e,n){if(!t||!t.editable)return!1;let r=t.state.doc.resolve(e);if(!se.valid(r))return!1;let o=t.posAtCoords({left:n.clientX,top:n.clientY});return o&&o.inside>-1&&O.isSelectable(t.state.doc.nodeAt(o.inside))?!1:(t.dispatch(t.state.tr.setSelection(new se(r))),!0)}function Zy(t,e){if(e.inputType!="insertCompositionText"||!(t.state.selection instanceof se))return!1;let{$from:n}=t.state.selection,r=n.parent.contentMatchAt(n.index()).findWrapping(t.state.schema.nodes.text);if(!r)return!1;let o=w.empty;for(let s=r.length-1;s>=0;s--)o=w.from(r[s].createAndFill(null,o));let i=t.state.tr.replace(n.pos,n.pos,new M(o,0,0));return i.setSelection(A.near(i.doc.resolve(n.pos+1))),t.dispatch(i),!1}function e0(t){if(!(t.selection instanceof se))return null;let e=document.createElement("div");return e.className="ProseMirror-gapcursor",J.create(t.doc,[Y.widget(t.selection.head,e,{key:"gapcursor"})])}var po=200,de=function(){};de.prototype.append=function(e){return e.length?(e=de.from(e),!this.length&&e||e.length<po&&this.leafAppend(e)||this.length<po&&e.leafPrepend(this)||this.appendInner(e)):this};de.prototype.prepend=function(e){return e.length?de.from(e).append(this):this};de.prototype.appendInner=function(e){return new t0(this,e)};de.prototype.slice=function(e,n){return e===void 0&&(e=0),n===void 0&&(n=this.length),e>=n?de.empty:this.sliceInner(Math.max(0,e),Math.min(this.length,n))};de.prototype.get=function(e){if(!(e<0||e>=this.length))return this.getInner(e)};de.prototype.forEach=function(e,n,r){n===void 0&&(n=0),r===void 0&&(r=this.length),n<=r?this.forEachInner(e,n,r,0):this.forEachInvertedInner(e,n,r,0)};de.prototype.map=function(e,n,r){n===void 0&&(n=0),r===void 0&&(r=this.length);var o=[];return this.forEach(function(i,s){return o.push(e(i,s))},n,r),o};de.from=function(e){return e instanceof de?e:e&&e.length?new cd(e):de.empty};var cd=function(t){function e(r){t.call(this),this.values=r}t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e;var n={length:{configurable:!0},depth:{configurable:!0}};return e.prototype.flatten=function(){return this.values},e.prototype.sliceInner=function(o,i){return o==0&&i==this.length?this:new e(this.values.slice(o,i))},e.prototype.getInner=function(o){return this.values[o]},e.prototype.forEachInner=function(o,i,s,l){for(var a=i;a<s;a++)if(o(this.values[a],l+a)===!1)return!1},e.prototype.forEachInvertedInner=function(o,i,s,l){for(var a=i-1;a>=s;a--)if(o(this.values[a],l+a)===!1)return!1},e.prototype.leafAppend=function(o){if(this.length+o.length<=po)return new e(this.values.concat(o.flatten()))},e.prototype.leafPrepend=function(o){if(this.length+o.length<=po)return new e(o.flatten().concat(this.values))},n.length.get=function(){return this.values.length},n.depth.get=function(){return 0},Object.defineProperties(e.prototype,n),e}(de);de.empty=new cd([]);var t0=function(t){function e(n,r){t.call(this),this.left=n,this.right=r,this.length=n.length+r.length,this.depth=Math.max(n.depth,r.depth)+1}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.flatten=function(){return this.left.flatten().concat(this.right.flatten())},e.prototype.getInner=function(r){return r<this.left.length?this.left.get(r):this.right.get(r-this.left.length)},e.prototype.forEachInner=function(r,o,i,s){var l=this.left.length;if(o<l&&this.left.forEachInner(r,o,Math.min(i,l),s)===!1||i>l&&this.right.forEachInner(r,Math.max(o-l,0),Math.min(this.length,i)-l,s+l)===!1)return!1},e.prototype.forEachInvertedInner=function(r,o,i,s){var l=this.left.length;if(o>l&&this.right.forEachInvertedInner(r,o-l,Math.max(i,l)-l,s+l)===!1||i<l&&this.left.forEachInvertedInner(r,Math.min(o,l),i,s)===!1)return!1},e.prototype.sliceInner=function(r,o){if(r==0&&o==this.length)return this;var i=this.left.length;return o<=i?this.left.slice(r,o):r>=i?this.right.slice(r-i,o-i):this.left.slice(r,i).append(this.right.slice(0,o-i))},e.prototype.leafAppend=function(r){var o=this.right.leafAppend(r);if(o)return new e(this.left,o)},e.prototype.leafPrepend=function(r){var o=this.left.leafPrepend(r);if(o)return new e(o,this.right)},e.prototype.appendInner=function(r){return this.left.depth>=Math.max(this.right.depth,r.depth)+1?new e(this.left,new e(this.right,r)):new e(this,r)},e}(de),Qs=de;var n0=500,Yt=class t{constructor(e,n){this.items=e,this.eventCount=n}popEvent(e,n){if(this.eventCount==0)return null;let r=this.items.length;for(;;r--)if(this.items.get(r-1).selection){--r;break}let o,i;n&&(o=this.remapping(r,this.items.length),i=o.maps.length);let s=e.tr,l,a,c=[],u=[];return this.items.forEach((d,f)=>{if(!d.step){o||(o=this.remapping(r,f+1),i=o.maps.length),i--,u.push(d);return}if(o){u.push(new Qe(d.map));let h=d.step.map(o.slice(i)),p;h&&s.maybeStep(h).doc&&(p=s.mapping.maps[s.mapping.maps.length-1],c.push(new Qe(p,void 0,void 0,c.length+u.length))),i--,p&&o.appendMap(p,i)}else s.maybeStep(d.step);if(d.selection)return l=o?d.selection.map(o.slice(i)):d.selection,a=new t(this.items.slice(0,r).append(u.reverse().concat(c)),this.eventCount-1),!1},this.items.length,0),{remaining:a,transform:s,selection:l}}addTransform(e,n,r,o){let i=[],s=this.eventCount,l=this.items,a=!o&&l.length?l.get(l.length-1):null;for(let u=0;u<e.steps.length;u++){let d=e.steps[u].invert(e.docs[u]),f=new Qe(e.mapping.maps[u],d,n),h;(h=a&&a.merge(f))&&(f=h,u?i.pop():l=l.slice(0,l.length-1)),i.push(f),n&&(s++,n=void 0),o||(a=f)}let c=s-r.depth;return c>o0&&(l=r0(l,c),s-=c),new t(l.append(i),s)}remapping(e,n){let r=new zn;return this.items.forEach((o,i)=>{let s=o.mirrorOffset!=null&&i-o.mirrorOffset>=e?r.maps.length-o.mirrorOffset:void 0;r.appendMap(o.map,s)},e,n),r}addMaps(e){return this.eventCount==0?this:new t(this.items.append(e.map(n=>new Qe(n))),this.eventCount)}rebased(e,n){if(!this.eventCount)return this;let r=[],o=Math.max(0,this.items.length-n),i=e.mapping,s=e.steps.length,l=this.eventCount;this.items.forEach(f=>{f.selection&&l--},o);let a=n;this.items.forEach(f=>{let h=i.getMirror(--a);if(h==null)return;s=Math.min(s,h);let p=i.maps[h];if(f.step){let m=e.steps[h].invert(e.docs[h]),g=f.selection&&f.selection.map(i.slice(a+1,h));g&&l++,r.push(new Qe(p,m,g))}else r.push(new Qe(p))},o);let c=[];for(let f=n;f<s;f++)c.push(new Qe(i.maps[f]));let u=this.items.slice(0,o).append(c).append(r),d=new t(u,l);return d.emptyItemCount()>n0&&(d=d.compress(this.items.length-r.length)),d}emptyItemCount(){let e=0;return this.items.forEach(n=>{n.step||e++}),e}compress(e=this.items.length){let n=this.remapping(0,e),r=n.maps.length,o=[],i=0;return this.items.forEach((s,l)=>{if(l>=e)o.push(s),s.selection&&i++;else if(s.step){let a=s.step.map(n.slice(r)),c=a&&a.getMap();if(r--,c&&n.appendMap(c,r),a){let u=s.selection&&s.selection.map(n.slice(r));u&&i++;let d=new Qe(c.invert(),a,u),f,h=o.length-1;(f=o.length&&o[h].merge(d))?o[h]=f:o.push(d)}}else s.map&&r--},this.items.length,0),new t(Qs.from(o.reverse()),i)}};Yt.empty=new Yt(Qs.empty,0);function r0(t,e){let n;return t.forEach((r,o)=>{if(r.selection&&e--==0)return n=o,!1}),t.slice(n)}var Qe=class t{constructor(e,n,r,o){this.map=e,this.step=n,this.selection=r,this.mirrorOffset=o}merge(e){if(this.step&&e.step&&!e.selection){let n=e.step.merge(this.step);if(n)return new t(n.getMap().invert(),n,this.selection)}}},Ze=class{constructor(e,n,r,o,i){this.done=e,this.undone=n,this.prevRanges=r,this.prevTime=o,this.prevComposition=i}},o0=20;function i0(t,e,n,r){let o=n.getMeta(Gt),i;if(o)return o.historyState;n.getMeta(a0)&&(t=new Ze(t.done,t.undone,null,0,-1));let s=n.getMeta("appendedTransaction");if(n.steps.length==0)return t;if(s&&s.getMeta(Gt))return s.getMeta(Gt).redo?new Ze(t.done.addTransform(n,void 0,r,mo(e)),t.undone,ud(n.mapping.maps),t.prevTime,t.prevComposition):new Ze(t.done,t.undone.addTransform(n,void 0,r,mo(e)),null,t.prevTime,t.prevComposition);if(n.getMeta("addToHistory")!==!1&&!(s&&s.getMeta("addToHistory")===!1)){let l=n.getMeta("composition"),a=t.prevTime==0||!s&&t.prevComposition!=l&&(t.prevTime<(n.time||0)-r.newGroupDelay||!s0(n,t.prevRanges)),c=s?Zs(t.prevRanges,n.mapping):ud(n.mapping.maps);return new Ze(t.done.addTransform(n,a?e.selection.getBookmark():void 0,r,mo(e)),Yt.empty,c,n.time,l??t.prevComposition)}else return(i=n.getMeta("rebased"))?new Ze(t.done.rebased(n,i),t.undone.rebased(n,i),Zs(t.prevRanges,n.mapping),t.prevTime,t.prevComposition):new Ze(t.done.addMaps(n.mapping.maps),t.undone.addMaps(n.mapping.maps),Zs(t.prevRanges,n.mapping),t.prevTime,t.prevComposition)}function s0(t,e){if(!e)return!1;if(!t.docChanged)return!0;let n=!1;return t.mapping.maps[0].forEach((r,o)=>{for(let i=0;i<e.length;i+=2)r<=e[i+1]&&o>=e[i]&&(n=!0)}),n}function ud(t){let e=[];for(let n=t.length-1;n>=0&&e.length==0;n--)t[n].forEach((r,o,i,s)=>e.push(i,s));return e}function Zs(t,e){if(!t)return null;let n=[];for(let r=0;r<t.length;r+=2){let o=e.map(t[r],1),i=e.map(t[r+1],-1);o<=i&&n.push(o,i)}return n}function l0(t,e,n){let r=mo(e),o=Gt.get(e).spec.config,i=(n?t.undone:t.done).popEvent(e,r);if(!i)return null;let s=i.selection.resolve(i.transform.doc),l=(n?t.done:t.undone).addTransform(i.transform,e.selection.getBookmark(),o,r),a=new Ze(n?l:i.remaining,n?i.remaining:l,null,0,-1);return i.transform.setSelection(s).setMeta(Gt,{redo:n,historyState:a})}var el=!1,dd=null;function mo(t){let e=t.plugins;if(dd!=e){el=!1,dd=e;for(let n=0;n<e.length;n++)if(e[n].spec.historyPreserveItems){el=!0;break}}return el}var Gt=new L("history"),a0=new L("closeHistory");function fd(t={}){return t={depth:t.depth||100,newGroupDelay:t.newGroupDelay||500},new N({key:Gt,state:{init(){return new Ze(Yt.empty,Yt.empty,null,0,-1)},apply(e,n,r){return i0(n,r,e,t)}},config:t,props:{handleDOMEvents:{beforeinput(e,n){let r=n.inputType,o=r=="historyUndo"?tl:r=="historyRedo"?nl:null;return o?(n.preventDefault(),o(e.state,e.dispatch)):!1}}}})}function go(t,e){return(n,r)=>{let o=Gt.getState(n);if(!o||(t?o.undone:o.done).eventCount==0)return!1;if(r){let i=l0(o,n,t);i&&r(e?i.scrollIntoView():i)}return!0}}var tl=go(!1,!0),nl=go(!0,!0),A1=go(!1,!1),E1=go(!0,!1);var I1=$.create({name:"characterCount",addOptions(){return{limit:null,mode:"textSize",textCounter:t=>t.length,wordCounter:t=>t.split(" ").filter(e=>e!=="").length}},addStorage(){return{characters:()=>0,words:()=>0}},onBeforeCreate(){this.storage.characters=t=>{let e=t?.node||this.editor.state.doc;if((t?.mode||this.options.mode)==="textSize"){let r=e.textBetween(0,e.content.size,void 0," ");return this.options.textCounter(r)}return e.nodeSize},this.storage.words=t=>{let e=t?.node||this.editor.state.doc,n=e.textBetween(0,e.content.size," "," ");return this.options.wordCounter(n)}},addProseMirrorPlugins(){let t=!1;return[new N({key:new L("characterCount"),appendTransaction:(e,n,r)=>{if(t)return;let o=this.options.limit;if(o==null||o===0){t=!0;return}let i=this.storage.characters({node:r.doc});if(i>o){let s=i-o,l=0,a=s;console.warn(`[CharacterCount] Initial content exceeded limit of ${o} characters. Content was automatically trimmed.`);let c=r.tr.deleteRange(l,a);return t=!0,c}t=!0},filterTransaction:(e,n)=>{let r=this.options.limit;if(!e.docChanged||r===0||r===null||r===void 0)return!0;let o=this.storage.characters({node:n.doc}),i=this.storage.characters({node:e.doc});if(i<=r||o>r&&i>r&&i<=o)return!0;if(o>r&&i>r&&i>o||!e.getMeta("paste"))return!1;let l=e.selection.$head.pos,a=i-r,c=l-a,u=l;return e.deleteRange(c,u),!(this.storage.characters({node:e.doc})>r)}})]}}),pd=$.create({name:"dropCursor",addOptions(){return{color:"currentColor",width:1,class:void 0}},addProseMirrorPlugins(){return[ld(this.options)]}}),$1=$.create({name:"focus",addOptions(){return{className:"has-focus",mode:"all"}},addProseMirrorPlugins(){return[new N({key:new L("focus"),props:{decorations:({doc:t,selection:e})=>{let{isEditable:n,isFocused:r}=this.editor,{anchor:o}=e,i=[];if(!n||!r)return J.create(t,[]);let s=0;this.options.mode==="deepest"&&t.descendants((a,c)=>{if(a.isText)return;if(!(o>=c&&o<=c+a.nodeSize-1))return!1;s+=1});let l=0;return t.descendants((a,c)=>{if(a.isText||!(o>=c&&o<=c+a.nodeSize-1))return!1;if(l+=1,this.options.mode==="deepest"&&s-l>0||this.options.mode==="shallowest"&&l>1)return this.options.mode==="deepest";i.push(Y.node(c,c+a.nodeSize,{class:this.options.className}))}),J.create(t,i)}}})]}}),md=$.create({name:"gapCursor",addProseMirrorPlugins(){return[ad()]},extendNodeSchema(t){var e;let n={name:t.name,options:t.options,storage:t.storage};return{allowGapCursor:(e=W(I(t,"allowGapCursor",n)))!=null?e:null}}}),rl=$.create({name:"placeholder",addOptions(){return{emptyEditorClass:"is-editor-empty",emptyNodeClass:"is-empty",placeholder:"Write something \u2026",showOnlyWhenEditable:!0,showOnlyCurrent:!0,includeChildren:!1}},addProseMirrorPlugins(){return[new N({key:new L("placeholder"),props:{decorations:({doc:t,selection:e})=>{let n=this.editor.isEditable||!this.options.showOnlyWhenEditable,{anchor:r}=e,o=[];if(!n)return null;let i=this.editor.isEmpty;return t.descendants((s,l)=>{let a=r>=l&&r<=l+s.nodeSize,c=!s.isLeaf&&tr(s);if((a||!this.options.showOnlyCurrent)&&c){let u=[this.options.emptyNodeClass];i&&u.push(this.options.emptyEditorClass);let d=Y.node(l,l+s.nodeSize,{class:u.join(" "),"data-placeholder":typeof this.options.placeholder=="function"?this.options.placeholder({editor:this.editor,node:s,pos:l,hasAnchor:a}):this.options.placeholder});o.push(d)}return this.options.includeChildren}),J.create(t,o)}}})]}}),G1=$.create({name:"selection",addOptions(){return{className:"selection"}},addProseMirrorPlugins(){let{editor:t,options:e}=this;return[new N({key:new L("selection"),props:{decorations(n){return n.selection.empty||t.isFocused||!t.isEditable||lo(n.selection)||t.view.dragging?null:J.create(n.doc,[Y.inline(n.selection.from,n.selection.to,{class:e.className})])}}})]}});function hd({types:t,node:e}){return e&&Array.isArray(t)&&t.includes(e.type)||e?.type===t}var Q1=$.create({name:"trailingNode",addOptions(){return{node:"paragraph",notAfter:[]}},addProseMirrorPlugins(){let t=new L(this.name),e=Object.entries(this.editor.schema.nodes).map(([,n])=>n).filter(n=>(this.options.notAfter||[]).concat(this.options.node).includes(n.name));return[new N({key:t,appendTransaction:(n,r,o)=>{let{doc:i,tr:s,schema:l}=o,a=t.getState(o),c=i.content.size,u=l.nodes[this.options.node];if(a)return s.insert(c,u.create())},state:{init:(n,r)=>{let o=r.tr.doc.lastChild;return!hd({node:o,types:e})},apply:(n,r)=>{if(!n.docChanged)return r;let o=n.doc.lastChild;return!hd({node:o,types:e})}}})]}}),gd=$.create({name:"undoRedo",addOptions(){return{depth:100,newGroupDelay:500}},addCommands(){return{undo:()=>({state:t,dispatch:e})=>tl(t,e),redo:()=>({state:t,dispatch:e})=>nl(t,e)}},addProseMirrorPlugins(){return[fd(this.options)]},addKeyboardShortcuts(){return{"Mod-z":()=>this.editor.commands.undo(),"Shift-Mod-z":()=>this.editor.commands.redo(),"Mod-y":()=>this.editor.commands.redo(),"Mod-\u044F":()=>this.editor.commands.undo(),"Shift-Mod-\u044F":()=>this.editor.commands.redo()}}});var kn=(t,e)=>{if(t==="slot")return 0;if(t instanceof Function)return t(e);let{children:n,...r}=e??{};if(t==="svg")throw new Error("SVG elements are not supported in the JSX syntax, use the array syntax instead");return[t,r,n]};var c0=/^\s*>\s$/,u0=z.create({name:"blockquote",addOptions(){return{HTMLAttributes:{}}},content:"block+",group:"block",defining:!0,parseHTML(){return[{tag:"blockquote"}]},renderHTML({HTMLAttributes:t}){return kn("blockquote",{...T(this.options.HTMLAttributes,t),children:kn("slot",{})})},addCommands(){return{setBlockquote:()=>({commands:t})=>t.wrapIn(this.name),toggleBlockquote:()=>({commands:t})=>t.toggleWrap(this.name),unsetBlockquote:()=>({commands:t})=>t.lift(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-b":()=>this.editor.commands.toggleBlockquote()}},addInputRules(){return[Xe({find:c0,type:this.type})]}}),yd=u0;var d0=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))$/,f0=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))/g,h0=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))$/,p0=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))/g,m0=te.create({name:"bold",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"strong"},{tag:"b",getAttrs:t=>t.style.fontWeight!=="normal"&&null},{style:"font-weight=400",clearMark:t=>t.type.name===this.name},{style:"font-weight",getAttrs:t=>/^(bold(er)?|[5-9]\d{2,})$/.test(t)&&null}]},renderHTML({HTMLAttributes:t}){return kn("strong",{...T(this.options.HTMLAttributes,t),children:kn("slot",{})})},addCommands(){return{setBold:()=>({commands:t})=>t.setMark(this.name),toggleBold:()=>({commands:t})=>t.toggleMark(this.name),unsetBold:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-b":()=>this.editor.commands.toggleBold(),"Mod-B":()=>this.editor.commands.toggleBold()}},addInputRules(){return[Ie({find:d0,type:this.type}),Ie({find:h0,type:this.type})]},addPasteRules(){return[Ce({find:f0,type:this.type}),Ce({find:p0,type:this.type})]}}),bd=m0;var g0=/(^|[^`])`([^`]+)`(?!`)/,y0=/(^|[^`])`([^`]+)`(?!`)/g,b0=te.create({name:"code",addOptions(){return{HTMLAttributes:{}}},excludes:"_",code:!0,exitable:!0,parseHTML(){return[{tag:"code"}]},renderHTML({HTMLAttributes:t}){return["code",T(this.options.HTMLAttributes,t),0]},addCommands(){return{setCode:()=>({commands:t})=>t.setMark(this.name),toggleCode:()=>({commands:t})=>t.toggleMark(this.name),unsetCode:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-e":()=>this.editor.commands.toggleCode()}},addInputRules(){return[Ie({find:g0,type:this.type})]},addPasteRules(){return[Ce({find:y0,type:this.type})]}}),xd=b0;var x0=/^```([a-z]+)?[\s\n]$/,w0=/^~~~([a-z]+)?[\s\n]$/,S0=z.create({name:"codeBlock",addOptions(){return{languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,defaultLanguage:null,HTMLAttributes:{}}},content:"text*",marks:"",group:"block",code:!0,defining:!0,addAttributes(){return{language:{default:this.options.defaultLanguage,parseHTML:t=>{var e;let{languageClassPrefix:n}=this.options,i=[...((e=t.firstElementChild)==null?void 0:e.classList)||[]].filter(s=>s.startsWith(n)).map(s=>s.replace(n,""))[0];return i||null},rendered:!1}}},parseHTML(){return[{tag:"pre",preserveWhitespace:"full"}]},renderHTML({node:t,HTMLAttributes:e}){return["pre",T(this.options.HTMLAttributes,e),["code",{class:t.attrs.language?this.options.languageClassPrefix+t.attrs.language:null},0]]},addCommands(){return{setCodeBlock:t=>({commands:e})=>e.setNode(this.name,t),toggleCodeBlock:t=>({commands:e})=>e.toggleNode(this.name,"paragraph",t)}},addKeyboardShortcuts(){return{"Mod-Alt-c":()=>this.editor.commands.toggleCodeBlock(),Backspace:()=>{let{empty:t,$anchor:e}=this.editor.state.selection,n=e.pos===1;return!t||e.parent.type.name!==this.name?!1:n||!e.parent.textContent.length?this.editor.commands.clearNodes():!1},Enter:({editor:t})=>{if(!this.options.exitOnTripleEnter)return!1;let{state:e}=t,{selection:n}=e,{$from:r,empty:o}=n;if(!o||r.parent.type!==this.type)return!1;let i=r.parentOffset===r.parent.nodeSize-2,s=r.parent.textContent.endsWith(`

`);return!i||!s?!1:t.chain().command(({tr:l})=>(l.delete(r.pos-2,r.pos),!0)).exitCode().run()},ArrowDown:({editor:t})=>{if(!this.options.exitOnArrowDown)return!1;let{state:e}=t,{selection:n,doc:r}=e,{$from:o,empty:i}=n;if(!i||o.parent.type!==this.type||!(o.parentOffset===o.parent.nodeSize-2))return!1;let l=o.after();return l===void 0?!1:r.nodeAt(l)?t.commands.command(({tr:c})=>(c.setSelection(E.near(r.resolve(l))),!0)):t.commands.exitCode()}}},addInputRules(){return[nr({find:x0,type:this.type,getAttributes:t=>({language:t[1]})}),nr({find:w0,type:this.type,getAttributes:t=>({language:t[1]})})]},addProseMirrorPlugins(){return[new N({key:new L("codeBlockVSCodeHandler"),props:{handlePaste:(t,e)=>{if(!e.clipboardData||this.editor.isActive(this.type.name))return!1;let n=e.clipboardData.getData("text/plain"),r=e.clipboardData.getData("vscode-editor-data"),o=r?JSON.parse(r):void 0,i=o?.mode;if(!n||!i)return!1;let{tr:s,schema:l}=t.state,a=l.text(n.replace(/\r\n?/g,`
`));return s.replaceSelectionWith(this.type.create({language:i},a)),s.selection.$from.parent.type!==this.type&&s.setSelection(A.near(s.doc.resolve(Math.max(0,s.selection.from-2)))),s.setMeta("paste",!0),t.dispatch(s),!0}}})]}}),wd=S0;var Sd=z.create({name:"customBlock",group:"block",atom:!0,defining:!0,draggable:!0,selectable:!0,isolating:!0,allowGapCursor:!0,inline:!1,addNodeView(){return({editor:t,node:e,getPos:n,HTMLAttributes:r,decorations:o,extension:i})=>{let s=document.createElement("div");s.setAttribute("data-config",e.attrs.config),s.setAttribute("data-id",e.attrs.id),s.setAttribute("data-type","customBlock");let l=document.createElement("div");if(l.className="fi-fo-rich-editor-custom-block-header fi-not-prose",s.appendChild(l),t.isEditable&&typeof e.attrs.config=="object"&&e.attrs.config!==null&&Object.keys(e.attrs.config).length>0){let c=document.createElement("div");c.className="fi-fo-rich-editor-custom-block-edit-btn-ctn",l.appendChild(c);let u=document.createElement("button");u.className="fi-icon-btn",u.type="button",u.innerHTML=i.options.editCustomBlockButtonIconHtml,u.addEventListener("click",()=>i.options.editCustomBlockUsing(e.attrs.id,e.attrs.config)),c.appendChild(u)}let a=document.createElement("p");if(a.className="fi-fo-rich-editor-custom-block-heading",a.textContent=e.attrs.label,l.appendChild(a),t.isEditable){let c=document.createElement("div");c.className="fi-fo-rich-editor-custom-block-delete-btn-ctn",l.appendChild(c);let u=document.createElement("button");u.className="fi-icon-btn",u.type="button",u.innerHTML=i.options.deleteCustomBlockButtonIconHtml,u.addEventListener("click",()=>t.chain().setNodeSelection(n()).deleteSelection().run()),c.appendChild(u)}if(e.attrs.preview){let c=document.createElement("div");c.className="fi-fo-rich-editor-custom-block-preview fi-not-prose",c.innerHTML=new TextDecoder().decode(Uint8Array.from(atob(e.attrs.preview),u=>u.charCodeAt(0))),s.appendChild(c)}return{dom:s}}},addOptions(){return{deleteCustomBlockButtonIconHtml:null,editCustomBlockButtonIconHtml:null,editCustomBlockUsing:()=>{},insertCustomBlockUsing:()=>{}}},addAttributes(){return{config:{default:null,parseHTML:t=>JSON.parse(t.getAttribute("data-config"))},id:{default:null,parseHTML:t=>t.getAttribute("data-id"),renderHTML:t=>t.id?{"data-id":t.id}:{}},label:{default:null,parseHTML:t=>t.getAttribute("data-label"),rendered:!1},preview:{default:null,parseHTML:t=>t.getAttribute("data-preview"),rendered:!1}}},parseHTML(){return[{tag:`div[data-type="${this.name}"]`}]},renderHTML({HTMLAttributes:t}){return["div",T(t)]},addKeyboardShortcuts(){return{Backspace:()=>this.editor.commands.command(({tr:t,state:e})=>{let n=!1,{selection:r}=e,{empty:o,anchor:i}=r;if(!o)return!1;let s=new le,l=0;return e.doc.nodesBetween(i-1,i,(a,c)=>{if(a.type.name===this.name)return n=!0,s=a,l=c,!1}),n})}},addProseMirrorPlugins(){let{insertCustomBlockUsing:t}=this.options;return[new N({props:{handleDrop(e,n){if(!n||(n.preventDefault(),!n.dataTransfer.getData("customBlock")))return!1;let r=n.dataTransfer.getData("customBlock");return t(r,e.posAtCoords({left:n.clientX,top:n.clientY}).pos),!1}}})]}});var yo=(t,e)=>e.view.domAtPos(t).node.offsetParent!==null,k0=(t,e,n)=>{for(let r=t.depth;r>0;r-=1){let o=t.node(r),i=e(o),s=yo(t.start(r),n);if(i&&s)return{pos:r>0?t.before(r):0,start:t.start(r),depth:r,node:o}}},kd=(t,e)=>{let{state:n,view:r,extensionManager:o}=t,{schema:i,selection:s}=n,{empty:l,$anchor:a}=s,c=!!o.extensions.find(y=>y.name==="gapCursor");if(!l||a.parent.type!==i.nodes.detailsSummary||!c||e==="right"&&a.parentOffset!==a.parent.nodeSize-2)return!1;let u=Ye(y=>y.type===i.nodes.details)(s);if(!u)return!1;let d=Sn(u.node,y=>y.type===i.nodes.detailsContent);if(!d.length||yo(u.start+d[0].pos+1,t))return!1;let h=n.doc.resolve(u.pos+u.node.nodeSize),p=se.findFrom(h,1,!1);if(!p)return!1;let{tr:m}=n,g=new se(p);return m.setSelection(g),m.scrollIntoView(),r.dispatch(m),!0},Cd=z.create({name:"details",content:"detailsSummary detailsContent",group:"block",defining:!0,isolating:!0,allowGapCursor:!1,addOptions(){return{persist:!1,openClassName:"is-open",HTMLAttributes:{}}},addAttributes(){return this.options.persist?{open:{default:!1,parseHTML:t=>t.hasAttribute("open"),renderHTML:({open:t})=>t?{open:""}:{}}}:[]},parseHTML(){return[{tag:"details"}]},renderHTML({HTMLAttributes:t}){return["details",T(this.options.HTMLAttributes,t),0]},addNodeView(){return({editor:t,getPos:e,node:n,HTMLAttributes:r})=>{let o=document.createElement("div"),i=T(this.options.HTMLAttributes,r,{"data-type":this.name});Object.entries(i).forEach(([c,u])=>o.setAttribute(c,u));let s=document.createElement("button");s.type="button",o.append(s);let l=document.createElement("div");o.append(l);let a=c=>{if(c!==void 0)if(c){if(o.classList.contains(this.options.openClassName))return;o.classList.add(this.options.openClassName)}else{if(!o.classList.contains(this.options.openClassName))return;o.classList.remove(this.options.openClassName)}else o.classList.toggle(this.options.openClassName);let u=new Event("toggleDetailsContent"),d=l.querySelector(':scope > div[data-type="detailsContent"]');d?.dispatchEvent(u)};return n.attrs.open&&setTimeout(()=>a()),s.addEventListener("click",()=>{if(a(),!this.options.persist){t.commands.focus(void 0,{scrollIntoView:!1});return}if(t.isEditable&&typeof e=="function"){let{from:c,to:u}=t.state.selection;t.chain().command(({tr:d})=>{let f=e();if(!f)return!1;let h=d.doc.nodeAt(f);return h?.type!==this.type?!1:(d.setNodeMarkup(f,void 0,{open:!h.attrs.open}),!0)}).setTextSelection({from:c,to:u}).focus(void 0,{scrollIntoView:!1}).run()}}),{dom:o,contentDOM:l,ignoreMutation(c){return c.type==="selection"?!1:!o.contains(c.target)||o===c.target},update:c=>c.type!==this.type?!1:(c.attrs.open!==void 0&&a(c.attrs.open),!0)}}},addCommands(){return{setDetails:()=>({state:t,chain:e})=>{var n;let{schema:r,selection:o}=t,{$from:i,$to:s}=o,l=i.blockRange(s);if(!l)return!1;let a=t.doc.slice(l.start,l.end);if(!r.nodes.detailsContent.contentMatch.matchFragment(a.content))return!1;let u=((n=a.toJSON())==null?void 0:n.content)||[];return e().insertContentAt({from:l.start,to:l.end},{type:this.name,content:[{type:"detailsSummary"},{type:"detailsContent",content:u}]}).setTextSelection(l.start+2).run()},unsetDetails:()=>({state:t,chain:e})=>{let{selection:n,schema:r}=t,o=Ye(y=>y.type===this.type)(n);if(!o)return!1;let i=Sn(o.node,y=>y.type===r.nodes.detailsSummary),s=Sn(o.node,y=>y.type===r.nodes.detailsContent);if(!i.length||!s.length)return!1;let l=i[0],a=s[0],c=o.pos,u=t.doc.resolve(c),d=c+o.node.nodeSize,f={from:c,to:d},h=a.node.content.toJSON()||[],p=u.parent.type.contentMatch.defaultType,g=[p?.create(null,l.node.content).toJSON(),...h];return e().insertContentAt(f,g).setTextSelection(c+1).run()}}},addKeyboardShortcuts(){return{Backspace:()=>{let{schema:t,selection:e}=this.editor.state,{empty:n,$anchor:r}=e;return!n||r.parent.type!==t.nodes.detailsSummary?!1:r.parentOffset!==0?this.editor.commands.command(({tr:o})=>{let i=r.pos-1,s=r.pos;return o.delete(i,s),!0}):this.editor.commands.unsetDetails()},Enter:({editor:t})=>{let{state:e,view:n}=t,{schema:r,selection:o}=e,{$head:i}=o;if(i.parent.type!==r.nodes.detailsSummary)return!1;let s=yo(i.after()+1,t),l=s?e.doc.nodeAt(i.after()):i.node(-2);if(!l)return!1;let a=s?0:i.indexAfter(-1),c=er(l.contentMatchAt(a));if(!c||!l.canReplaceWith(a,a,c))return!1;let u=c.createAndFill();if(!u)return!1;let d=s?i.after()+1:i.after(-1),f=e.tr.replaceWith(d,d,u),h=f.doc.resolve(d),p=E.near(h,1);return f.setSelection(p),f.scrollIntoView(),n.dispatch(f),!0},ArrowRight:({editor:t})=>kd(t,"right"),ArrowDown:({editor:t})=>kd(t,"down")}},addProseMirrorPlugins(){return[new N({key:new L("detailsSelection"),appendTransaction:(t,e,n)=>{let{editor:r,type:o}=this;if(!t.some(g=>g.selectionSet)||!e.selection.empty||!n.selection.empty||!Js(n,o.name))return;let{$from:l}=n.selection;if(yo(l.pos,r))return;let c=k0(l,g=>g.type===o,r);if(!c)return;let u=Sn(c.node,g=>g.type===n.schema.nodes.detailsSummary);if(!u.length)return;let d=u[0],h=(e.selection.from<n.selection.from?"forward":"backward")==="forward"?c.start+d.pos:c.pos+d.pos+d.node.nodeSize,p=A.create(n.doc,h);return n.tr.setSelection(p)}})]}}),Md=z.create({name:"detailsContent",content:"block+",defining:!0,selectable:!1,addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:`div[data-type="${this.name}"]`}]},renderHTML({HTMLAttributes:t}){return["div",T(this.options.HTMLAttributes,t,{"data-type":this.name}),0]},addNodeView(){return({HTMLAttributes:t})=>{let e=document.createElement("div"),n=T(this.options.HTMLAttributes,t,{"data-type":this.name,hidden:"hidden"});return Object.entries(n).forEach(([r,o])=>e.setAttribute(r,o)),e.addEventListener("toggleDetailsContent",()=>{e.toggleAttribute("hidden")}),{dom:e,contentDOM:e,ignoreMutation(r){return r.type==="selection"?!1:!e.contains(r.target)||e===r.target},update:r=>r.type===this.type}}},addKeyboardShortcuts(){return{Enter:({editor:t})=>{let{state:e,view:n}=t,{selection:r}=e,{$from:o,empty:i}=r,s=Ye(_=>_.type===this.type)(r);if(!i||!s||!s.node.childCount)return!1;let l=o.index(s.depth),{childCount:a}=s.node;if(!(a===l+1))return!1;let u=s.node.type.contentMatch.defaultType,d=u?.createAndFill();if(!d)return!1;let f=e.doc.resolve(s.pos+1),h=a-1,p=s.node.child(h),m=f.posAtIndex(h,s.depth);if(!p.eq(d))return!1;let y=o.node(-3);if(!y)return!1;let b=o.indexAfter(-3),x=er(y.contentMatchAt(b));if(!x||!y.canReplaceWith(b,b,x))return!1;let C=x.createAndFill();if(!C)return!1;let{tr:S}=e,R=o.after(-2);S.replaceWith(R,R,C);let P=S.doc.resolve(R),v=E.near(P,1);S.setSelection(v);let B=m,D=m+p.nodeSize;return S.delete(B,D),S.scrollIntoView(),n.dispatch(S),!0}}}}),vd=z.create({name:"detailsSummary",content:"text*",defining:!0,selectable:!1,isolating:!0,addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"summary"}]},renderHTML({HTMLAttributes:t}){return["summary",T(this.options.HTMLAttributes,t),0]}});var C0=z.create({name:"doc",topNode:!0,content:"block+"}),Td=C0;var M0=z.create({name:"hardBreak",addOptions(){return{keepMarks:!0,HTMLAttributes:{}}},inline:!0,group:"inline",selectable:!1,linebreakReplacement:!0,parseHTML(){return[{tag:"br"}]},renderHTML({HTMLAttributes:t}){return["br",T(this.options.HTMLAttributes,t)]},renderText(){return`
`},addCommands(){return{setHardBreak:()=>({commands:t,chain:e,state:n,editor:r})=>t.first([()=>t.exitCode(),()=>t.command(()=>{let{selection:o,storedMarks:i}=n;if(o.$from.parent.type.spec.isolating)return!1;let{keepMarks:s}=this.options,{splittableMarks:l}=r.extensionManager,a=i||o.$to.parentOffset&&o.$from.marks();return e().insertContent({type:this.name}).command(({tr:c,dispatch:u})=>{if(u&&a&&s){let d=a.filter(f=>l.includes(f.type.name));c.ensureMarks(d)}return!0}).run()})])}},addKeyboardShortcuts(){return{"Mod-Enter":()=>this.editor.commands.setHardBreak(),"Shift-Enter":()=>this.editor.commands.setHardBreak()}}}),Ad=M0;var v0=z.create({name:"heading",addOptions(){return{levels:[1,2,3,4,5,6],HTMLAttributes:{}}},content:"inline*",group:"block",defining:!0,addAttributes(){return{level:{default:1,rendered:!1}}},parseHTML(){return this.options.levels.map(t=>({tag:`h${t}`,attrs:{level:t}}))},renderHTML({node:t,HTMLAttributes:e}){return[`h${this.options.levels.includes(t.attrs.level)?t.attrs.level:this.options.levels[0]}`,T(this.options.HTMLAttributes,e),0]},addCommands(){return{setHeading:t=>({commands:e})=>this.options.levels.includes(t.level)?e.setNode(this.name,t):!1,toggleHeading:t=>({commands:e})=>this.options.levels.includes(t.level)?e.toggleNode(this.name,"paragraph",t):!1}},addKeyboardShortcuts(){return this.options.levels.reduce((t,e)=>({...t,[`Mod-Alt-${e}`]:()=>this.editor.commands.toggleHeading({level:e})}),{})},addInputRules(){return this.options.levels.map(t=>nr({find:new RegExp(`^(#{${Math.min(...this.options.levels)},${t}})\\s$`),type:this.type,getAttributes:{level:t}}))}}),Ed=v0;var T0=/(?:^|\s)(==(?!\s+==)((?:[^=]+))==(?!\s+==))$/,A0=/(?:^|\s)(==(?!\s+==)((?:[^=]+))==(?!\s+==))/g,E0=te.create({name:"highlight",addOptions(){return{multicolor:!1,HTMLAttributes:{}}},addAttributes(){return this.options.multicolor?{color:{default:null,parseHTML:t=>t.getAttribute("data-color")||t.style.backgroundColor,renderHTML:t=>t.color?{"data-color":t.color,style:`background-color: ${t.color}; color: inherit`}:{}}}:{}},parseHTML(){return[{tag:"mark"}]},renderHTML({HTMLAttributes:t}){return["mark",T(this.options.HTMLAttributes,t),0]},addCommands(){return{setHighlight:t=>({commands:e})=>e.setMark(this.name,t),toggleHighlight:t=>({commands:e})=>e.toggleMark(this.name,t),unsetHighlight:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-h":()=>this.editor.commands.toggleHighlight()}},addInputRules(){return[Ie({find:T0,type:this.type})]},addPasteRules(){return[Ce({find:A0,type:this.type})]}}),Od=E0;var O0=z.create({name:"horizontalRule",addOptions(){return{HTMLAttributes:{}}},group:"block",parseHTML(){return[{tag:"hr"}]},renderHTML({HTMLAttributes:t}){return["hr",T(this.options.HTMLAttributes,t)]},addCommands(){return{setHorizontalRule:()=>({chain:t,state:e})=>{if(!od(e,e.schema.nodes[this.name]))return!1;let{selection:n}=e,{$to:r}=n,o=t();return lo(n)?o.insertContentAt(r.pos,{type:this.name}):o.insertContent({type:this.name}),o.command(({tr:i,dispatch:s})=>{var l;if(s){let{$to:a}=i.selection,c=a.end();if(a.nodeAfter)a.nodeAfter.isTextblock?i.setSelection(A.create(i.doc,a.pos+1)):a.nodeAfter.isBlock?i.setSelection(O.create(i.doc,a.pos)):i.setSelection(A.create(i.doc,a.pos));else{let u=(l=a.parent.type.contentMatch.defaultType)==null?void 0:l.create();u&&(i.insert(c,u),i.setSelection(A.create(i.doc,c+1)))}i.scrollIntoView()}return!0}).run()}}},addInputRules(){return[fo({find:/^(?:---|—-|___\s|\*\*\*\s)$/,type:this.type})]}}),Nd=O0;var N0=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))$/,R0=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))/g,D0=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))$/,P0=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))/g,I0=te.create({name:"italic",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"em"},{tag:"i",getAttrs:t=>t.style.fontStyle!=="normal"&&null},{style:"font-style=normal",clearMark:t=>t.type.name===this.name},{style:"font-style=italic"}]},renderHTML({HTMLAttributes:t}){return["em",T(this.options.HTMLAttributes,t),0]},addCommands(){return{setItalic:()=>({commands:t})=>t.setMark(this.name),toggleItalic:()=>({commands:t})=>t.toggleMark(this.name),unsetItalic:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-i":()=>this.editor.commands.toggleItalic(),"Mod-I":()=>this.editor.commands.toggleItalic()}},addInputRules(){return[Ie({find:N0,type:this.type}),Ie({find:D0,type:this.type})]},addPasteRules(){return[Ce({find:R0,type:this.type}),Ce({find:P0,type:this.type})]}}),Rd=I0;var L0=/(?:^|\s)(!\[(.+|:?)]\((\S+)(?:(?:\s+)["'](\S+)["'])?\))$/,B0=z.create({name:"image",addOptions(){return{inline:!1,allowBase64:!1,HTMLAttributes:{}}},inline(){return this.options.inline},group(){return this.options.inline?"inline":"block"},draggable:!0,addAttributes(){return{src:{default:null},alt:{default:null},title:{default:null},width:{default:null},height:{default:null}}},parseHTML(){return[{tag:this.options.allowBase64?"img[src]":'img[src]:not([src^="data:"])'}]},renderHTML({HTMLAttributes:t}){return["img",T(this.options.HTMLAttributes,t)]},addCommands(){return{setImage:t=>({commands:e})=>e.insertContent({type:this.name,attrs:t})}},addInputRules(){return[fo({find:L0,type:this.type,getAttributes:t=>{let[,,e,n,r]=t;return{src:n,alt:e,title:r}}})]}}),Dd=B0;var Pd=Dd.extend({addAttributes(){return{...this.parent?.(),id:{default:null,parseHTML:t=>t.getAttribute("data-id"),renderHTML:t=>t.id?{"data-id":t.id}:{}}}}});var Id=z.create({name:"lead",group:"block",content:"block+",addOptions(){return{HTMLAttributes:{class:"lead"}}},parseHTML(){return[{tag:"div",getAttrs:t=>t.classList.contains("lead")}]},renderHTML({HTMLAttributes:t}){return["div",T(this.options.HTMLAttributes,t),0]},addCommands(){return{toggleLead:()=>({commands:t})=>t.toggleWrap(this.name)}}});var z0="aaa1rp3bb0ott3vie4c1le2ogado5udhabi7c0ademy5centure6ountant0s9o1tor4d0s1ult4e0g1ro2tna4f0l1rica5g0akhan5ency5i0g1rbus3force5tel5kdn3l0ibaba4pay4lfinanz6state5y2sace3tom5m0azon4ericanexpress7family11x2fam3ica3sterdam8nalytics7droid5quan4z2o0l2partments8p0le4q0uarelle8r0ab1mco4chi3my2pa2t0e3s0da2ia2sociates9t0hleta5torney7u0ction5di0ble3o3spost5thor3o0s4w0s2x0a2z0ure5ba0by2idu3namex4d1k2r0celona5laycard4s5efoot5gains6seball5ketball8uhaus5yern5b0c1t1va3cg1n2d1e0ats2uty4er2rlin4st0buy5t2f1g1h0arti5i0ble3d1ke2ng0o3o1z2j1lack0friday9ockbuster8g1omberg7ue3m0s1w2n0pparibas9o0ats3ehringer8fa2m1nd2o0k0ing5sch2tik2on4t1utique6x2r0adesco6idgestone9oadway5ker3ther5ussels7s1t1uild0ers6siness6y1zz3v1w1y1z0h3ca0b1fe2l0l1vinklein9m0era3p2non3petown5ital0one8r0avan4ds2e0er0s4s2sa1e1h1ino4t0ering5holic7ba1n1re3c1d1enter4o1rn3f0a1d2g1h0anel2nel4rity4se2t2eap3intai5ristmas6ome4urch5i0priani6rcle4sco3tadel4i0c2y3k1l0aims4eaning6ick2nic1que6othing5ud3ub0med6m1n1o0ach3des3ffee4llege4ogne5m0mbank4unity6pany2re3uter5sec4ndos3struction8ulting7tact3ractors9oking4l1p2rsica5untry4pon0s4rses6pa2r0edit0card4union9icket5own3s1uise0s6u0isinella9v1w1x1y0mru3ou3z2dad1nce3ta1e1ing3sun4y2clk3ds2e0al0er2s3gree4livery5l1oitte5ta3mocrat6ntal2ist5si0gn4v2hl2iamonds6et2gital5rect0ory7scount3ver5h2y2j1k1m1np2o0cs1tor4g1mains5t1wnload7rive4tv2ubai3nlop4pont4rban5vag2r2z2earth3t2c0o2deka3u0cation8e1g1mail3erck5nergy4gineer0ing9terprises10pson4quipment8r0icsson6ni3s0q1tate5t1u0rovision8s2vents5xchange6pert3osed4ress5traspace10fage2il1rwinds6th3mily4n0s2rm0ers5shion4t3edex3edback6rrari3ero6i0delity5o2lm2nal1nce1ial7re0stone6mdale6sh0ing5t0ness6j1k1lickr3ghts4r2orist4wers5y2m1o0o0d1tball6rd1ex2sale4um3undation8x2r0ee1senius7l1ogans4ntier7tr2ujitsu5n0d2rniture7tbol5yi3ga0l0lery3o1up4me0s3p1rden4y2b0iz3d0n2e0a1nt0ing5orge5f1g0ee3h1i0ft0s3ves2ing5l0ass3e1obal2o4m0ail3bh2o1x2n1odaddy5ld0point6f2o0dyear5g0le4p1t1v2p1q1r0ainger5phics5tis4een3ipe3ocery4up4s1t1u0cci3ge2ide2tars5ru3w1y2hair2mburg5ngout5us3bo2dfc0bank7ealth0care8lp1sinki6re1mes5iphop4samitsu7tachi5v2k0t2m1n1ockey4ldings5iday5medepot5goods5s0ense7nda3rse3spital5t0ing5t0els3mail5use3w2r1sbc3t1u0ghes5yatt3undai7ibm2cbc2e1u2d1e0ee3fm2kano4l1m0amat4db2mo0bilien9n0c1dustries8finiti5o2g1k1stitute6urance4e4t0ernational10uit4vestments10o1piranga7q1r0ish4s0maili5t0anbul7t0au2v3jaguar4va3cb2e0ep2tzt3welry6io2ll2m0p2nj2o0bs1urg4t1y2p0morgan6rs3uegos4niper7kaufen5ddi3e0rryhotels6properties14fh2g1h1i0a1ds2m1ndle4tchen5wi3m1n1oeln3matsu5sher5p0mg2n2r0d1ed3uokgroup8w1y0oto4z2la0caixa5mborghini8er3nd0rover6xess5salle5t0ino3robe5w0yer5b1c1ds2ease3clerc5frak4gal2o2xus4gbt3i0dl2fe0insurance9style7ghting6ke2lly3mited4o2ncoln4k2ve1ing5k1lc1p2oan0s3cker3us3l1ndon4tte1o3ve3pl0financial11r1s1t0d0a3u0ndbeck6xe1ury5v1y2ma0drid4if1son4keup4n0agement7go3p1rket0ing3s4riott5shalls7ttel5ba2c0kinsey7d1e0d0ia3et2lbourne7me1orial6n0u2rckmsd7g1h1iami3crosoft7l1ni1t2t0subishi9k1l0b1s2m0a2n1o0bi0le4da2e1i1m1nash3ey2ster5rmon3tgage6scow4to0rcycles9v0ie4p1q1r1s0d2t0n1r2u0seum3ic4v1w1x1y1z2na0b1goya4me2vy3ba2c1e0c1t0bank4flix4work5ustar5w0s2xt0direct7us4f0l2g0o2hk2i0co2ke1on3nja3ssan1y5l1o0kia3rton4w0ruz3tv4p1r0a1w2tt2u1yc2z2obi1server7ffice5kinawa6layan0group9lo3m0ega4ne1g1l0ine5oo2pen3racle3nge4g0anic5igins6saka4tsuka4t2vh3pa0ge2nasonic7ris2s1tners4s1y3y2ccw3e0t2f0izer5g1h0armacy6d1ilips5one2to0graphy6s4ysio5ics1tet2ures6d1n0g1k2oneer5zza4k1l0ace2y0station9umbing5s3m1n0c2ohl2ker3litie5rn2st3r0america6xi3ess3ime3o0d0uctions8f1gressive8mo2perties3y5tection8u0dential9s1t1ub2w0c2y2qa1pon3uebec3st5racing4dio4e0ad1lestate6tor2y4cipes5d0stone5umbrella9hab3ise0n3t2liance6n0t0als5pair3ort3ublican8st0aurant8view0s5xroth6ich0ardli6oh3l1o1p2o0cks3deo3gers4om3s0vp3u0gby3hr2n2w0e2yukyu6sa0arland6fe0ty4kura4le1on3msclub4ung5ndvik0coromant12ofi4p1rl2s1ve2xo3b0i1s2c0b1haeffler7midt4olarships8ol3ule3warz5ience5ot3d1e0arch3t2cure1ity6ek2lect4ner3rvices6ven3w1x0y3fr2g1h0angrila6rp3ell3ia1ksha5oes2p0ping5uji3w3i0lk2na1gles5te3j1k0i0n2y0pe4l0ing4m0art3ile4n0cf3o0ccer3ial4ftbank4ware6hu2lar2utions7ng1y2y2pa0ce3ort2t3r0l2s1t0ada2ples4r1tebank4farm7c0group6ockholm6rage3e3ream4udio2y3yle4u0cks3pplies3y2ort5rf1gery5zuki5v1watch4iss4x1y0dney4stems6z2tab1ipei4lk2obao4rget4tamotors6r2too4x0i3c0i2d0k2eam2ch0nology8l1masek5nnis4va3f1g1h0d1eater2re6iaa2ckets5enda4ps2res2ol4j0maxx4x2k0maxx5l1m0all4n1o0day3kyo3ols3p1ray3shiba5tal3urs3wn2yota3s3r0ade1ing4ining5vel0ers0insurance16ust3v2t1ube2i1nes3shu4v0s2w1z2ua1bank3s2g1k1nicom3versity8o2ol2ps2s1y1z2va0cations7na1guard7c1e0gas3ntures6risign5m\xF6gensberater2ung14sicherung10t2g1i0ajes4deo3g1king4llas4n1p1rgin4sa1ion4va1o3laanderen9n1odka3lvo3te1ing3o2yage5u2wales2mart4ter4ng0gou5tch0es6eather0channel12bcam3er2site5d0ding5ibo2r3f1hoswho6ien2ki2lliamhill9n0dows4e1ners6me2olterskluwer11odside6rk0s2ld3w2s1tc1f3xbox3erox4ihuan4n2xx2yz3yachts4hoo3maxun5ndex5e1odobashi7ga2kohama6u0tube6t1un3za0ppos4ra3ero3ip2m1one3uerich6w2",H0="\u03B5\u03BB1\u03C52\u0431\u04331\u0435\u043B3\u0434\u0435\u0442\u04384\u0435\u044E2\u043A\u0430\u0442\u043E\u043B\u0438\u043A6\u043E\u043C3\u043C\u043A\u04342\u043E\u043D1\u0441\u043A\u0432\u04306\u043E\u043D\u043B\u0430\u0439\u043D5\u0440\u04333\u0440\u0443\u04412\u04442\u0441\u0430\u0439\u04423\u0440\u04313\u0443\u043A\u04403\u049B\u0430\u04373\u0570\u0561\u05753\u05D9\u05E9\u05E8\u05D0\u05DC5\u05E7\u05D5\u05DD3\u0627\u0628\u0648\u0638\u0628\u064A5\u0631\u0627\u0645\u0643\u06485\u0644\u0627\u0631\u062F\u06464\u0628\u062D\u0631\u064A\u06465\u062C\u0632\u0627\u0626\u06315\u0633\u0639\u0648\u062F\u064A\u06296\u0639\u0644\u064A\u0627\u06465\u0645\u063A\u0631\u06285\u0645\u0627\u0631\u0627\u062A5\u06CC\u0631\u0627\u06465\u0628\u0627\u0631\u062A2\u0632\u0627\u06314\u064A\u062A\u06433\u06BE\u0627\u0631\u062A5\u062A\u0648\u0646\u06334\u0633\u0648\u062F\u0627\u06463\u0631\u064A\u06295\u0634\u0628\u0643\u06294\u0639\u0631\u0627\u06422\u06282\u0645\u0627\u06464\u0641\u0644\u0633\u0637\u064A\u06466\u0642\u0637\u06313\u0643\u0627\u062B\u0648\u0644\u064A\u06436\u0648\u06453\u0645\u0635\u06312\u0644\u064A\u0633\u064A\u06275\u0648\u0631\u064A\u062A\u0627\u0646\u064A\u06277\u0642\u06394\u0647\u0645\u0631\u0627\u06475\u067E\u0627\u06A9\u0633\u062A\u0627\u06467\u0680\u0627\u0631\u062A4\u0915\u0949\u092E3\u0928\u0947\u091F3\u092D\u093E\u0930\u09240\u092E\u094D3\u094B\u09245\u0938\u0902\u0917\u0920\u09285\u09AC\u09BE\u0982\u09B2\u09BE5\u09AD\u09BE\u09B0\u09A42\u09F0\u09A44\u0A2D\u0A3E\u0A30\u0A244\u0AAD\u0ABE\u0AB0\u0AA44\u0B2D\u0B3E\u0B30\u0B244\u0B87\u0BA8\u0BCD\u0BA4\u0BBF\u0BAF\u0BBE6\u0BB2\u0B99\u0BCD\u0B95\u0BC86\u0B9A\u0BBF\u0B99\u0BCD\u0B95\u0BAA\u0BCD\u0BAA\u0BC2\u0BB0\u0BCD11\u0C2D\u0C3E\u0C30\u0C24\u0C4D5\u0CAD\u0CBE\u0CB0\u0CA44\u0D2D\u0D3E\u0D30\u0D24\u0D025\u0DBD\u0D82\u0D9A\u0DCF4\u0E04\u0E2D\u0E213\u0E44\u0E17\u0E223\u0EA5\u0EB2\u0EA73\u10D2\u10D42\u307F\u3093\u306A3\u30A2\u30DE\u30BE\u30F34\u30AF\u30E9\u30A6\u30C94\u30B0\u30FC\u30B0\u30EB4\u30B3\u30E02\u30B9\u30C8\u30A23\u30BB\u30FC\u30EB3\u30D5\u30A1\u30C3\u30B7\u30E7\u30F36\u30DD\u30A4\u30F3\u30C84\u4E16\u754C2\u4E2D\u4FE11\u56FD1\u570B1\u6587\u7F513\u4E9A\u9A6C\u900A3\u4F01\u4E1A2\u4F5B\u5C712\u4FE1\u606F2\u5065\u5EB72\u516B\u53662\u516C\u53F81\u76CA2\u53F0\u6E7E1\u70632\u5546\u57CE1\u5E971\u68072\u5609\u91CC0\u5927\u9152\u5E975\u5728\u7EBF2\u5927\u62FF2\u5929\u4E3B\u65593\u5A31\u4E502\u5BB6\u96FB2\u5E7F\u4E1C2\u5FAE\u535A2\u6148\u55842\u6211\u7231\u4F603\u624B\u673A2\u62DB\u80582\u653F\u52A11\u5E9C2\u65B0\u52A0\u57612\u95FB2\u65F6\u5C1A2\u66F8\u7C4D2\u673A\u67842\u6DE1\u9A6C\u95213\u6E38\u620F2\u6FB3\u95802\u70B9\u770B2\u79FB\u52A82\u7EC4\u7EC7\u673A\u67844\u7F51\u57401\u5E971\u7AD91\u7EDC2\u8054\u901A2\u8C37\u6B4C2\u8D2D\u72692\u901A\u8CA92\u96C6\u56E22\u96FB\u8A0A\u76C8\u79D14\u98DE\u5229\u6D663\u98DF\u54C12\u9910\u53852\u9999\u683C\u91CC\u62C93\u6E2F2\uB2F7\uB1371\uCEF42\uC0BC\uC1312\uD55C\uAD6D2",Mn=(t,e)=>{for(let n in e)t[n]=e[n];return t},ul="numeric",dl="ascii",fl="alpha",ir="asciinumeric",or="alphanumeric",hl="domain",Vd="emoji",F0="scheme",$0="slashscheme",ol="whitespace";function V0(t,e){return t in e||(e[t]=[]),e[t]}function Xt(t,e,n){e[ul]&&(e[ir]=!0,e[or]=!0),e[dl]&&(e[ir]=!0,e[fl]=!0),e[ir]&&(e[or]=!0),e[fl]&&(e[or]=!0),e[or]&&(e[hl]=!0),e[Vd]&&(e[hl]=!0);for(let r in e){let o=V0(r,n);o.indexOf(t)<0&&o.push(t)}}function W0(t,e){let n={};for(let r in e)e[r].indexOf(t)>=0&&(n[r]=!0);return n}function Me(t=null){this.j={},this.jr=[],this.jd=null,this.t=t}Me.groups={};Me.prototype={accepts(){return!!this.t},go(t){let e=this,n=e.j[t];if(n)return n;for(let r=0;r<e.jr.length;r++){let o=e.jr[r][0],i=e.jr[r][1];if(i&&o.test(t))return i}return e.jd},has(t,e=!1){return e?t in this.j:!!this.go(t)},ta(t,e,n,r){for(let o=0;o<t.length;o++)this.tt(t[o],e,n,r)},tr(t,e,n,r){r=r||Me.groups;let o;return e&&e.j?o=e:(o=new Me(e),n&&r&&Xt(e,n,r)),this.jr.push([t,o]),o},ts(t,e,n,r){let o=this,i=t.length;if(!i)return o;for(let s=0;s<i-1;s++)o=o.tt(t[s]);return o.tt(t[i-1],e,n,r)},tt(t,e,n,r){r=r||Me.groups;let o=this;if(e&&e.j)return o.j[t]=e,e;let i=e,s,l=o.go(t);if(l?(s=new Me,Mn(s.j,l.j),s.jr.push.apply(s.jr,l.jr),s.jd=l.jd,s.t=l.t):s=new Me,i){if(r)if(s.t&&typeof s.t=="string"){let a=Mn(W0(s.t,r),n);Xt(i,a,r)}else n&&Xt(i,n,r);s.t=i}return o.j[t]=s,s}};var F=(t,e,n,r,o)=>t.ta(e,n,r,o),Z=(t,e,n,r,o)=>t.tr(e,n,r,o),Ld=(t,e,n,r,o)=>t.ts(e,n,r,o),k=(t,e,n,r,o)=>t.tt(e,n,r,o),bt="WORD",pl="UWORD",Wd="ASCIINUMERICAL",_d="ALPHANUMERICAL",dr="LOCALHOST",ml="TLD",gl="UTLD",So="SCHEME",Cn="SLASH_SCHEME",bl="NUM",yl="WS",xl="NL",sr="OPENBRACE",lr="CLOSEBRACE",ko="OPENBRACKET",Co="CLOSEBRACKET",Mo="OPENPAREN",vo="CLOSEPAREN",To="OPENANGLEBRACKET",Ao="CLOSEANGLEBRACKET",Eo="FULLWIDTHLEFTPAREN",Oo="FULLWIDTHRIGHTPAREN",No="LEFTCORNERBRACKET",Ro="RIGHTCORNERBRACKET",Do="LEFTWHITECORNERBRACKET",Po="RIGHTWHITECORNERBRACKET",Io="FULLWIDTHLESSTHAN",Lo="FULLWIDTHGREATERTHAN",Bo="AMPERSAND",zo="APOSTROPHE",Ho="ASTERISK",Rt="AT",Fo="BACKSLASH",$o="BACKTICK",Vo="CARET",Dt="COLON",wl="COMMA",Wo="DOLLAR",et="DOT",_o="EQUALS",Sl="EXCLAMATION",Be="HYPHEN",ar="PERCENT",jo="PIPE",Ko="PLUS",Uo="POUND",cr="QUERY",kl="QUOTE",jd="FULLWIDTHMIDDLEDOT",Cl="SEMI",tt="SLASH",ur="TILDE",Jo="UNDERSCORE",Kd="EMOJI",qo="SYM",Ud=Object.freeze({__proto__:null,ALPHANUMERICAL:_d,AMPERSAND:Bo,APOSTROPHE:zo,ASCIINUMERICAL:Wd,ASTERISK:Ho,AT:Rt,BACKSLASH:Fo,BACKTICK:$o,CARET:Vo,CLOSEANGLEBRACKET:Ao,CLOSEBRACE:lr,CLOSEBRACKET:Co,CLOSEPAREN:vo,COLON:Dt,COMMA:wl,DOLLAR:Wo,DOT:et,EMOJI:Kd,EQUALS:_o,EXCLAMATION:Sl,FULLWIDTHGREATERTHAN:Lo,FULLWIDTHLEFTPAREN:Eo,FULLWIDTHLESSTHAN:Io,FULLWIDTHMIDDLEDOT:jd,FULLWIDTHRIGHTPAREN:Oo,HYPHEN:Be,LEFTCORNERBRACKET:No,LEFTWHITECORNERBRACKET:Do,LOCALHOST:dr,NL:xl,NUM:bl,OPENANGLEBRACKET:To,OPENBRACE:sr,OPENBRACKET:ko,OPENPAREN:Mo,PERCENT:ar,PIPE:jo,PLUS:Ko,POUND:Uo,QUERY:cr,QUOTE:kl,RIGHTCORNERBRACKET:Ro,RIGHTWHITECORNERBRACKET:Po,SCHEME:So,SEMI:Cl,SLASH:tt,SLASH_SCHEME:Cn,SYM:qo,TILDE:ur,TLD:ml,UNDERSCORE:Jo,UTLD:gl,UWORD:pl,WORD:bt,WS:yl}),gt=/[a-z]/,rr=/\p{L}/u,il=/\p{Emoji}/u;var yt=/\d/,sl=/\s/;var Bd="\r",ll=`
`,_0="\uFE0F",j0="\u200D",al="\uFFFC",bo=null,xo=null;function K0(t=[]){let e={};Me.groups=e;let n=new Me;bo==null&&(bo=zd(z0)),xo==null&&(xo=zd(H0)),k(n,"'",zo),k(n,"{",sr),k(n,"}",lr),k(n,"[",ko),k(n,"]",Co),k(n,"(",Mo),k(n,")",vo),k(n,"<",To),k(n,">",Ao),k(n,"\uFF08",Eo),k(n,"\uFF09",Oo),k(n,"\u300C",No),k(n,"\u300D",Ro),k(n,"\u300E",Do),k(n,"\u300F",Po),k(n,"\uFF1C",Io),k(n,"\uFF1E",Lo),k(n,"&",Bo),k(n,"*",Ho),k(n,"@",Rt),k(n,"`",$o),k(n,"^",Vo),k(n,":",Dt),k(n,",",wl),k(n,"$",Wo),k(n,".",et),k(n,"=",_o),k(n,"!",Sl),k(n,"-",Be),k(n,"%",ar),k(n,"|",jo),k(n,"+",Ko),k(n,"#",Uo),k(n,"?",cr),k(n,'"',kl),k(n,"/",tt),k(n,";",Cl),k(n,"~",ur),k(n,"_",Jo),k(n,"\\",Fo),k(n,"\u30FB",jd);let r=Z(n,yt,bl,{[ul]:!0});Z(r,yt,r);let o=Z(r,gt,Wd,{[ir]:!0}),i=Z(r,rr,_d,{[or]:!0}),s=Z(n,gt,bt,{[dl]:!0});Z(s,yt,o),Z(s,gt,s),Z(o,yt,o),Z(o,gt,o);let l=Z(n,rr,pl,{[fl]:!0});Z(l,gt),Z(l,yt,i),Z(l,rr,l),Z(i,yt,i),Z(i,gt),Z(i,rr,i);let a=k(n,ll,xl,{[ol]:!0}),c=k(n,Bd,yl,{[ol]:!0}),u=Z(n,sl,yl,{[ol]:!0});k(n,al,u),k(c,ll,a),k(c,al,u),Z(c,sl,u),k(u,Bd),k(u,ll),Z(u,sl,u),k(u,al,u);let d=Z(n,il,Kd,{[Vd]:!0});k(d,"#"),Z(d,il,d),k(d,_0,d);let f=k(d,j0);k(f,"#"),Z(f,il,d);let h=[[gt,s],[yt,o]],p=[[gt,null],[rr,l],[yt,i]];for(let m=0;m<bo.length;m++)Nt(n,bo[m],ml,bt,h);for(let m=0;m<xo.length;m++)Nt(n,xo[m],gl,pl,p);Xt(ml,{tld:!0,ascii:!0},e),Xt(gl,{utld:!0,alpha:!0},e),Nt(n,"file",So,bt,h),Nt(n,"mailto",So,bt,h),Nt(n,"http",Cn,bt,h),Nt(n,"https",Cn,bt,h),Nt(n,"ftp",Cn,bt,h),Nt(n,"ftps",Cn,bt,h),Xt(So,{scheme:!0,ascii:!0},e),Xt(Cn,{slashscheme:!0,ascii:!0},e),t=t.sort((m,g)=>m[0]>g[0]?1:-1);for(let m=0;m<t.length;m++){let g=t[m][0],b=t[m][1]?{[F0]:!0}:{[$0]:!0};g.indexOf("-")>=0?b[hl]=!0:gt.test(g)?yt.test(g)?b[ir]=!0:b[dl]=!0:b[ul]=!0,Ld(n,g,g,b)}return Ld(n,"localhost",dr,{ascii:!0}),n.jd=new Me(qo),{start:n,tokens:Mn({groups:e},Ud)}}function Jd(t,e){let n=U0(e.replace(/[A-Z]/g,l=>l.toLowerCase())),r=n.length,o=[],i=0,s=0;for(;s<r;){let l=t,a=null,c=0,u=null,d=-1,f=-1;for(;s<r&&(a=l.go(n[s]));)l=a,l.accepts()?(d=0,f=0,u=l):d>=0&&(d+=n[s].length,f++),c+=n[s].length,i+=n[s].length,s++;i-=d,s-=f,c-=d,o.push({t:u.t,v:e.slice(i-c,i),s:i-c,e:i})}return o}function U0(t){let e=[],n=t.length,r=0;for(;r<n;){let o=t.charCodeAt(r),i,s=o<55296||o>56319||r+1===n||(i=t.charCodeAt(r+1))<56320||i>57343?t[r]:t.slice(r,r+2);e.push(s),r+=s.length}return e}function Nt(t,e,n,r,o){let i,s=e.length;for(let l=0;l<s-1;l++){let a=e[l];t.j[a]?i=t.j[a]:(i=new Me(r),i.jr=o.slice(),t.j[a]=i),t=i}return i=new Me(n),i.jr=o.slice(),t.j[e[s-1]]=i,i}function zd(t){let e=[],n=[],r=0,o="0123456789";for(;r<t.length;){let i=0;for(;o.indexOf(t[r+i])>=0;)i++;if(i>0){e.push(n.join(""));for(let s=parseInt(t.substring(r,r+i),10);s>0;s--)n.pop();r+=i}else n.push(t[r]),r++}return e}var fr={defaultProtocol:"http",events:null,format:Hd,formatHref:Hd,nl2br:!1,tagName:"a",target:null,rel:null,validate:!0,truncate:1/0,className:null,attributes:null,ignoreTags:[],render:null};function Ml(t,e=null){let n=Mn({},fr);t&&(n=Mn(n,t instanceof Ml?t.o:t));let r=n.ignoreTags,o=[];for(let i=0;i<r.length;i++)o.push(r[i].toUpperCase());this.o=n,e&&(this.defaultRender=e),this.ignoreTags=o}Ml.prototype={o:fr,ignoreTags:[],defaultRender(t){return t},check(t){return this.get("validate",t.toString(),t)},get(t,e,n){let r=e!=null,o=this.o[t];return o&&(typeof o=="object"?(o=n.t in o?o[n.t]:fr[t],typeof o=="function"&&r&&(o=o(e,n))):typeof o=="function"&&r&&(o=o(e,n.t,n)),o)},getObj(t,e,n){let r=this.o[t];return typeof r=="function"&&e!=null&&(r=r(e,n.t,n)),r},render(t){let e=t.render(this);return(this.get("render",null,t)||this.defaultRender)(e,t.t,t)}};function Hd(t){return t}function qd(t,e){this.t="token",this.v=t,this.tk=e}qd.prototype={isLink:!1,toString(){return this.v},toHref(t){return this.toString()},toFormattedString(t){let e=this.toString(),n=t.get("truncate",e,this),r=t.get("format",e,this);return n&&r.length>n?r.substring(0,n)+"\u2026":r},toFormattedHref(t){return t.get("formatHref",this.toHref(t.get("defaultProtocol")),this)},startIndex(){return this.tk[0].s},endIndex(){return this.tk[this.tk.length-1].e},toObject(t=fr.defaultProtocol){return{type:this.t,value:this.toString(),isLink:this.isLink,href:this.toHref(t),start:this.startIndex(),end:this.endIndex()}},toFormattedObject(t){return{type:this.t,value:this.toFormattedString(t),isLink:this.isLink,href:this.toFormattedHref(t),start:this.startIndex(),end:this.endIndex()}},validate(t){return t.get("validate",this.toString(),this)},render(t){let e=this,n=this.toHref(t.get("defaultProtocol")),r=t.get("formatHref",n,this),o=t.get("tagName",n,e),i=this.toFormattedString(t),s={},l=t.get("className",n,e),a=t.get("target",n,e),c=t.get("rel",n,e),u=t.getObj("attributes",n,e),d=t.getObj("events",n,e);return s.href=r,l&&(s.class=l),a&&(s.target=a),c&&(s.rel=c),u&&Mn(s,u),{tagName:o,attributes:s,content:i,eventListeners:d}}};function Go(t,e){class n extends qd{constructor(o,i){super(o,i),this.t=t}}for(let r in e)n.prototype[r]=e[r];return n.t=t,n}var Fd=Go("email",{isLink:!0,toHref(){return"mailto:"+this.toString()}}),$d=Go("text"),J0=Go("nl"),wo=Go("url",{isLink:!0,toHref(t=fr.defaultProtocol){return this.hasProtocol()?this.v:`${t}://${this.v}`},hasProtocol(){let t=this.tk;return t.length>=2&&t[0].t!==dr&&t[1].t===Dt}});var Le=t=>new Me(t);function q0({groups:t}){let e=t.domain.concat([Bo,Ho,Rt,Fo,$o,Vo,Wo,_o,Be,bl,ar,jo,Ko,Uo,tt,qo,ur,Jo]),n=[zo,Dt,wl,et,Sl,ar,cr,kl,Cl,To,Ao,sr,lr,Co,ko,Mo,vo,Eo,Oo,No,Ro,Do,Po,Io,Lo],r=[Bo,zo,Ho,Fo,$o,Vo,Wo,_o,Be,sr,lr,ar,jo,Ko,Uo,cr,tt,qo,ur,Jo],o=Le(),i=k(o,ur);F(i,r,i),F(i,t.domain,i);let s=Le(),l=Le(),a=Le();F(o,t.domain,s),F(o,t.scheme,l),F(o,t.slashscheme,a),F(s,r,i),F(s,t.domain,s);let c=k(s,Rt);k(i,Rt,c),k(l,Rt,c),k(a,Rt,c);let u=k(i,et);F(u,r,i),F(u,t.domain,i);let d=Le();F(c,t.domain,d),F(d,t.domain,d);let f=k(d,et);F(f,t.domain,d);let h=Le(Fd);F(f,t.tld,h),F(f,t.utld,h),k(c,dr,h);let p=k(d,Be);k(p,Be,p),F(p,t.domain,d),F(h,t.domain,d),k(h,et,f),k(h,Be,p);let m=k(h,Dt);F(m,t.numeric,Fd);let g=k(s,Be),y=k(s,et);k(g,Be,g),F(g,t.domain,s),F(y,r,i),F(y,t.domain,s);let b=Le(wo);F(y,t.tld,b),F(y,t.utld,b),F(b,t.domain,s),F(b,r,i),k(b,et,y),k(b,Be,g),k(b,Rt,c);let x=k(b,Dt),C=Le(wo);F(x,t.numeric,C);let S=Le(wo),R=Le();F(S,e,S),F(S,n,R),F(R,e,S),F(R,n,R),k(b,tt,S),k(C,tt,S);let P=k(l,Dt),v=k(a,Dt),B=k(v,tt),D=k(B,tt);F(l,t.domain,s),k(l,et,y),k(l,Be,g),F(a,t.domain,s),k(a,et,y),k(a,Be,g),F(P,t.domain,S),k(P,tt,S),k(P,cr,S),F(D,t.domain,S),F(D,e,S),k(D,tt,S);let _=[[sr,lr],[ko,Co],[Mo,vo],[To,Ao],[Eo,Oo],[No,Ro],[Do,Po],[Io,Lo]];for(let H=0;H<_.length;H++){let[j,U]=_[H],oe=k(S,j);k(R,j,oe),k(oe,U,S);let X=Le(wo);F(oe,e,X);let G=Le();F(oe,n),F(X,e,X),F(X,n,G),F(G,e,X),F(G,n,G),k(X,U,S),k(G,U,S)}return k(o,dr,b),k(o,xl,J0),{start:o,tokens:Ud}}function G0(t,e,n){let r=n.length,o=0,i=[],s=[];for(;o<r;){let l=t,a=null,c=null,u=0,d=null,f=-1;for(;o<r&&!(a=l.go(n[o].t));)s.push(n[o++]);for(;o<r&&(c=a||l.go(n[o].t));)a=null,l=c,l.accepts()?(f=0,d=l):f>=0&&f++,o++,u++;if(f<0)o-=u,o<r&&(s.push(n[o]),o++);else{s.length>0&&(i.push(cl($d,e,s)),s=[]),o-=f,u-=f;let h=d.t,p=n.slice(o-u,o);i.push(cl(h,e,p))}}return s.length>0&&i.push(cl($d,e,s)),i}function cl(t,e,n){let r=n[0].s,o=n[n.length-1].e,i=e.slice(r,o);return new t(i,n)}var Y0=typeof console<"u"&&console&&console.warn||(()=>{}),X0="until manual call of linkify.init(). Register all schemes and plugins before invoking linkify the first time.",q={scanner:null,parser:null,tokenQueue:[],pluginQueue:[],customSchemes:[],initialized:!1};function Gd(){return Me.groups={},q.scanner=null,q.parser=null,q.tokenQueue=[],q.pluginQueue=[],q.customSchemes=[],q.initialized=!1,q}function vl(t,e=!1){if(q.initialized&&Y0(`linkifyjs: already initialized - will not register custom scheme "${t}" ${X0}`),!/^[0-9a-z]+(-[0-9a-z]+)*$/.test(t))throw new Error(`linkifyjs: incorrect scheme format.
1. Must only contain digits, lowercase ASCII letters or "-"
2. Cannot start or end with "-"
3. "-" cannot repeat`);q.customSchemes.push([t,e])}function Q0(){q.scanner=K0(q.customSchemes);for(let t=0;t<q.tokenQueue.length;t++)q.tokenQueue[t][1]({scanner:q.scanner});q.parser=q0(q.scanner.tokens);for(let t=0;t<q.pluginQueue.length;t++)q.pluginQueue[t][1]({scanner:q.scanner,parser:q.parser});return q.initialized=!0,q}function Yo(t){return q.initialized||Q0(),G0(q.parser.start,t,Jd(q.scanner.start,t))}Yo.scan=Jd;function Xo(t,e=null,n=null){if(e&&typeof e=="object"){if(n)throw Error(`linkifyjs: Invalid link type ${e}; must be a string`);n=e,e=null}let r=new Ml(n),o=Yo(t),i=[];for(let s=0;s<o.length;s++){let l=o[s];l.isLink&&(!e||l.t===e)&&r.check(l)&&i.push(l.toFormattedObject(r))}return i}var Tl="[\0- \xA0\u1680\u180E\u2000-\u2029\u205F\u3000]",Z0=new RegExp(Tl),eb=new RegExp(`${Tl}$`),tb=new RegExp(Tl,"g");function nb(t){return t.length===1?t[0].isLink:t.length===3&&t[1].isLink?["()","[]"].includes(t[0].value+t[2].value):!1}function rb(t){return new N({key:new L("autolink"),appendTransaction:(e,n,r)=>{let o=e.some(c=>c.docChanged)&&!n.doc.eq(r.doc),i=e.some(c=>c.getMeta("preventAutolink"));if(!o||i)return;let{tr:s}=r,l=Hs(n.doc,[...e]);if(js(l).forEach(({newRange:c})=>{let u=Ou(r.doc,c,h=>h.isTextblock),d,f;if(u.length>1)d=u[0],f=r.doc.textBetween(d.pos,d.pos+d.node.nodeSize,void 0," ");else if(u.length){let h=r.doc.textBetween(c.from,c.to," "," ");if(!eb.test(h))return;d=u[0],f=r.doc.textBetween(d.pos,c.to,void 0," ")}if(d&&f){let h=f.split(Z0).filter(Boolean);if(h.length<=0)return!1;let p=h[h.length-1],m=d.pos+f.lastIndexOf(p);if(!p)return!1;let g=Yo(p).map(y=>y.toObject(t.defaultProtocol));if(!nb(g))return!1;g.filter(y=>y.isLink).map(y=>({...y,from:m+y.start+1,to:m+y.end+1})).filter(y=>r.schema.marks.code?!r.doc.rangeHasMark(y.from,y.to,r.schema.marks.code):!0).filter(y=>t.validate(y.value)).filter(y=>t.shouldAutoLink(y.value)).forEach(y=>{so(y.from,y.to,r.doc).some(b=>b.mark.type===t.type)||s.addMark(y.from,y.to,t.type.create({href:y.href}))})}}),!!s.steps.length)return s}})}function ob(t){return new N({key:new L("handleClickLink"),props:{handleClick:(e,n,r)=>{var o,i;if(r.button!==0||!e.editable)return!1;let s=null;if(r.target instanceof HTMLAnchorElement)s=r.target;else{let u=r.target,d=[];for(;u.nodeName!=="DIV";)d.push(u),u=u.parentNode;s=d.find(f=>f.nodeName==="A")}if(!s)return!1;let l=_s(e.state,t.type.name),a=(o=s?.href)!=null?o:l.href,c=(i=s?.target)!=null?i:l.target;return t.enableClickSelection&&t.editor.commands.extendMarkRange(t.type.name),s&&a?(window.open(a,c),!0):!1}}})}function ib(t){return new N({key:new L("handlePasteLink"),props:{handlePaste:(e,n,r)=>{let{state:o}=e,{selection:i}=o,{empty:s}=i;if(s)return!1;let l="";r.content.forEach(c=>{l+=c.textContent});let a=Xo(l,{defaultProtocol:t.defaultProtocol}).find(c=>c.isLink&&c.value===l);return!l||!a?!1:t.editor.commands.setMark(t.type,{href:a.href})}}})}function Qt(t,e){let n=["http","https","ftp","ftps","mailto","tel","callto","sms","cid","xmpp"];return e&&e.forEach(r=>{let o=typeof r=="string"?r:r.scheme;o&&n.push(o)}),!t||t.replace(tb,"").match(new RegExp(`^(?:(?:${n.join("|")}):|[^a-z]|[a-z0-9+.-]+(?:[^a-z+.-:]|$))`,"i"))}var sb=te.create({name:"link",priority:1e3,keepOnSplit:!1,exitable:!0,onCreate(){this.options.validate&&!this.options.shouldAutoLink&&(this.options.shouldAutoLink=this.options.validate,console.warn("The `validate` option is deprecated. Rename to the `shouldAutoLink` option instead.")),this.options.protocols.forEach(t=>{if(typeof t=="string"){vl(t);return}vl(t.scheme,t.optionalSlashes)})},onDestroy(){Gd()},inclusive(){return this.options.autolink},addOptions(){return{openOnClick:!0,enableClickSelection:!1,linkOnPaste:!0,autolink:!0,protocols:[],defaultProtocol:"http",HTMLAttributes:{target:"_blank",rel:"noopener noreferrer nofollow",class:null},isAllowedUri:(t,e)=>!!Qt(t,e.protocols),validate:t=>!!t,shouldAutoLink:t=>!!t}},addAttributes(){return{href:{default:null,parseHTML(t){return t.getAttribute("href")}},target:{default:this.options.HTMLAttributes.target},rel:{default:this.options.HTMLAttributes.rel},class:{default:this.options.HTMLAttributes.class}}},parseHTML(){return[{tag:"a[href]",getAttrs:t=>{let e=t.getAttribute("href");return!e||!this.options.isAllowedUri(e,{defaultValidate:n=>!!Qt(n,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?!1:null}}]},renderHTML({HTMLAttributes:t}){return this.options.isAllowedUri(t.href,{defaultValidate:e=>!!Qt(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?["a",T(this.options.HTMLAttributes,t),0]:["a",T(this.options.HTMLAttributes,{...t,href:""}),0]},addCommands(){return{setLink:t=>({chain:e})=>{let{href:n}=t;return this.options.isAllowedUri(n,{defaultValidate:r=>!!Qt(r,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?e().setMark(this.name,t).setMeta("preventAutolink",!0).run():!1},toggleLink:t=>({chain:e})=>{let{href:n}=t||{};return n&&!this.options.isAllowedUri(n,{defaultValidate:r=>!!Qt(r,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?!1:e().toggleMark(this.name,t,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()},unsetLink:()=>({chain:t})=>t().unsetMark(this.name,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()}},addPasteRules(){return[Ce({find:t=>{let e=[];if(t){let{protocols:n,defaultProtocol:r}=this.options,o=Xo(t).filter(i=>i.isLink&&this.options.isAllowedUri(i.value,{defaultValidate:s=>!!Qt(s,n),protocols:n,defaultProtocol:r}));o.length&&o.forEach(i=>e.push({text:i.value,data:{href:i.href},index:i.start}))}return e},type:this.type,getAttributes:t=>{var e;return{href:(e=t.data)==null?void 0:e.href}}})]},addProseMirrorPlugins(){let t=[],{protocols:e,defaultProtocol:n}=this.options;return this.options.autolink&&t.push(rb({type:this.type,defaultProtocol:this.options.defaultProtocol,validate:r=>this.options.isAllowedUri(r,{defaultValidate:o=>!!Qt(o,e),protocols:e,defaultProtocol:n}),shouldAutoLink:this.options.shouldAutoLink})),this.options.openOnClick===!0&&t.push(ob({type:this.type,editor:this.editor,enableClickSelection:this.options.enableClickSelection})),this.options.linkOnPaste&&t.push(ib({editor:this.editor,defaultProtocol:this.options.defaultProtocol,type:this.type})),t}}),Yd=sb;var lb=Object.defineProperty,ab=(t,e)=>{for(var n in e)lb(t,n,{get:e[n],enumerable:!0})},cb="listItem",Xd="textStyle",Qd=/^\s*([-+*])\s$/,Ol=z.create({name:"bulletList",addOptions(){return{itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}},group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML(){return[{tag:"ul"}]},renderHTML({HTMLAttributes:t}){return["ul",T(this.options.HTMLAttributes,t),0]},addCommands(){return{toggleBulletList:()=>({commands:t,chain:e})=>this.options.keepAttributes?e().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes(cb,this.editor.getAttributes(Xd)).run():t.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-8":()=>this.editor.commands.toggleBulletList()}},addInputRules(){let t=Xe({find:Qd,type:this.type});return(this.options.keepMarks||this.options.keepAttributes)&&(t=Xe({find:Qd,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:()=>this.editor.getAttributes(Xd),editor:this.editor})),[t]}}),Nl=z.create({name:"listItem",addOptions(){return{HTMLAttributes:{},bulletListTypeName:"bulletList",orderedListTypeName:"orderedList"}},content:"paragraph block*",defining:!0,parseHTML(){return[{tag:"li"}]},renderHTML({HTMLAttributes:t}){return["li",T(this.options.HTMLAttributes,t),0]},addKeyboardShortcuts(){return{Enter:()=>this.editor.commands.splitListItem(this.name),Tab:()=>this.editor.commands.sinkListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)}}}),ub={};ab(ub,{findListItemPos:()=>hr,getNextListDepth:()=>Rl,handleBackspace:()=>Al,handleDelete:()=>El,hasListBefore:()=>tf,hasListItemAfter:()=>db,hasListItemBefore:()=>nf,listItemHasSubList:()=>rf,nextListIsDeeper:()=>of,nextListIsHigher:()=>sf});var hr=(t,e)=>{let{$from:n}=e.selection,r=Q(t,e.schema),o=null,i=n.depth,s=n.pos,l=null;for(;i>0&&l===null;)o=n.node(i),o.type===r?l=i:(i-=1,s-=1);return l===null?null:{$pos:e.doc.resolve(s),depth:l}},Rl=(t,e)=>{let n=hr(t,e);if(!n)return!1;let[,r]=zu(e,t,n.$pos.pos+4);return r},tf=(t,e,n)=>{let{$anchor:r}=t.selection,o=Math.max(0,r.pos-2),i=t.doc.resolve(o).node();return!(!i||!n.includes(i.type.name))},nf=(t,e)=>{var n;let{$anchor:r}=e.selection,o=e.doc.resolve(r.pos-2);return!(o.index()===0||((n=o.nodeBefore)==null?void 0:n.type.name)!==t)},rf=(t,e,n)=>{if(!n)return!1;let r=Q(t,e.schema),o=!1;return n.descendants(i=>{i.type===r&&(o=!0)}),o},Al=(t,e,n)=>{if(t.commands.undoInputRule())return!0;if(t.state.selection.from!==t.state.selection.to)return!1;if(!Ge(t.state,e)&&tf(t.state,e,n)){let{$anchor:l}=t.state.selection,a=t.state.doc.resolve(l.before()-1),c=[];a.node().descendants((f,h)=>{f.type.name===e&&c.push({node:f,pos:h})});let u=c.at(-1);if(!u)return!1;let d=t.state.doc.resolve(a.start()+u.pos+1);return t.chain().cut({from:l.start()-1,to:l.end()+1},d.end()).joinForward().run()}if(!Ge(t.state,e)||!Fu(t.state))return!1;let r=hr(e,t.state);if(!r)return!1;let i=t.state.doc.resolve(r.$pos.pos-2).node(r.depth),s=rf(e,t.state,i);return nf(e,t.state)&&!s?t.commands.joinItemBackward():t.chain().liftListItem(e).run()},of=(t,e)=>{let n=Rl(t,e),r=hr(t,e);return!r||!n?!1:n>r.depth},sf=(t,e)=>{let n=Rl(t,e),r=hr(t,e);return!r||!n?!1:n<r.depth},El=(t,e)=>{if(!Ge(t.state,e)||!Hu(t.state,e))return!1;let{selection:n}=t.state,{$from:r,$to:o}=n;return!n.empty&&r.sameParent(o)?!1:of(e,t.state)?t.chain().focus(t.state.selection.from+4).lift(e).joinBackward().run():sf(e,t.state)?t.chain().joinForward().joinBackward().run():t.commands.joinItemForward()},db=(t,e)=>{var n;let{$anchor:r}=e.selection,o=e.doc.resolve(r.pos-r.parentOffset-2);return!(o.index()===o.parent.childCount-1||((n=o.nodeAfter)==null?void 0:n.type.name)!==t)},fb=$.create({name:"listKeymap",addOptions(){return{listTypes:[{itemName:"listItem",wrapperNames:["bulletList","orderedList"]},{itemName:"taskItem",wrapperNames:["taskList"]}]}},addKeyboardShortcuts(){return{Delete:({editor:t})=>{let e=!1;return this.options.listTypes.forEach(({itemName:n})=>{t.state.schema.nodes[n]!==void 0&&El(t,n)&&(e=!0)}),e},"Mod-Delete":({editor:t})=>{let e=!1;return this.options.listTypes.forEach(({itemName:n})=>{t.state.schema.nodes[n]!==void 0&&El(t,n)&&(e=!0)}),e},Backspace:({editor:t})=>{let e=!1;return this.options.listTypes.forEach(({itemName:n,wrapperNames:r})=>{t.state.schema.nodes[n]!==void 0&&Al(t,n,r)&&(e=!0)}),e},"Mod-Backspace":({editor:t})=>{let e=!1;return this.options.listTypes.forEach(({itemName:n,wrapperNames:r})=>{t.state.schema.nodes[n]!==void 0&&Al(t,n,r)&&(e=!0)}),e}}}}),hb="listItem",Zd="textStyle",ef=/^(\d+)\.\s$/,Dl=z.create({name:"orderedList",addOptions(){return{itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}},group:"block list",content(){return`${this.options.itemTypeName}+`},addAttributes(){return{start:{default:1,parseHTML:t=>t.hasAttribute("start")?parseInt(t.getAttribute("start")||"",10):1},type:{default:null,parseHTML:t=>t.getAttribute("type")}}},parseHTML(){return[{tag:"ol"}]},renderHTML({HTMLAttributes:t}){let{start:e,...n}=t;return e===1?["ol",T(this.options.HTMLAttributes,n),0]:["ol",T(this.options.HTMLAttributes,t),0]},addCommands(){return{toggleOrderedList:()=>({commands:t,chain:e})=>this.options.keepAttributes?e().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes(hb,this.editor.getAttributes(Zd)).run():t.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-7":()=>this.editor.commands.toggleOrderedList()}},addInputRules(){let t=Xe({find:ef,type:this.type,getAttributes:e=>({start:+e[1]}),joinPredicate:(e,n)=>n.childCount+n.attrs.start===+e[1]});return(this.options.keepMarks||this.options.keepAttributes)&&(t=Xe({find:ef,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:e=>({start:+e[1],...this.editor.getAttributes(Zd)}),joinPredicate:(e,n)=>n.childCount+n.attrs.start===+e[1],editor:this.editor})),[t]}}),pb=/^\s*(\[([( |x])?\])\s$/,mb=z.create({name:"taskItem",addOptions(){return{nested:!1,HTMLAttributes:{},taskListTypeName:"taskList",a11y:void 0}},content(){return this.options.nested?"paragraph block*":"paragraph+"},defining:!0,addAttributes(){return{checked:{default:!1,keepOnSplit:!1,parseHTML:t=>{let e=t.getAttribute("data-checked");return e===""||e==="true"},renderHTML:t=>({"data-checked":t.checked})}}},parseHTML(){return[{tag:`li[data-type="${this.name}"]`,priority:51}]},renderHTML({node:t,HTMLAttributes:e}){return["li",T(this.options.HTMLAttributes,e,{"data-type":this.name}),["label",["input",{type:"checkbox",checked:t.attrs.checked?"checked":null}],["span"]],["div",0]]},addKeyboardShortcuts(){let t={Enter:()=>this.editor.commands.splitListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)};return this.options.nested?{...t,Tab:()=>this.editor.commands.sinkListItem(this.name)}:t},addNodeView(){return({node:t,HTMLAttributes:e,getPos:n,editor:r})=>{let o=document.createElement("li"),i=document.createElement("label"),s=document.createElement("span"),l=document.createElement("input"),a=document.createElement("div"),c=()=>{var u,d;l.ariaLabel=((d=(u=this.options.a11y)==null?void 0:u.checkboxLabel)==null?void 0:d.call(u,t,l.checked))||`Task item checkbox for ${t.textContent||"empty task item"}`};return c(),i.contentEditable="false",l.type="checkbox",l.addEventListener("mousedown",u=>u.preventDefault()),l.addEventListener("change",u=>{if(!r.isEditable&&!this.options.onReadOnlyChecked){l.checked=!l.checked;return}let{checked:d}=u.target;r.isEditable&&typeof n=="function"&&r.chain().focus(void 0,{scrollIntoView:!1}).command(({tr:f})=>{let h=n();if(typeof h!="number")return!1;let p=f.doc.nodeAt(h);return f.setNodeMarkup(h,void 0,{...p?.attrs,checked:d}),!0}).run(),!r.isEditable&&this.options.onReadOnlyChecked&&(this.options.onReadOnlyChecked(t,d)||(l.checked=!l.checked))}),Object.entries(this.options.HTMLAttributes).forEach(([u,d])=>{o.setAttribute(u,d)}),o.dataset.checked=t.attrs.checked,l.checked=t.attrs.checked,i.append(l,s),o.append(i,a),Object.entries(e).forEach(([u,d])=>{o.setAttribute(u,d)}),{dom:o,contentDOM:a,update:u=>u.type!==this.type?!1:(o.dataset.checked=u.attrs.checked,l.checked=u.attrs.checked,c(),!0)}}},addInputRules(){return[Xe({find:pb,type:this.type,getAttributes:t=>({checked:t[t.length-1]==="x"})})]}}),gb=z.create({name:"taskList",addOptions(){return{itemTypeName:"taskItem",HTMLAttributes:{}}},group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML(){return[{tag:`ul[data-type="${this.name}"]`,priority:51}]},renderHTML({HTMLAttributes:t}){return["ul",T(this.options.HTMLAttributes,t,{"data-type":this.name}),0]},addCommands(){return{toggleTaskList:()=>({commands:t})=>t.toggleList(this.name,this.options.itemTypeName)}},addKeyboardShortcuts(){return{"Mod-Shift-9":()=>this.editor.commands.toggleTaskList()}}}),gC=$.create({name:"listKit",addExtensions(){let t=[];return this.options.bulletList!==!1&&t.push(Ol.configure(this.options.bulletList)),this.options.listItem!==!1&&t.push(Nl.configure(this.options.listItem)),this.options.listKeymap!==!1&&t.push(fb.configure(this.options.listKeymap)),this.options.orderedList!==!1&&t.push(Dl.configure(this.options.orderedList)),this.options.taskItem!==!1&&t.push(mb.configure(this.options.taskItem)),this.options.taskList!==!1&&t.push(gb.configure(this.options.taskList)),t}});var lf=["image/png","image/jpeg","image/gif","image/webp"],Qo=(t,e,n={})=>{t.dom.closest("form")?.dispatchEvent(new CustomEvent(e,{composed:!0,cancelable:!0,detail:n}))},yb=({editor:t,get$WireUsing:e,key:n,statePath:r,uploadingMessage:o})=>{let i=s=>e().callSchemaComponentMethod(n,"getUploadedFileAttachmentTemporaryUrl",{attachment:s});return new N({key:new L("localFiles"),props:{handleDrop(s,l){if(!l.dataTransfer?.files.length)return!1;let a=Array.from(l.dataTransfer.files).filter(u=>lf.includes(u.type));if(!a.length)return!1;Qo(s,"form-processing-started",{message:o}),l.preventDefault(),l.stopPropagation();let c=s.posAtCoords({left:l.clientX,top:l.clientY});return a.forEach((u,d)=>{t.setEditable(!1),s.dom.dispatchEvent(new CustomEvent("rich-editor-uploading-file",{bubbles:!0,detail:{key:n,livewireId:e().id}}));let f=new FileReader;f.readAsDataURL(u),f.onload=()=>{t.chain().insertContentAt(c?.pos??0,{type:"image",attrs:{class:"fi-loading",src:f.result}}).run()};let h=("10000000-1000-4000-8000"+-1e11).replace(/[018]/g,p=>(p^crypto.getRandomValues(new Uint8Array(1))[0]&15>>p/4).toString(16));e().upload(`componentFileAttachments.${r}.${h}`,u,()=>{i(h).then(p=>{p&&(t.chain().updateAttributes("image",{class:null,id:h,src:p}).run(),t.setEditable(!0),s.dom.dispatchEvent(new CustomEvent("rich-editor-uploaded-file",{bubbles:!0,detail:{key:n,livewireId:e().id}})),d===a.length-1&&Qo(s,"form-processing-finished"))})})}),!0},handlePaste(s,l){if(!l.clipboardData?.files.length)return!1;let a=Array.from(l.clipboardData.files).filter(c=>lf.includes(c.type));return a.length?(l.preventDefault(),l.stopPropagation(),Qo(s,"form-processing-started",{message:o}),a.forEach((c,u)=>{t.setEditable(!1),s.dom.dispatchEvent(new CustomEvent("rich-editor-uploading-file",{bubbles:!0,detail:{key:n,livewireId:e().id}}));let d=new FileReader;d.readAsDataURL(c),d.onload=()=>{t.chain().insertContentAt(t.state.selection.anchor,{type:"image",attrs:{class:"fi-loading",src:d.result}}).run()};let f=("10000000-1000-4000-8000"+-1e11).replace(/[018]/g,h=>(h^crypto.getRandomValues(new Uint8Array(1))[0]&15>>h/4).toString(16));e().upload(`componentFileAttachments.${r}.${f}`,c,()=>{i(f).then(h=>{h&&(t.chain().updateAttributes("image",{class:null,id:f,src:h}).run(),t.setEditable(!0),s.dom.dispatchEvent(new CustomEvent("rich-editor-uploaded-file",{bubbles:!0,detail:{key:n,livewireId:e().id}})),u===a.length-1&&Qo(s,"form-processing-finished"))})})}),!0):!1}}})},af=$.create({name:"localFiles",addOptions(){return{key:null,statePath:null,uploadingMessage:null,get$WireUsing:null}},addProseMirrorPlugins(){return[yb({editor:this.editor,...this.options})]}});function bb(t){var e;let{char:n,allowSpaces:r,allowToIncludeChar:o,allowedPrefixes:i,startOfLine:s,$position:l}=t,a=r&&!o,c=id(n),u=new RegExp(`\\s${c}$`),d=s?"^":"",f=o?"":c,h=a?new RegExp(`${d}${c}.*?(?=\\s${f}|$)`,"gm"):new RegExp(`${d}(?:^)?${c}[^\\s${f}]*`,"gm"),p=((e=l.nodeBefore)==null?void 0:e.isText)&&l.nodeBefore.text;if(!p)return null;let m=l.pos-p.length,g=Array.from(p.matchAll(h)).pop();if(!g||g.input===void 0||g.index===void 0)return null;let y=g.input.slice(Math.max(0,g.index-1),g.index),b=new RegExp(`^[${i?.join("")}\0]?$`).test(y);if(i!==null&&!b)return null;let x=m+g.index,C=x+g[0].length;return a&&u.test(p.slice(C-1,C+1))&&(g[0]+=" ",C+=1),x<l.pos&&C>=l.pos?{range:{from:x,to:C},query:g[0].slice(n.length),text:g[0]}:null}var xb=new L("suggestion");function wb({pluginKey:t=xb,editor:e,char:n="@",allowSpaces:r=!1,allowToIncludeChar:o=!1,allowedPrefixes:i=[" "],startOfLine:s=!1,decorationTag:l="span",decorationClass:a="suggestion",decorationContent:c="",decorationEmptyClass:u="is-empty",command:d=()=>null,items:f=()=>[],render:h=()=>({}),allow:p=()=>!0,findSuggestionMatch:m=bb}){let g,y=h?.(),b=new N({key:t,view(){return{update:async(x,C)=>{var S,R,P,v,B,D,_;let H=(S=this.key)==null?void 0:S.getState(C),j=(R=this.key)==null?void 0:R.getState(x.state),U=H.active&&j.active&&H.range.from!==j.range.from,oe=!H.active&&j.active,X=H.active&&!j.active,G=!oe&&!X&&H.query!==j.query,ie=oe||U&&G,Te=G||U,On=X||U&&G;if(!ie&&!Te&&!On)return;let rn=On&&!ie?H:j,Ql=x.dom.querySelector(`[data-decoration-id="${rn.decorationId}"]`);g={editor:e,range:rn.range,query:rn.query,text:rn.text,items:[],command:wr=>d({editor:e,range:rn.range,props:wr}),decorationNode:Ql,clientRect:Ql?()=>{var wr;let{decorationId:zh}=(wr=this.key)==null?void 0:wr.getState(e.state),Zl=x.dom.querySelector(`[data-decoration-id="${zh}"]`);return Zl?.getBoundingClientRect()||null}:null},ie&&((P=y?.onBeforeStart)==null||P.call(y,g)),Te&&((v=y?.onBeforeUpdate)==null||v.call(y,g)),(Te||ie)&&(g.items=await f({editor:e,query:rn.query})),On&&((B=y?.onExit)==null||B.call(y,g)),Te&&((D=y?.onUpdate)==null||D.call(y,g)),ie&&((_=y?.onStart)==null||_.call(y,g))},destroy:()=>{var x;g&&((x=y?.onExit)==null||x.call(y,g))}}},state:{init(){return{active:!1,range:{from:0,to:0},query:null,text:null,composing:!1}},apply(x,C,S,R){let{isEditable:P}=e,{composing:v}=e.view,{selection:B}=x,{empty:D,from:_}=B,H={...C};if(H.composing=v,P&&(D||e.view.composing)){(_<C.range.from||_>C.range.to)&&!v&&!C.composing&&(H.active=!1);let j=m({char:n,allowSpaces:r,allowToIncludeChar:o,allowedPrefixes:i,startOfLine:s,$position:B.$from}),U=`id_${Math.floor(Math.random()*4294967295)}`;j&&p({editor:e,state:R,range:j.range,isActive:C.active})?(H.active=!0,H.decorationId=C.decorationId?C.decorationId:U,H.range=j.range,H.query=j.query,H.text=j.text):H.active=!1}else H.active=!1;return H.active||(H.decorationId=null,H.range={from:0,to:0},H.query=null,H.text=null),H}},props:{handleKeyDown(x,C){var S;let{active:R,range:P}=b.getState(x.state);return R&&((S=y?.onKeyDown)==null?void 0:S.call(y,{view:x,event:C,range:P}))||!1},decorations(x){let{active:C,range:S,decorationId:R,query:P}=b.getState(x);if(!C)return null;let v=!P?.length,B=[a];return v&&B.push(u),J.create(x.doc,[Y.inline(S.from,S.to,{nodeName:l,class:B.join(" "),"data-decoration-id":R,"data-decoration-content":c})])}}});return b}var cf=wb;var Sb=function({editor:t,overrideSuggestionOptions:e,extensionName:n}){let r=new L;return{editor:t,char:"{{",pluginKey:r,command:({editor:o,range:i,props:s})=>{o.view.state.selection.$to.nodeAfter?.text?.startsWith(" ")&&(i.to+=1),o.chain().focus().insertContentAt(i,[{type:n,attrs:{...s}},{type:"text",text:" "}]).run(),o.view.dom.ownerDocument.defaultView?.getSelection()?.collapseToEnd()},allow:({state:o,range:i})=>{let s=o.doc.resolve(i.from),l=o.schema.nodes[n];return!!s.parent.type.contentMatch.matchType(l)},...e}},uf=z.create({name:"mergeTag",priority:101,addStorage(){return{suggestions:[],getSuggestionFromChar:()=>null}},addOptions(){return{HTMLAttributes:{},renderText({node:t}){return`{{ ${t.attrs.label??t.attrs.id} }}`},deleteTriggerWithBackspace:!1,renderHTML({options:t,node:e}){return["span",T(this.HTMLAttributes,t.HTMLAttributes),`${e.attrs.label??e.attrs.id}`]},suggestions:[],suggestion:{}}},group:"inline",inline:!0,selectable:!1,atom:!0,addAttributes(){return{id:{default:null,parseHTML:t=>t.getAttribute("data-id"),renderHTML:t=>t.id?{"data-id":t.id}:{}},label:{default:null,parseHTML:t=>t.getAttribute("data-label"),renderHTML:t=>t.label?{"data-label":t.label}:{}}}},parseHTML(){return[{tag:`span[data-type="${this.name}"]`}]},renderHTML({node:t,HTMLAttributes:e}){let n=this.editor?.extensionStorage?.[this.name]?.getSuggestionFromChar("{{"),r={...this.options};r.HTMLAttributes=T({"data-type":this.name},this.options.HTMLAttributes,e);let o=this.options.renderHTML({options:r,node:t,suggestion:n});return typeof o=="string"?["span",T({"data-type":this.name},this.options.HTMLAttributes,e),o]:o},renderText({node:t}){let e={options:this.options,node:t,suggestion:this.editor?.extensionStorage?.[this.name]?.getSuggestionFromChar("{{")};return this.options.renderText(e)},addKeyboardShortcuts(){return{Backspace:()=>this.editor.commands.command(({tr:t,state:e})=>{let n=!1,{selection:r}=e,{empty:o,anchor:i}=r;if(!o)return!1;let s=new le,l=0;return e.doc.nodesBetween(i-1,i,(a,c)=>{if(a.type.name===this.name)return n=!0,s=a,l=c,!1}),n&&t.insertText(this.options.deleteTriggerWithBackspace?"":"{{",l,l+s.nodeSize),n})}},addProseMirrorPlugins(){return[...this.storage.suggestions.map(cf),new N({props:{handleDrop(t,e){if(!e||(e.preventDefault(),!e.dataTransfer.getData("mergeTag")))return!1;let n=e.dataTransfer.getData("mergeTag");return t.dispatch(t.state.tr.insert(t.posAtCoords({left:e.clientX,top:e.clientY}).pos,t.state.schema.nodes.mergeTag.create({id:n}))),!1}}})]},onBeforeCreate(){this.storage.suggestions=(this.options.suggestions.length?this.options.suggestions:[this.options.suggestion]).map(t=>Sb({editor:this.editor,overrideSuggestionOptions:t,extensionName:this.name})),this.storage.getSuggestionFromChar=t=>{let e=this.storage.suggestions.find(n=>n.char===t);return e||(this.storage.suggestions.length?this.storage.suggestions[0]:null)}}});var kb=z.create({name:"paragraph",priority:1e3,addOptions(){return{HTMLAttributes:{}}},group:"block",content:"inline*",parseHTML(){return[{tag:"p"}]},renderHTML({HTMLAttributes:t}){return["p",T(this.options.HTMLAttributes,t),0]},addCommands(){return{setParagraph:()=>({commands:t})=>t.setNode(this.name)}},addKeyboardShortcuts(){return{"Mod-Alt-0":()=>this.editor.commands.setParagraph()}}}),df=kb;var ff=rl;var hf=te.create({name:"small",parseHTML(){return[{tag:"small"}]},renderHTML({HTMLAttributes:t}){return["small",t,0]},addCommands(){return{setSmall:()=>({commands:t})=>t.setMark(this.name),toggleSmall:()=>({commands:t})=>t.toggleMark(this.name),unsetSmall:()=>({commands:t})=>t.unsetMark(this.name)}}});var Cb=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))$/,Mb=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))/g,vb=te.create({name:"strike",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"s"},{tag:"del"},{tag:"strike"},{style:"text-decoration",consuming:!1,getAttrs:t=>t.includes("line-through")?{}:!1}]},renderHTML({HTMLAttributes:t}){return["s",T(this.options.HTMLAttributes,t),0]},addCommands(){return{setStrike:()=>({commands:t})=>t.setMark(this.name),toggleStrike:()=>({commands:t})=>t.toggleMark(this.name),unsetStrike:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-s":()=>this.editor.commands.toggleStrike()}},addInputRules(){return[Ie({find:Cb,type:this.type})]},addPasteRules(){return[Ce({find:Mb,type:this.type})]}}),pf=vb;var Tb=te.create({name:"subscript",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"sub"},{style:"vertical-align",getAttrs(t){return t!=="sub"?!1:null}}]},renderHTML({HTMLAttributes:t}){return["sub",T(this.options.HTMLAttributes,t),0]},addCommands(){return{setSubscript:()=>({commands:t})=>t.setMark(this.name),toggleSubscript:()=>({commands:t})=>t.toggleMark(this.name),unsetSubscript:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-,":()=>this.editor.commands.toggleSubscript()}}}),mf=Tb;var Ab=te.create({name:"superscript",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"sup"},{style:"vertical-align",getAttrs(t){return t!=="super"?!1:null}}]},renderHTML({HTMLAttributes:t}){return["sup",T(this.options.HTMLAttributes,t),0]},addCommands(){return{setSuperscript:()=>({commands:t})=>t.setMark(this.name),toggleSuperscript:()=>({commands:t})=>t.toggleMark(this.name),unsetSuperscript:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-.":()=>this.editor.commands.toggleSuperscript()}}}),gf=Ab;var Il,Ll;if(typeof WeakMap<"u"){let t=new WeakMap;Il=e=>t.get(e),Ll=(e,n)=>(t.set(e,n),n)}else{let t=[],n=0;Il=r=>{for(let o=0;o<t.length;o+=2)if(t[o]==r)return t[o+1]},Ll=(r,o)=>(n==10&&(n=0),t[n++]=r,t[n++]=o)}var ee=class{constructor(t,e,n,r){this.width=t,this.height=e,this.map=n,this.problems=r}findCell(t){for(let e=0;e<this.map.length;e++){let n=this.map[e];if(n!=t)continue;let r=e%this.width,o=e/this.width|0,i=r+1,s=o+1;for(let l=1;i<this.width&&this.map[e+l]==n;l++)i++;for(let l=1;s<this.height&&this.map[e+this.width*l]==n;l++)s++;return{left:r,top:o,right:i,bottom:s}}throw new RangeError(`No cell with offset ${t} found`)}colCount(t){for(let e=0;e<this.map.length;e++)if(this.map[e]==t)return e%this.width;throw new RangeError(`No cell with offset ${t} found`)}nextCell(t,e,n){let{left:r,right:o,top:i,bottom:s}=this.findCell(t);return e=="horiz"?(n<0?r==0:o==this.width)?null:this.map[i*this.width+(n<0?r-1:o)]:(n<0?i==0:s==this.height)?null:this.map[r+this.width*(n<0?i-1:s)]}rectBetween(t,e){let{left:n,right:r,top:o,bottom:i}=this.findCell(t),{left:s,right:l,top:a,bottom:c}=this.findCell(e);return{left:Math.min(n,s),top:Math.min(o,a),right:Math.max(r,l),bottom:Math.max(i,c)}}cellsInRect(t){let e=[],n={};for(let r=t.top;r<t.bottom;r++)for(let o=t.left;o<t.right;o++){let i=r*this.width+o,s=this.map[i];n[s]||(n[s]=!0,!(o==t.left&&o&&this.map[i-1]==s||r==t.top&&r&&this.map[i-this.width]==s)&&e.push(s))}return e}positionAt(t,e,n){for(let r=0,o=0;;r++){let i=o+n.child(r).nodeSize;if(r==t){let s=e+t*this.width,l=(t+1)*this.width;for(;s<l&&this.map[s]<o;)s++;return s==l?i-1:this.map[s]}o=i}}static get(t){return Il(t)||Ll(t,Eb(t))}};function Eb(t){if(t.type.spec.tableRole!="table")throw new RangeError("Not a table node: "+t.type.name);let e=Ob(t),n=t.childCount,r=[],o=0,i=null,s=[];for(let c=0,u=e*n;c<u;c++)r[c]=0;for(let c=0,u=0;c<n;c++){let d=t.child(c);u++;for(let p=0;;p++){for(;o<r.length&&r[o]!=0;)o++;if(p==d.childCount)break;let m=d.child(p),{colspan:g,rowspan:y,colwidth:b}=m.attrs;for(let x=0;x<y;x++){if(x+c>=n){(i||(i=[])).push({type:"overlong_rowspan",pos:u,n:y-x});break}let C=o+x*e;for(let S=0;S<g;S++){r[C+S]==0?r[C+S]=u:(i||(i=[])).push({type:"collision",row:c,pos:u,n:g-S});let R=b&&b[S];if(R){let P=(C+S)%e*2,v=s[P];v==null||v!=R&&s[P+1]==1?(s[P]=R,s[P+1]=1):v==R&&s[P+1]++}}}o+=g,u+=m.nodeSize}let f=(c+1)*e,h=0;for(;o<f;)r[o++]==0&&h++;h&&(i||(i=[])).push({type:"missing",row:c,n:h}),u++}(e===0||n===0)&&(i||(i=[])).push({type:"zero_sized"});let l=new ee(e,n,r,i),a=!1;for(let c=0;!a&&c<s.length;c+=2)s[c]!=null&&s[c+1]<n&&(a=!0);return a&&Nb(l,s,t),l}function Ob(t){let e=-1,n=!1;for(let r=0;r<t.childCount;r++){let o=t.child(r),i=0;if(n)for(let s=0;s<r;s++){let l=t.child(s);for(let a=0;a<l.childCount;a++){let c=l.child(a);s+c.attrs.rowspan>r&&(i+=c.attrs.colspan)}}for(let s=0;s<o.childCount;s++){let l=o.child(s);i+=l.attrs.colspan,l.attrs.rowspan>1&&(n=!0)}e==-1?e=i:e!=i&&(e=Math.max(e,i))}return e}function Nb(t,e,n){t.problems||(t.problems=[]);let r={};for(let o=0;o<t.map.length;o++){let i=t.map[o];if(r[i])continue;r[i]=!0;let s=n.nodeAt(i);if(!s)throw new RangeError(`No cell with offset ${i} found`);let l=null,a=s.attrs;for(let c=0;c<a.colspan;c++){let u=(o+c)%t.width,d=e[u*2];d!=null&&(!a.colwidth||a.colwidth[c]!=d)&&((l||(l=Rb(a)))[c]=d)}l&&t.problems.unshift({type:"colwidth mismatch",pos:i,colwidth:l})}}function Rb(t){if(t.colwidth)return t.colwidth.slice();let e=[];for(let n=0;n<t.colspan;n++)e.push(0);return e}function ye(t){let e=t.cached.tableNodeTypes;if(!e){e=t.cached.tableNodeTypes={};for(let n in t.nodes){let r=t.nodes[n],o=r.spec.tableRole;o&&(e[o]=r)}}return e}var Pt=new L("selectingCells");function vn(t){for(let e=t.depth-1;e>0;e--)if(t.node(e).type.spec.tableRole=="row")return t.node(0).resolve(t.before(e+1));return null}function Db(t){for(let e=t.depth;e>0;e--){let n=t.node(e).type.spec.tableRole;if(n==="cell"||n==="header_cell")return t.node(e)}return null}function We(t){let e=t.selection.$head;for(let n=e.depth;n>0;n--)if(e.node(n).type.spec.tableRole=="row")return!0;return!1}function oi(t){let e=t.selection;if("$anchorCell"in e&&e.$anchorCell)return e.$anchorCell.pos>e.$headCell.pos?e.$anchorCell:e.$headCell;if("node"in e&&e.node&&e.node.type.spec.tableRole=="cell")return e.$anchor;let n=vn(e.$head)||Pb(e.$head);if(n)return n;throw new RangeError(`No cell found around position ${e.head}`)}function Pb(t){for(let e=t.nodeAfter,n=t.pos;e;e=e.firstChild,n++){let r=e.type.spec.tableRole;if(r=="cell"||r=="header_cell")return t.doc.resolve(n)}for(let e=t.nodeBefore,n=t.pos;e;e=e.lastChild,n--){let r=e.type.spec.tableRole;if(r=="cell"||r=="header_cell")return t.doc.resolve(n-e.nodeSize)}}function Bl(t){return t.parent.type.spec.tableRole=="row"&&!!t.nodeAfter}function Ib(t){return t.node(0).resolve(t.pos+t.nodeAfter.nodeSize)}function Fl(t,e){return t.depth==e.depth&&t.pos>=e.start(-1)&&t.pos<=e.end(-1)}function Tf(t,e,n){let r=t.node(-1),o=ee.get(r),i=t.start(-1),s=o.nextCell(t.pos-i,e,n);return s==null?null:t.node(0).resolve(i+s)}function Zt(t,e,n=1){let r={...t,colspan:t.colspan-n};return r.colwidth&&(r.colwidth=r.colwidth.slice(),r.colwidth.splice(e,n),r.colwidth.some(o=>o>0)||(r.colwidth=null)),r}function Af(t,e,n=1){let r={...t,colspan:t.colspan+n};if(r.colwidth){r.colwidth=r.colwidth.slice();for(let o=0;o<n;o++)r.colwidth.splice(e,0,0)}return r}function Lb(t,e,n){let r=ye(e.type.schema).header_cell;for(let o=0;o<t.height;o++)if(e.nodeAt(t.map[n+o*t.width]).type!=r)return!1;return!0}var K=class xt extends E{constructor(e,n=e){let r=e.node(-1),o=ee.get(r),i=e.start(-1),s=o.rectBetween(e.pos-i,n.pos-i),l=e.node(0),a=o.cellsInRect(s).filter(u=>u!=n.pos-i);a.unshift(n.pos-i);let c=a.map(u=>{let d=r.nodeAt(u);if(!d)throw RangeError(`No cell with offset ${u} found`);let f=i+u+1;return new dn(l.resolve(f),l.resolve(f+d.content.size))});super(c[0].$from,c[0].$to,c),this.$anchorCell=e,this.$headCell=n}map(e,n){let r=e.resolve(n.map(this.$anchorCell.pos)),o=e.resolve(n.map(this.$headCell.pos));if(Bl(r)&&Bl(o)&&Fl(r,o)){let i=this.$anchorCell.node(-1)!=r.node(-1);return i&&this.isRowSelection()?xt.rowSelection(r,o):i&&this.isColSelection()?xt.colSelection(r,o):new xt(r,o)}return A.between(r,o)}content(){let e=this.$anchorCell.node(-1),n=ee.get(e),r=this.$anchorCell.start(-1),o=n.rectBetween(this.$anchorCell.pos-r,this.$headCell.pos-r),i={},s=[];for(let a=o.top;a<o.bottom;a++){let c=[];for(let u=a*n.width+o.left,d=o.left;d<o.right;d++,u++){let f=n.map[u];if(i[f])continue;i[f]=!0;let h=n.findCell(f),p=e.nodeAt(f);if(!p)throw RangeError(`No cell with offset ${f} found`);let m=o.left-h.left,g=h.right-o.right;if(m>0||g>0){let y=p.attrs;if(m>0&&(y=Zt(y,0,m)),g>0&&(y=Zt(y,y.colspan-g,g)),h.left<o.left){if(p=p.type.createAndFill(y),!p)throw RangeError(`Could not create cell with attrs ${JSON.stringify(y)}`)}else p=p.type.create(y,p.content)}if(h.top<o.top||h.bottom>o.bottom){let y={...p.attrs,rowspan:Math.min(h.bottom,o.bottom)-Math.max(h.top,o.top)};h.top<o.top?p=p.type.createAndFill(y):p=p.type.create(y,p.content)}c.push(p)}s.push(e.child(a).copy(w.from(c)))}let l=this.isColSelection()&&this.isRowSelection()?e:s;return new M(w.from(l),1,1)}replace(e,n=M.empty){let r=e.steps.length,o=this.ranges;for(let s=0;s<o.length;s++){let{$from:l,$to:a}=o[s],c=e.mapping.slice(r);e.replace(c.map(l.pos),c.map(a.pos),s?M.empty:n)}let i=E.findFrom(e.doc.resolve(e.mapping.slice(r).map(this.to)),-1);i&&e.setSelection(i)}replaceWith(e,n){this.replace(e,new M(w.from(n),0,0))}forEachCell(e){let n=this.$anchorCell.node(-1),r=ee.get(n),o=this.$anchorCell.start(-1),i=r.cellsInRect(r.rectBetween(this.$anchorCell.pos-o,this.$headCell.pos-o));for(let s=0;s<i.length;s++)e(n.nodeAt(i[s]),o+i[s])}isColSelection(){let e=this.$anchorCell.index(-1),n=this.$headCell.index(-1);if(Math.min(e,n)>0)return!1;let r=e+this.$anchorCell.nodeAfter.attrs.rowspan,o=n+this.$headCell.nodeAfter.attrs.rowspan;return Math.max(r,o)==this.$headCell.node(-1).childCount}static colSelection(e,n=e){let r=e.node(-1),o=ee.get(r),i=e.start(-1),s=o.findCell(e.pos-i),l=o.findCell(n.pos-i),a=e.node(0);return s.top<=l.top?(s.top>0&&(e=a.resolve(i+o.map[s.left])),l.bottom<o.height&&(n=a.resolve(i+o.map[o.width*(o.height-1)+l.right-1]))):(l.top>0&&(n=a.resolve(i+o.map[l.left])),s.bottom<o.height&&(e=a.resolve(i+o.map[o.width*(o.height-1)+s.right-1]))),new xt(e,n)}isRowSelection(){let e=this.$anchorCell.node(-1),n=ee.get(e),r=this.$anchorCell.start(-1),o=n.colCount(this.$anchorCell.pos-r),i=n.colCount(this.$headCell.pos-r);if(Math.min(o,i)>0)return!1;let s=o+this.$anchorCell.nodeAfter.attrs.colspan,l=i+this.$headCell.nodeAfter.attrs.colspan;return Math.max(s,l)==n.width}eq(e){return e instanceof xt&&e.$anchorCell.pos==this.$anchorCell.pos&&e.$headCell.pos==this.$headCell.pos}static rowSelection(e,n=e){let r=e.node(-1),o=ee.get(r),i=e.start(-1),s=o.findCell(e.pos-i),l=o.findCell(n.pos-i),a=e.node(0);return s.left<=l.left?(s.left>0&&(e=a.resolve(i+o.map[s.top*o.width])),l.right<o.width&&(n=a.resolve(i+o.map[o.width*(l.top+1)-1]))):(l.left>0&&(n=a.resolve(i+o.map[l.top*o.width])),s.right<o.width&&(e=a.resolve(i+o.map[o.width*(s.top+1)-1]))),new xt(e,n)}toJSON(){return{type:"cell",anchor:this.$anchorCell.pos,head:this.$headCell.pos}}static fromJSON(e,n){return new xt(e.resolve(n.anchor),e.resolve(n.head))}static create(e,n,r=n){return new xt(e.resolve(n),e.resolve(r))}getBookmark(){return new Bb(this.$anchorCell.pos,this.$headCell.pos)}};K.prototype.visible=!1;E.jsonID("cell",K);var Bb=class Ef{constructor(e,n){this.anchor=e,this.head=n}map(e){return new Ef(e.map(this.anchor),e.map(this.head))}resolve(e){let n=e.resolve(this.anchor),r=e.resolve(this.head);return n.parent.type.spec.tableRole=="row"&&r.parent.type.spec.tableRole=="row"&&n.index()<n.parent.childCount&&r.index()<r.parent.childCount&&Fl(n,r)?new K(n,r):E.near(r,1)}};function zb(t){if(!(t.selection instanceof K))return null;let e=[];return t.selection.forEachCell((n,r)=>{e.push(Y.node(r,r+n.nodeSize,{class:"selectedCell"}))}),J.create(t.doc,e)}function Hb({$from:t,$to:e}){if(t.pos==e.pos||t.pos<e.pos-6)return!1;let n=t.pos,r=e.pos,o=t.depth;for(;o>=0&&!(t.after(o+1)<t.end(o));o--,n++);for(let i=e.depth;i>=0&&!(e.before(i+1)>e.start(i));i--,r--);return n==r&&/row|table/.test(t.node(o).type.spec.tableRole)}function Fb({$from:t,$to:e}){let n,r;for(let o=t.depth;o>0;o--){let i=t.node(o);if(i.type.spec.tableRole==="cell"||i.type.spec.tableRole==="header_cell"){n=i;break}}for(let o=e.depth;o>0;o--){let i=e.node(o);if(i.type.spec.tableRole==="cell"||i.type.spec.tableRole==="header_cell"){r=i;break}}return n!==r&&e.parentOffset===0}function $b(t,e,n){let r=(e||t).selection,o=(e||t).doc,i,s;if(r instanceof O&&(s=r.node.type.spec.tableRole)){if(s=="cell"||s=="header_cell")i=K.create(o,r.from);else if(s=="row"){let l=o.resolve(r.from+1);i=K.rowSelection(l,l)}else if(!n){let l=ee.get(r.node),a=r.from+1,c=a+l.map[l.width*l.height-1];i=K.create(o,a+1,c)}}else r instanceof A&&Hb(r)?i=A.create(o,r.from):r instanceof A&&Fb(r)&&(i=A.create(o,r.$from.start(),r.$from.end()));return i&&(e||(e=t.tr)).setSelection(i),e}var Vb=new L("fix-tables");function Of(t,e,n,r){let o=t.childCount,i=e.childCount;e:for(let s=0,l=0;s<i;s++){let a=e.child(s);for(let c=l,u=Math.min(o,s+3);c<u;c++)if(t.child(c)==a){l=c+1,n+=a.nodeSize;continue e}r(a,n),l<o&&t.child(l).sameMarkup(a)?Of(t.child(l),a,n+1,r):a.nodesBetween(0,a.content.size,r,n+1),n+=a.nodeSize}}function $l(t,e){let n,r=(o,i)=>{o.type.spec.tableRole=="table"&&(n=Wb(t,o,i,n))};return e?e.doc!=t.doc&&Of(e.doc,t.doc,0,r):t.doc.descendants(r),n}function Wb(t,e,n,r){let o=ee.get(e);if(!o.problems)return r;r||(r=t.tr);let i=[];for(let a=0;a<o.height;a++)i.push(0);for(let a=0;a<o.problems.length;a++){let c=o.problems[a];if(c.type=="collision"){let u=e.nodeAt(c.pos);if(!u)continue;let d=u.attrs;for(let f=0;f<d.rowspan;f++)i[c.row+f]+=c.n;r.setNodeMarkup(r.mapping.map(n+1+c.pos),null,Zt(d,d.colspan-c.n,c.n))}else if(c.type=="missing")i[c.row]+=c.n;else if(c.type=="overlong_rowspan"){let u=e.nodeAt(c.pos);if(!u)continue;r.setNodeMarkup(r.mapping.map(n+1+c.pos),null,{...u.attrs,rowspan:u.attrs.rowspan-c.n})}else if(c.type=="colwidth mismatch"){let u=e.nodeAt(c.pos);if(!u)continue;r.setNodeMarkup(r.mapping.map(n+1+c.pos),null,{...u.attrs,colwidth:c.colwidth})}else if(c.type=="zero_sized"){let u=r.mapping.map(n);r.delete(u,u+e.nodeSize)}}let s,l;for(let a=0;a<i.length;a++)i[a]&&(s==null&&(s=a),l=a);for(let a=0,c=n+1;a<o.height;a++){let u=e.child(a),d=c+u.nodeSize,f=i[a];if(f>0){let h="cell";u.firstChild&&(h=u.firstChild.type.spec.tableRole);let p=[];for(let g=0;g<f;g++){let y=ye(t.schema)[h].createAndFill();y&&p.push(y)}let m=(a==0||s==a-1)&&l==a?c+1:d-1;r.insert(r.mapping.map(m),p)}c=d}return r.setMeta(Vb,{fixTables:!0})}function nt(t){let e=t.selection,n=oi(t),r=n.node(-1),o=n.start(-1),i=ee.get(r);return{...e instanceof K?i.rectBetween(e.$anchorCell.pos-o,e.$headCell.pos-o):i.findCell(n.pos-o),tableStart:o,map:i,table:r}}function Nf(t,{map:e,tableStart:n,table:r},o){let i=o>0?-1:0;Lb(e,r,o+i)&&(i=o==0||o==e.width?null:0);for(let s=0;s<e.height;s++){let l=s*e.width+o;if(o>0&&o<e.width&&e.map[l-1]==e.map[l]){let a=e.map[l],c=r.nodeAt(a);t.setNodeMarkup(t.mapping.map(n+a),null,Af(c.attrs,o-e.colCount(a))),s+=c.attrs.rowspan-1}else{let a=i==null?ye(r.type.schema).cell:r.nodeAt(e.map[l+i]).type,c=e.positionAt(s,o,r);t.insert(t.mapping.map(n+c),a.createAndFill())}}return t}function Rf(t,e){if(!We(t))return!1;if(e){let n=nt(t);e(Nf(t.tr,n,n.left))}return!0}function Df(t,e){if(!We(t))return!1;if(e){let n=nt(t);e(Nf(t.tr,n,n.right))}return!0}function _b(t,{map:e,table:n,tableStart:r},o){let i=t.mapping.maps.length;for(let s=0;s<e.height;){let l=s*e.width+o,a=e.map[l],c=n.nodeAt(a),u=c.attrs;if(o>0&&e.map[l-1]==a||o<e.width-1&&e.map[l+1]==a)t.setNodeMarkup(t.mapping.slice(i).map(r+a),null,Zt(u,o-e.colCount(a)));else{let d=t.mapping.slice(i).map(r+a);t.delete(d,d+c.nodeSize)}s+=u.rowspan}}function Pf(t,e){if(!We(t))return!1;if(e){let n=nt(t),r=t.tr;if(n.left==0&&n.right==n.map.width)return!1;for(let o=n.right-1;_b(r,n,o),o!=n.left;o--){let i=n.tableStart?r.doc.nodeAt(n.tableStart-1):r.doc;if(!i)throw RangeError("No table found");n.table=i,n.map=ee.get(i)}e(r)}return!0}function jb(t,e,n){var r;let o=ye(e.type.schema).header_cell;for(let i=0;i<t.width;i++)if(((r=e.nodeAt(t.map[i+n*t.width]))==null?void 0:r.type)!=o)return!1;return!0}function If(t,{map:e,tableStart:n,table:r},o){var i;let s=n;for(let c=0;c<o;c++)s+=r.child(c).nodeSize;let l=[],a=o>0?-1:0;jb(e,r,o+a)&&(a=o==0||o==e.height?null:0);for(let c=0,u=e.width*o;c<e.width;c++,u++)if(o>0&&o<e.height&&e.map[u]==e.map[u-e.width]){let d=e.map[u],f=r.nodeAt(d).attrs;t.setNodeMarkup(n+d,null,{...f,rowspan:f.rowspan+1}),c+=f.colspan-1}else{let d=a==null?ye(r.type.schema).cell:(i=r.nodeAt(e.map[u+a*e.width]))==null?void 0:i.type,f=d?.createAndFill();f&&l.push(f)}return t.insert(s,ye(r.type.schema).row.create(null,l)),t}function Lf(t,e){if(!We(t))return!1;if(e){let n=nt(t);e(If(t.tr,n,n.top))}return!0}function Bf(t,e){if(!We(t))return!1;if(e){let n=nt(t);e(If(t.tr,n,n.bottom))}return!0}function Kb(t,{map:e,table:n,tableStart:r},o){let i=0;for(let c=0;c<o;c++)i+=n.child(c).nodeSize;let s=i+n.child(o).nodeSize,l=t.mapping.maps.length;t.delete(i+r,s+r);let a=new Set;for(let c=0,u=o*e.width;c<e.width;c++,u++){let d=e.map[u];if(!a.has(d)){if(a.add(d),o>0&&d==e.map[u-e.width]){let f=n.nodeAt(d).attrs;t.setNodeMarkup(t.mapping.slice(l).map(d+r),null,{...f,rowspan:f.rowspan-1}),c+=f.colspan-1}else if(o<e.height&&d==e.map[u+e.width]){let f=n.nodeAt(d),h=f.attrs,p=f.type.create({...h,rowspan:f.attrs.rowspan-1},f.content),m=e.positionAt(o+1,c,n);t.insert(t.mapping.slice(l).map(r+m),p),c+=h.colspan-1}}}}function zf(t,e){if(!We(t))return!1;if(e){let n=nt(t),r=t.tr;if(n.top==0&&n.bottom==n.map.height)return!1;for(let o=n.bottom-1;Kb(r,n,o),o!=n.top;o--){let i=n.tableStart?r.doc.nodeAt(n.tableStart-1):r.doc;if(!i)throw RangeError("No table found");n.table=i,n.map=ee.get(n.table)}e(r)}return!0}function yf(t){let e=t.content;return e.childCount==1&&e.child(0).isTextblock&&e.child(0).childCount==0}function Ub({width:t,height:e,map:n},r){let o=r.top*t+r.left,i=o,s=(r.bottom-1)*t+r.left,l=o+(r.right-r.left-1);for(let a=r.top;a<r.bottom;a++){if(r.left>0&&n[i]==n[i-1]||r.right<t&&n[l]==n[l+1])return!0;i+=t,l+=t}for(let a=r.left;a<r.right;a++){if(r.top>0&&n[o]==n[o-t]||r.bottom<e&&n[s]==n[s+t])return!0;o++,s++}return!1}function Vl(t,e){let n=t.selection;if(!(n instanceof K)||n.$anchorCell.pos==n.$headCell.pos)return!1;let r=nt(t),{map:o}=r;if(Ub(o,r))return!1;if(e){let i=t.tr,s={},l=w.empty,a,c;for(let u=r.top;u<r.bottom;u++)for(let d=r.left;d<r.right;d++){let f=o.map[u*o.width+d],h=r.table.nodeAt(f);if(!(s[f]||!h))if(s[f]=!0,a==null)a=f,c=h;else{yf(h)||(l=l.append(h.content));let p=i.mapping.map(f+r.tableStart);i.delete(p,p+h.nodeSize)}}if(a==null||c==null)return!0;if(i.setNodeMarkup(a+r.tableStart,null,{...Af(c.attrs,c.attrs.colspan,r.right-r.left-c.attrs.colspan),rowspan:r.bottom-r.top}),l.size){let u=a+1+c.content.size,d=yf(c)?a+1:u;i.replaceWith(d+r.tableStart,u+r.tableStart,l)}i.setSelection(new K(i.doc.resolve(a+r.tableStart))),e(i)}return!0}function Wl(t,e){let n=ye(t.schema);return Jb(({node:r})=>n[r.type.spec.tableRole])(t,e)}function Jb(t){return(e,n)=>{var r;let o=e.selection,i,s;if(o instanceof K){if(o.$anchorCell.pos!=o.$headCell.pos)return!1;i=o.$anchorCell.nodeAfter,s=o.$anchorCell.pos}else{if(i=Db(o.$from),!i)return!1;s=(r=vn(o.$from))==null?void 0:r.pos}if(i==null||s==null||i.attrs.colspan==1&&i.attrs.rowspan==1)return!1;if(n){let l=i.attrs,a=[],c=l.colwidth;l.rowspan>1&&(l={...l,rowspan:1}),l.colspan>1&&(l={...l,colspan:1});let u=nt(e),d=e.tr;for(let h=0;h<u.right-u.left;h++)a.push(c?{...l,colwidth:c&&c[h]?[c[h]]:null}:l);let f;for(let h=u.top;h<u.bottom;h++){let p=u.map.positionAt(h,u.left,u.table);h==u.top&&(p+=i.nodeSize);for(let m=u.left,g=0;m<u.right;m++,g++)m==u.left&&h==u.top||d.insert(f=d.mapping.map(p+u.tableStart,1),t({node:i,row:h,col:m}).createAndFill(a[g]))}d.setNodeMarkup(s,t({node:i,row:u.top,col:u.left}),a[0]),o instanceof K&&d.setSelection(new K(d.doc.resolve(o.$anchorCell.pos),f?d.doc.resolve(f):void 0)),n(d)}return!0}}function Hf(t,e){return function(n,r){if(!We(n))return!1;let o=oi(n);if(o.nodeAfter.attrs[t]===e)return!1;if(r){let i=n.tr;n.selection instanceof K?n.selection.forEachCell((s,l)=>{s.attrs[t]!==e&&i.setNodeMarkup(l,null,{...s.attrs,[t]:e})}):i.setNodeMarkup(o.pos,null,{...o.nodeAfter.attrs,[t]:e}),r(i)}return!0}}function qb(t){return function(e,n){if(!We(e))return!1;if(n){let r=ye(e.schema),o=nt(e),i=e.tr,s=o.map.cellsInRect(t=="column"?{left:o.left,top:0,right:o.right,bottom:o.map.height}:t=="row"?{left:0,top:o.top,right:o.map.width,bottom:o.bottom}:o),l=s.map(a=>o.table.nodeAt(a));for(let a=0;a<s.length;a++)l[a].type==r.header_cell&&i.setNodeMarkup(o.tableStart+s[a],r.cell,l[a].attrs);if(i.steps.length==0)for(let a=0;a<s.length;a++)i.setNodeMarkup(o.tableStart+s[a],r.header_cell,l[a].attrs);n(i)}return!0}}function bf(t,e,n){let r=e.map.cellsInRect({left:0,top:0,right:t=="row"?e.map.width:1,bottom:t=="column"?e.map.height:1});for(let o=0;o<r.length;o++){let i=e.table.nodeAt(r[o]);if(i&&i.type!==n.header_cell)return!1}return!0}function Tn(t,e){return e=e||{useDeprecatedLogic:!1},e.useDeprecatedLogic?qb(t):function(n,r){if(!We(n))return!1;if(r){let o=ye(n.schema),i=nt(n),s=n.tr,l=bf("row",i,o),a=bf("column",i,o),u=(t==="column"?l:t==="row"?a:!1)?1:0,d=t=="column"?{left:0,top:u,right:1,bottom:i.map.height}:t=="row"?{left:u,top:0,right:i.map.width,bottom:1}:i,f=t=="column"?a?o.cell:o.header_cell:t=="row"?l?o.cell:o.header_cell:o.cell;i.map.cellsInRect(d).forEach(h=>{let p=h+i.tableStart,m=s.doc.nodeAt(p);m&&s.setNodeMarkup(p,f,m.attrs)}),r(s)}return!0}}var eM=Tn("row",{useDeprecatedLogic:!0}),tM=Tn("column",{useDeprecatedLogic:!0}),Ff=Tn("cell",{useDeprecatedLogic:!0});function Gb(t,e){if(e<0){let n=t.nodeBefore;if(n)return t.pos-n.nodeSize;for(let r=t.index(-1)-1,o=t.before();r>=0;r--){let i=t.node(-1).child(r),s=i.lastChild;if(s)return o-1-s.nodeSize;o-=i.nodeSize}}else{if(t.index()<t.parent.childCount-1)return t.pos+t.nodeAfter.nodeSize;let n=t.node(-1);for(let r=t.indexAfter(-1),o=t.after();r<n.childCount;r++){let i=n.child(r);if(i.childCount)return o+1;o+=i.nodeSize}}return null}function _l(t){return function(e,n){if(!We(e))return!1;let r=Gb(oi(e),t);if(r==null)return!1;if(n){let o=e.doc.resolve(r);n(e.tr.setSelection(A.between(o,Ib(o))).scrollIntoView())}return!0}}function $f(t,e){let n=t.selection.$anchor;for(let r=n.depth;r>0;r--)if(n.node(r).type.spec.tableRole=="table")return e&&e(t.tr.delete(n.before(r),n.after(r)).scrollIntoView()),!0;return!1}function Zo(t,e){let n=t.selection;if(!(n instanceof K))return!1;if(e){let r=t.tr,o=ye(t.schema).cell.createAndFill().content;n.forEachCell((i,s)=>{i.content.eq(o)||r.replace(r.mapping.map(s+1),r.mapping.map(s+i.nodeSize-1),new M(o,0,0))}),r.docChanged&&e(r)}return!0}function Yb(t){if(!t.size)return null;let{content:e,openStart:n,openEnd:r}=t;for(;e.childCount==1&&(n>0&&r>0||e.child(0).type.spec.tableRole=="table");)n--,r--,e=e.child(0).content;let o=e.child(0),i=o.type.spec.tableRole,s=o.type.schema,l=[];if(i=="row")for(let a=0;a<e.childCount;a++){let c=e.child(a).content,u=a?0:Math.max(0,n-1),d=a<e.childCount-1?0:Math.max(0,r-1);(u||d)&&(c=zl(ye(s).row,new M(c,u,d)).content),l.push(c)}else if(i=="cell"||i=="header_cell")l.push(n||r?zl(ye(s).row,new M(e,n,r)).content:e);else return null;return Xb(s,l)}function Xb(t,e){let n=[];for(let o=0;o<e.length;o++){let i=e[o];for(let s=i.childCount-1;s>=0;s--){let{rowspan:l,colspan:a}=i.child(s).attrs;for(let c=o;c<o+l;c++)n[c]=(n[c]||0)+a}}let r=0;for(let o=0;o<n.length;o++)r=Math.max(r,n[o]);for(let o=0;o<n.length;o++)if(o>=e.length&&e.push(w.empty),n[o]<r){let i=ye(t).cell.createAndFill(),s=[];for(let l=n[o];l<r;l++)s.push(i);e[o]=e[o].append(w.from(s))}return{height:e.length,width:r,rows:e}}function zl(t,e){let n=t.createAndFill();return new kt(n).replace(0,n.content.size,e).doc}function Qb({width:t,height:e,rows:n},r,o){if(t!=r){let i=[],s=[];for(let l=0;l<n.length;l++){let a=n[l],c=[];for(let u=i[l]||0,d=0;u<r;d++){let f=a.child(d%a.childCount);u+f.attrs.colspan>r&&(f=f.type.createChecked(Zt(f.attrs,f.attrs.colspan,u+f.attrs.colspan-r),f.content)),c.push(f),u+=f.attrs.colspan;for(let h=1;h<f.attrs.rowspan;h++)i[l+h]=(i[l+h]||0)+f.attrs.colspan}s.push(w.from(c))}n=s,t=r}if(e!=o){let i=[];for(let s=0,l=0;s<o;s++,l++){let a=[],c=n[l%e];for(let u=0;u<c.childCount;u++){let d=c.child(u);s+d.attrs.rowspan>o&&(d=d.type.create({...d.attrs,rowspan:Math.max(1,o-d.attrs.rowspan)},d.content)),a.push(d)}i.push(w.from(a))}n=i,e=o}return{width:t,height:e,rows:n}}function Zb(t,e,n,r,o,i,s){let l=t.doc.type.schema,a=ye(l),c,u;if(o>e.width)for(let d=0,f=0;d<e.height;d++){let h=n.child(d);f+=h.nodeSize;let p=[],m;h.lastChild==null||h.lastChild.type==a.cell?m=c||(c=a.cell.createAndFill()):m=u||(u=a.header_cell.createAndFill());for(let g=e.width;g<o;g++)p.push(m);t.insert(t.mapping.slice(s).map(f-1+r),p)}if(i>e.height){let d=[];for(let p=0,m=(e.height-1)*e.width;p<Math.max(e.width,o);p++){let g=p>=e.width?!1:n.nodeAt(e.map[m+p]).type==a.header_cell;d.push(g?u||(u=a.header_cell.createAndFill()):c||(c=a.cell.createAndFill()))}let f=a.row.create(null,w.from(d)),h=[];for(let p=e.height;p<i;p++)h.push(f);t.insert(t.mapping.slice(s).map(r+n.nodeSize-2),h)}return!!(c||u)}function xf(t,e,n,r,o,i,s,l){if(s==0||s==e.height)return!1;let a=!1;for(let c=o;c<i;c++){let u=s*e.width+c,d=e.map[u];if(e.map[u-e.width]==d){a=!0;let f=n.nodeAt(d),{top:h,left:p}=e.findCell(d);t.setNodeMarkup(t.mapping.slice(l).map(d+r),null,{...f.attrs,rowspan:s-h}),t.insert(t.mapping.slice(l).map(e.positionAt(s,p,n)),f.type.createAndFill({...f.attrs,rowspan:h+f.attrs.rowspan-s})),c+=f.attrs.colspan-1}}return a}function wf(t,e,n,r,o,i,s,l){if(s==0||s==e.width)return!1;let a=!1;for(let c=o;c<i;c++){let u=c*e.width+s,d=e.map[u];if(e.map[u-1]==d){a=!0;let f=n.nodeAt(d),h=e.colCount(d),p=t.mapping.slice(l).map(d+r);t.setNodeMarkup(p,null,Zt(f.attrs,s-h,f.attrs.colspan-(s-h))),t.insert(p+f.nodeSize,f.type.createAndFill(Zt(f.attrs,0,s-h))),c+=f.attrs.rowspan-1}}return a}function Sf(t,e,n,r,o){let i=n?t.doc.nodeAt(n-1):t.doc;if(!i)throw new Error("No table found");let s=ee.get(i),{top:l,left:a}=r,c=a+o.width,u=l+o.height,d=t.tr,f=0;function h(){if(i=n?d.doc.nodeAt(n-1):d.doc,!i)throw new Error("No table found");s=ee.get(i),f=d.mapping.maps.length}Zb(d,s,i,n,c,u,f)&&h(),xf(d,s,i,n,a,c,l,f)&&h(),xf(d,s,i,n,a,c,u,f)&&h(),wf(d,s,i,n,l,u,a,f)&&h(),wf(d,s,i,n,l,u,c,f)&&h();for(let p=l;p<u;p++){let m=s.positionAt(p,a,i),g=s.positionAt(p,c,i);d.replace(d.mapping.slice(f).map(m+n),d.mapping.slice(f).map(g+n),new M(o.rows[p-l],0,0))}h(),d.setSelection(new K(d.doc.resolve(n+s.positionAt(l,a,i)),d.doc.resolve(n+s.positionAt(u-1,c-1,i)))),e(d)}var ex=Xn({ArrowLeft:ei("horiz",-1),ArrowRight:ei("horiz",1),ArrowUp:ei("vert",-1),ArrowDown:ei("vert",1),"Shift-ArrowLeft":ti("horiz",-1),"Shift-ArrowRight":ti("horiz",1),"Shift-ArrowUp":ti("vert",-1),"Shift-ArrowDown":ti("vert",1),Backspace:Zo,"Mod-Backspace":Zo,Delete:Zo,"Mod-Delete":Zo});function ni(t,e,n){return n.eq(t.selection)?!1:(e&&e(t.tr.setSelection(n).scrollIntoView()),!0)}function ei(t,e){return(n,r,o)=>{if(!o)return!1;let i=n.selection;if(i instanceof K)return ni(n,r,E.near(i.$headCell,e));if(t!="horiz"&&!i.empty)return!1;let s=Vf(o,t,e);if(s==null)return!1;if(t=="horiz")return ni(n,r,E.near(n.doc.resolve(i.head+e),e));{let l=n.doc.resolve(s),a=Tf(l,t,e),c;return a?c=E.near(a,1):e<0?c=E.near(n.doc.resolve(l.before(-1)),-1):c=E.near(n.doc.resolve(l.after(-1)),1),ni(n,r,c)}}}function ti(t,e){return(n,r,o)=>{if(!o)return!1;let i=n.selection,s;if(i instanceof K)s=i;else{let a=Vf(o,t,e);if(a==null)return!1;s=new K(n.doc.resolve(a))}let l=Tf(s.$headCell,t,e);return l?ni(n,r,new K(s.$anchorCell,l)):!1}}function tx(t,e){let n=t.state.doc,r=vn(n.resolve(e));return r?(t.dispatch(t.state.tr.setSelection(new K(r))),!0):!1}function nx(t,e,n){if(!We(t.state))return!1;let r=Yb(n),o=t.state.selection;if(o instanceof K){r||(r={width:1,height:1,rows:[w.from(zl(ye(t.state.schema).cell,n))]});let i=o.$anchorCell.node(-1),s=o.$anchorCell.start(-1),l=ee.get(i).rectBetween(o.$anchorCell.pos-s,o.$headCell.pos-s);return r=Qb(r,l.right-l.left,l.bottom-l.top),Sf(t.state,t.dispatch,s,l,r),!0}else if(r){let i=oi(t.state),s=i.start(-1);return Sf(t.state,t.dispatch,s,ee.get(i.node(-1)).findCell(i.pos-s),r),!0}else return!1}function rx(t,e){var n;if(e.ctrlKey||e.metaKey)return;let r=kf(t,e.target),o;if(e.shiftKey&&t.state.selection instanceof K)i(t.state.selection.$anchorCell,e),e.preventDefault();else if(e.shiftKey&&r&&(o=vn(t.state.selection.$anchor))!=null&&((n=Pl(t,e))==null?void 0:n.pos)!=o.pos)i(o,e),e.preventDefault();else if(!r)return;function i(a,c){let u=Pl(t,c),d=Pt.getState(t.state)==null;if(!u||!Fl(a,u))if(d)u=a;else return;let f=new K(a,u);if(d||!t.state.selection.eq(f)){let h=t.state.tr.setSelection(f);d&&h.setMeta(Pt,a.pos),t.dispatch(h)}}function s(){t.root.removeEventListener("mouseup",s),t.root.removeEventListener("dragstart",s),t.root.removeEventListener("mousemove",l),Pt.getState(t.state)!=null&&t.dispatch(t.state.tr.setMeta(Pt,-1))}function l(a){let c=a,u=Pt.getState(t.state),d;if(u!=null)d=t.state.doc.resolve(u);else if(kf(t,c.target)!=r&&(d=Pl(t,e),!d))return s();d&&i(d,c)}t.root.addEventListener("mouseup",s),t.root.addEventListener("dragstart",s),t.root.addEventListener("mousemove",l)}function Vf(t,e,n){if(!(t.state.selection instanceof A))return null;let{$head:r}=t.state.selection;for(let o=r.depth-1;o>=0;o--){let i=r.node(o);if((n<0?r.index(o):r.indexAfter(o))!=(n<0?0:i.childCount))return null;if(i.type.spec.tableRole=="cell"||i.type.spec.tableRole=="header_cell"){let l=r.before(o),a=e=="vert"?n>0?"down":"up":n>0?"right":"left";return t.endOfTextblock(a)?l:null}}return null}function kf(t,e){for(;e&&e!=t.dom;e=e.parentNode)if(e.nodeName=="TD"||e.nodeName=="TH")return e;return null}function Pl(t,e){let n=t.posAtCoords({left:e.clientX,top:e.clientY});return n&&n?vn(t.state.doc.resolve(n.pos)):null}var ox=class{constructor(t,e){this.node=t,this.defaultCellMinWidth=e,this.dom=document.createElement("div"),this.dom.className="tableWrapper",this.table=this.dom.appendChild(document.createElement("table")),this.table.style.setProperty("--default-cell-min-width",`${e}px`),this.colgroup=this.table.appendChild(document.createElement("colgroup")),Hl(t,this.colgroup,this.table,e),this.contentDOM=this.table.appendChild(document.createElement("tbody"))}update(t){return t.type!=this.node.type?!1:(this.node=t,Hl(t,this.colgroup,this.table,this.defaultCellMinWidth),!0)}ignoreMutation(t){return t.type=="attributes"&&(t.target==this.table||this.colgroup.contains(t.target))}};function Hl(t,e,n,r,o,i){var s;let l=0,a=!0,c=e.firstChild,u=t.firstChild;if(u){for(let d=0,f=0;d<u.childCount;d++){let{colspan:h,colwidth:p}=u.child(d).attrs;for(let m=0;m<h;m++,f++){let g=o==f?i:p&&p[m],y=g?g+"px":"";if(l+=g||r,g||(a=!1),c)c.style.width!=y&&(c.style.width=y),c=c.nextSibling;else{let b=document.createElement("col");b.style.width=y,e.appendChild(b)}}}for(;c;){let d=c.nextSibling;(s=c.parentNode)==null||s.removeChild(c),c=d}a?(n.style.width=l+"px",n.style.minWidth=""):(n.style.width="",n.style.minWidth=l+"px")}}var Ee=new L("tableColumnResizing");function Wf({handleWidth:t=5,cellMinWidth:e=25,defaultCellMinWidth:n=100,View:r=ox,lastColumnResizable:o=!0}={}){let i=new N({key:Ee,state:{init(s,l){var a,c;let u=(c=(a=i.spec)==null?void 0:a.props)==null?void 0:c.nodeViews,d=ye(l.schema).table.name;return r&&u&&(u[d]=(f,h)=>new r(f,n,h)),new ix(-1,!1)},apply(s,l){return l.apply(s)}},props:{attributes:s=>{let l=Ee.getState(s);return l&&l.activeHandle>-1?{class:"resize-cursor"}:{}},handleDOMEvents:{mousemove:(s,l)=>{sx(s,l,t,o)},mouseleave:s=>{lx(s)},mousedown:(s,l)=>{ax(s,l,e,n)}},decorations:s=>{let l=Ee.getState(s);if(l&&l.activeHandle>-1)return hx(s,l.activeHandle)},nodeViews:{}}});return i}var ix=class ri{constructor(e,n){this.activeHandle=e,this.dragging=n}apply(e){let n=this,r=e.getMeta(Ee);if(r&&r.setHandle!=null)return new ri(r.setHandle,!1);if(r&&r.setDragging!==void 0)return new ri(n.activeHandle,r.setDragging);if(n.activeHandle>-1&&e.docChanged){let o=e.mapping.map(n.activeHandle,-1);return Bl(e.doc.resolve(o))||(o=-1),new ri(o,n.dragging)}return n}};function sx(t,e,n,r){if(!t.editable)return;let o=Ee.getState(t.state);if(o&&!o.dragging){let i=ux(e.target),s=-1;if(i){let{left:l,right:a}=i.getBoundingClientRect();e.clientX-l<=n?s=Cf(t,e,"left",n):a-e.clientX<=n&&(s=Cf(t,e,"right",n))}if(s!=o.activeHandle){if(!r&&s!==-1){let l=t.state.doc.resolve(s),a=l.node(-1),c=ee.get(a),u=l.start(-1);if(c.colCount(l.pos-u)+l.nodeAfter.attrs.colspan-1==c.width-1)return}_f(t,s)}}}function lx(t){if(!t.editable)return;let e=Ee.getState(t.state);e&&e.activeHandle>-1&&!e.dragging&&_f(t,-1)}function ax(t,e,n,r){var o;if(!t.editable)return!1;let i=(o=t.dom.ownerDocument.defaultView)!=null?o:window,s=Ee.getState(t.state);if(!s||s.activeHandle==-1||s.dragging)return!1;let l=t.state.doc.nodeAt(s.activeHandle),a=cx(t,s.activeHandle,l.attrs);t.dispatch(t.state.tr.setMeta(Ee,{setDragging:{startX:e.clientX,startWidth:a}}));function c(d){i.removeEventListener("mouseup",c),i.removeEventListener("mousemove",u);let f=Ee.getState(t.state);f?.dragging&&(dx(t,f.activeHandle,Mf(f.dragging,d,n)),t.dispatch(t.state.tr.setMeta(Ee,{setDragging:null})))}function u(d){if(!d.which)return c(d);let f=Ee.getState(t.state);if(f&&f.dragging){let h=Mf(f.dragging,d,n);vf(t,f.activeHandle,h,r)}}return vf(t,s.activeHandle,a,r),i.addEventListener("mouseup",c),i.addEventListener("mousemove",u),e.preventDefault(),!0}function cx(t,e,{colspan:n,colwidth:r}){let o=r&&r[r.length-1];if(o)return o;let i=t.domAtPos(e),l=i.node.childNodes[i.offset].offsetWidth,a=n;if(r)for(let c=0;c<n;c++)r[c]&&(l-=r[c],a--);return l/a}function ux(t){for(;t&&t.nodeName!="TD"&&t.nodeName!="TH";)t=t.classList&&t.classList.contains("ProseMirror")?null:t.parentNode;return t}function Cf(t,e,n,r){let o=n=="right"?-r:r,i=t.posAtCoords({left:e.clientX+o,top:e.clientY});if(!i)return-1;let{pos:s}=i,l=vn(t.state.doc.resolve(s));if(!l)return-1;if(n=="right")return l.pos;let a=ee.get(l.node(-1)),c=l.start(-1),u=a.map.indexOf(l.pos-c);return u%a.width==0?-1:c+a.map[u-1]}function Mf(t,e,n){let r=e.clientX-t.startX;return Math.max(n,t.startWidth+r)}function _f(t,e){t.dispatch(t.state.tr.setMeta(Ee,{setHandle:e}))}function dx(t,e,n){let r=t.state.doc.resolve(e),o=r.node(-1),i=ee.get(o),s=r.start(-1),l=i.colCount(r.pos-s)+r.nodeAfter.attrs.colspan-1,a=t.state.tr;for(let c=0;c<i.height;c++){let u=c*i.width+l;if(c&&i.map[u]==i.map[u-i.width])continue;let d=i.map[u],f=o.nodeAt(d).attrs,h=f.colspan==1?0:l-i.colCount(d);if(f.colwidth&&f.colwidth[h]==n)continue;let p=f.colwidth?f.colwidth.slice():fx(f.colspan);p[h]=n,a.setNodeMarkup(s+d,null,{...f,colwidth:p})}a.docChanged&&t.dispatch(a)}function vf(t,e,n,r){let o=t.state.doc.resolve(e),i=o.node(-1),s=o.start(-1),l=ee.get(i).colCount(o.pos-s)+o.nodeAfter.attrs.colspan-1,a=t.domAtPos(o.start(-1)).node;for(;a&&a.nodeName!="TABLE";)a=a.parentNode;a&&Hl(i,a.firstChild,a,r,l,n)}function fx(t){return Array(t).fill(0)}function hx(t,e){var n;let r=[],o=t.doc.resolve(e),i=o.node(-1);if(!i)return J.empty;let s=ee.get(i),l=o.start(-1),a=s.colCount(o.pos-l)+o.nodeAfter.attrs.colspan-1;for(let c=0;c<s.height;c++){let u=a+c*s.width;if((a==s.width-1||s.map[u]!=s.map[u+1])&&(c==0||s.map[u]!=s.map[u-s.width])){let d=s.map[u],f=l+d+i.nodeAt(d).nodeSize-1,h=document.createElement("div");h.className="column-resize-handle",(n=Ee.getState(t))!=null&&n.dragging&&r.push(Y.node(l+d,l+d+i.nodeAt(d).nodeSize,{class:"column-resize-dragging"})),r.push(Y.widget(f,h))}}return J.create(t.doc,r)}function jf({allowTableNodeSelection:t=!1}={}){return new N({key:Pt,state:{init(){return null},apply(e,n){let r=e.getMeta(Pt);if(r!=null)return r==-1?null:r;if(n==null||!e.docChanged)return n;let{deleted:o,pos:i}=e.mapping.mapResult(n);return o?null:i}},props:{decorations:zb,handleDOMEvents:{mousedown:rx},createSelectionBetween(e){return Pt.getState(e.state)!=null?e.state.selection:null},handleTripleClick:tx,handleKeyDown:ex,handlePaste:nx},appendTransaction(e,n,r){return $b(r,$l(r,n),t)}})}var px=z.create({name:"tableCell",addOptions(){return{HTMLAttributes:{}}},content:"block+",addAttributes(){return{colspan:{default:1},rowspan:{default:1},colwidth:{default:null,parseHTML:t=>{let e=t.getAttribute("colwidth");return e?e.split(",").map(r=>parseInt(r,10)):null}}}},tableRole:"cell",isolating:!0,parseHTML(){return[{tag:"td"}]},renderHTML({HTMLAttributes:t}){return["td",T(this.options.HTMLAttributes,t),0]}}),mx=z.create({name:"tableHeader",addOptions(){return{HTMLAttributes:{}}},content:"block+",addAttributes(){return{colspan:{default:1},rowspan:{default:1},colwidth:{default:null,parseHTML:t=>{let e=t.getAttribute("colwidth");return e?e.split(",").map(r=>parseInt(r,10)):null}}}},tableRole:"header_cell",isolating:!0,parseHTML(){return[{tag:"th"}]},renderHTML({HTMLAttributes:t}){return["th",T(this.options.HTMLAttributes,t),0]}}),gx=z.create({name:"tableRow",addOptions(){return{HTMLAttributes:{}}},content:"(tableCell | tableHeader)*",tableRole:"row",parseHTML(){return[{tag:"tr"}]},renderHTML({HTMLAttributes:t}){return["tr",T(this.options.HTMLAttributes,t),0]}});function jl(t,e){return e?["width",`${Math.max(e,t)}px`]:["min-width",`${t}px`]}function Kf(t,e,n,r,o,i){var s;let l=0,a=!0,c=e.firstChild,u=t.firstChild;if(u!==null)for(let d=0,f=0;d<u.childCount;d+=1){let{colspan:h,colwidth:p}=u.child(d).attrs;for(let m=0;m<h;m+=1,f+=1){let g=o===f?i:p&&p[m],y=g?`${g}px`:"";if(l+=g||r,g||(a=!1),c){if(c.style.width!==y){let[b,x]=jl(r,g);c.style.setProperty(b,x)}c=c.nextSibling}else{let b=document.createElement("col"),[x,C]=jl(r,g);b.style.setProperty(x,C),e.appendChild(b)}}}for(;c;){let d=c.nextSibling;(s=c.parentNode)==null||s.removeChild(c),c=d}a?(n.style.width=`${l}px`,n.style.minWidth=""):(n.style.width="",n.style.minWidth=`${l}px`)}var yx=class{constructor(t,e){this.node=t,this.cellMinWidth=e,this.dom=document.createElement("div"),this.dom.className="tableWrapper",this.table=this.dom.appendChild(document.createElement("table")),this.colgroup=this.table.appendChild(document.createElement("colgroup")),Kf(t,this.colgroup,this.table,e),this.contentDOM=this.table.appendChild(document.createElement("tbody"))}update(t){return t.type!==this.node.type?!1:(this.node=t,Kf(t,this.colgroup,this.table,this.cellMinWidth),!0)}ignoreMutation(t){return t.type==="attributes"&&(t.target===this.table||this.colgroup.contains(t.target))}};function bx(t,e,n,r){let o=0,i=!0,s=[],l=t.firstChild;if(!l)return{};for(let d=0,f=0;d<l.childCount;d+=1){let{colspan:h,colwidth:p}=l.child(d).attrs;for(let m=0;m<h;m+=1,f+=1){let g=n===f?r:p&&p[m];o+=g||e,g||(i=!1);let[y,b]=jl(e,g);s.push(["col",{style:`${y}: ${b}`}])}}let a=i?`${o}px`:"",c=i?"":`${o}px`;return{colgroup:["colgroup",{},...s],tableWidth:a,tableMinWidth:c}}function Uf(t,e){return e?t.createChecked(null,e):t.createAndFill()}function xx(t){if(t.cached.tableNodeTypes)return t.cached.tableNodeTypes;let e={};return Object.keys(t.nodes).forEach(n=>{let r=t.nodes[n];r.spec.tableRole&&(e[r.spec.tableRole]=r)}),t.cached.tableNodeTypes=e,e}function wx(t,e,n,r,o){let i=xx(t),s=[],l=[];for(let c=0;c<n;c+=1){let u=Uf(i.cell,o);if(u&&l.push(u),r){let d=Uf(i.header_cell,o);d&&s.push(d)}}let a=[];for(let c=0;c<e;c+=1)a.push(i.row.createChecked(null,r&&c===0?s:l));return i.table.createChecked(null,a)}function Sx(t){return t instanceof K}var ii=({editor:t})=>{let{selection:e}=t.state;if(!Sx(e))return!1;let n=0,r=Fs(e.ranges[0].$from,i=>i.type.name==="table");return r?.node.descendants(i=>{if(i.type.name==="table")return!1;["tableCell","tableHeader"].includes(i.type.name)&&(n+=1)}),n===e.ranges.length?(t.commands.deleteTable(),!0):!1},kx=z.create({name:"table",addOptions(){return{HTMLAttributes:{},resizable:!1,handleWidth:5,cellMinWidth:25,View:yx,lastColumnResizable:!0,allowTableNodeSelection:!1}},content:"tableRow+",tableRole:"table",isolating:!0,group:"block",parseHTML(){return[{tag:"table"}]},renderHTML({node:t,HTMLAttributes:e}){let{colgroup:n,tableWidth:r,tableMinWidth:o}=bx(t,this.options.cellMinWidth);return["table",T(this.options.HTMLAttributes,e,{style:r?`width: ${r}`:`min-width: ${o}`}),n,["tbody",0]]},addCommands(){return{insertTable:({rows:t=3,cols:e=3,withHeaderRow:n=!0}={})=>({tr:r,dispatch:o,editor:i})=>{let s=wx(i.schema,t,e,n);if(o){let l=r.selection.from+1;r.replaceSelectionWith(s).scrollIntoView().setSelection(A.near(r.doc.resolve(l)))}return!0},addColumnBefore:()=>({state:t,dispatch:e})=>Rf(t,e),addColumnAfter:()=>({state:t,dispatch:e})=>Df(t,e),deleteColumn:()=>({state:t,dispatch:e})=>Pf(t,e),addRowBefore:()=>({state:t,dispatch:e})=>Lf(t,e),addRowAfter:()=>({state:t,dispatch:e})=>Bf(t,e),deleteRow:()=>({state:t,dispatch:e})=>zf(t,e),deleteTable:()=>({state:t,dispatch:e})=>$f(t,e),mergeCells:()=>({state:t,dispatch:e})=>Vl(t,e),splitCell:()=>({state:t,dispatch:e})=>Wl(t,e),toggleHeaderColumn:()=>({state:t,dispatch:e})=>Tn("column")(t,e),toggleHeaderRow:()=>({state:t,dispatch:e})=>Tn("row")(t,e),toggleHeaderCell:()=>({state:t,dispatch:e})=>Ff(t,e),mergeOrSplit:()=>({state:t,dispatch:e})=>Vl(t,e)?!0:Wl(t,e),setCellAttribute:(t,e)=>({state:n,dispatch:r})=>Hf(t,e)(n,r),goToNextCell:()=>({state:t,dispatch:e})=>_l(1)(t,e),goToPreviousCell:()=>({state:t,dispatch:e})=>_l(-1)(t,e),fixTables:()=>({state:t,dispatch:e})=>(e&&$l(t),!0),setCellSelection:t=>({tr:e,dispatch:n})=>{if(n){let r=K.create(e.doc,t.anchorCell,t.headCell);e.setSelection(r)}return!0}}},addKeyboardShortcuts(){return{Tab:()=>this.editor.commands.goToNextCell()?!0:this.editor.can().addRowAfter()?this.editor.chain().addRowAfter().goToNextCell().run():!1,"Shift-Tab":()=>this.editor.commands.goToPreviousCell(),Backspace:ii,"Mod-Backspace":ii,Delete:ii,"Mod-Delete":ii}},addProseMirrorPlugins(){return[...this.options.resizable&&this.editor.isEditable?[Wf({handleWidth:this.options.handleWidth,cellMinWidth:this.options.cellMinWidth,defaultCellMinWidth:this.options.cellMinWidth,View:this.options.View,lastColumnResizable:this.options.lastColumnResizable})]:[],jf({allowTableNodeSelection:this.options.allowTableNodeSelection})]},extendNodeSchema(t){let e={name:t.name,options:t.options,storage:t.storage};return{tableRole:W(I(t,"tableRole",e))}}}),Jf=$.create({name:"tableKit",addExtensions(){let t=[];return this.options.table!==!1&&t.push(kx.configure(this.options.table)),this.options.tableCell!==!1&&t.push(px.configure(this.options.tableCell)),this.options.tableHeader!==!1&&t.push(mx.configure(this.options.tableHeader)),this.options.tableRow!==!1&&t.push(gx.configure(this.options.tableRow)),t}});var Cx=z.create({name:"text",group:"inline"}),qf=Cx;var Mx=$.create({name:"textAlign",addOptions(){return{types:[],alignments:["left","center","right","justify"],defaultAlignment:null}},addGlobalAttributes(){return[{types:this.options.types,attributes:{textAlign:{default:this.options.defaultAlignment,parseHTML:t=>{let e=t.style.textAlign;return this.options.alignments.includes(e)?e:this.options.defaultAlignment},renderHTML:t=>t.textAlign?{style:`text-align: ${t.textAlign}`}:{}}}}]},addCommands(){return{setTextAlign:t=>({commands:e})=>this.options.alignments.includes(t)?this.options.types.map(n=>e.updateAttributes(n,{textAlign:t})).every(n=>n):!1,unsetTextAlign:()=>({commands:t})=>this.options.types.map(e=>t.resetAttributes(e,"textAlign")).every(e=>e),toggleTextAlign:t=>({editor:e,commands:n})=>this.options.alignments.includes(t)?e.isActive({textAlign:t})?n.unsetTextAlign():n.setTextAlign(t):!1}},addKeyboardShortcuts(){return{"Mod-Shift-l":()=>this.editor.commands.setTextAlign("left"),"Mod-Shift-e":()=>this.editor.commands.setTextAlign("center"),"Mod-Shift-r":()=>this.editor.commands.setTextAlign("right"),"Mod-Shift-j":()=>this.editor.commands.setTextAlign("justify")}}}),Gf=Mx;var vx=te.create({name:"underline",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"u"},{style:"text-decoration",consuming:!1,getAttrs:t=>t.includes("underline")?{}:!1}]},renderHTML({HTMLAttributes:t}){return["u",T(this.options.HTMLAttributes,t),0]},addCommands(){return{setUnderline:()=>({commands:t})=>t.setMark(this.name),toggleUnderline:()=>({commands:t})=>t.toggleMark(this.name),unsetUnderline:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-u":()=>this.editor.commands.toggleUnderline(),"Mod-U":()=>this.editor.commands.toggleUnderline()}}}),Yf=vx;var Kl=["top","right","bottom","left"],Xf=["start","end"],Ul=Kl.reduce((t,e)=>t.concat(e,e+"-"+Xf[0],e+"-"+Xf[1]),[]),ze=Math.min,fe=Math.max,gr=Math.round;var _e=t=>({x:t,y:t}),Tx={left:"right",right:"left",bottom:"top",top:"bottom"},Ax={start:"end",end:"start"};function si(t,e,n){return fe(t,ze(e,n))}function rt(t,e){return typeof t=="function"?t(e):t}function Oe(t){return t.split("-")[0]}function He(t){return t.split("-")[1]}function Jl(t){return t==="x"?"y":"x"}function li(t){return t==="y"?"height":"width"}var Ex=new Set(["top","bottom"]);function je(t){return Ex.has(Oe(t))?"y":"x"}function ai(t){return Jl(je(t))}function ql(t,e,n){n===void 0&&(n=!1);let r=He(t),o=ai(t),i=li(o),s=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return e.reference[i]>e.floating[i]&&(s=mr(s)),[s,mr(s)]}function eh(t){let e=mr(t);return[pr(t),e,pr(e)]}function pr(t){return t.replace(/start|end/g,e=>Ax[e])}var Qf=["left","right"],Zf=["right","left"],Ox=["top","bottom"],Nx=["bottom","top"];function Rx(t,e,n){switch(t){case"top":case"bottom":return n?e?Zf:Qf:e?Qf:Zf;case"left":case"right":return e?Ox:Nx;default:return[]}}function th(t,e,n,r){let o=He(t),i=Rx(Oe(t),n==="start",r);return o&&(i=i.map(s=>s+"-"+o),e&&(i=i.concat(i.map(pr)))),i}function mr(t){return t.replace(/left|right|bottom|top/g,e=>Tx[e])}function Dx(t){return{top:0,right:0,bottom:0,left:0,...t}}function ci(t){return typeof t!="number"?Dx(t):{top:t,right:t,bottom:t,left:t}}function wt(t){let{x:e,y:n,width:r,height:o}=t;return{width:r,height:o,top:n,left:e,right:e+r,bottom:n+o,x:e,y:n}}function nh(t,e,n){let{reference:r,floating:o}=t,i=je(e),s=ai(e),l=li(s),a=Oe(e),c=i==="y",u=r.x+r.width/2-o.width/2,d=r.y+r.height/2-o.height/2,f=r[l]/2-o[l]/2,h;switch(a){case"top":h={x:u,y:r.y-o.height};break;case"bottom":h={x:u,y:r.y+r.height};break;case"right":h={x:r.x+r.width,y:d};break;case"left":h={x:r.x-o.width,y:d};break;default:h={x:r.x,y:r.y}}switch(He(e)){case"start":h[s]-=f*(n&&c?-1:1);break;case"end":h[s]+=f*(n&&c?-1:1);break}return h}var ih=async(t,e,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:s}=n,l=i.filter(Boolean),a=await(s.isRTL==null?void 0:s.isRTL(e)),c=await s.getElementRects({reference:t,floating:e,strategy:o}),{x:u,y:d}=nh(c,r,a),f=r,h={},p=0;for(let m=0;m<l.length;m++){let{name:g,fn:y}=l[m],{x:b,y:x,data:C,reset:S}=await y({x:u,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:h,rects:c,platform:s,elements:{reference:t,floating:e}});u=b??u,d=x??d,h={...h,[g]:{...h[g],...C}},S&&p<=50&&(p++,typeof S=="object"&&(S.placement&&(f=S.placement),S.rects&&(c=S.rects===!0?await s.getElementRects({reference:t,floating:e,strategy:o}):S.rects),{x:u,y:d}=nh(c,f,a)),m=-1)}return{x:u,y:d,placement:f,strategy:o,middlewareData:h}};async function en(t,e){var n;e===void 0&&(e={});let{x:r,y:o,platform:i,rects:s,elements:l,strategy:a}=t,{boundary:c="clippingAncestors",rootBoundary:u="viewport",elementContext:d="floating",altBoundary:f=!1,padding:h=0}=rt(e,t),p=ci(h),g=l[f?d==="floating"?"reference":"floating":d],y=wt(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(g)))==null||n?g:g.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(l.floating)),boundary:c,rootBoundary:u,strategy:a})),b=d==="floating"?{x:r,y:o,width:s.floating.width,height:s.floating.height}:s.reference,x=await(i.getOffsetParent==null?void 0:i.getOffsetParent(l.floating)),C=await(i.isElement==null?void 0:i.isElement(x))?await(i.getScale==null?void 0:i.getScale(x))||{x:1,y:1}:{x:1,y:1},S=wt(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:b,offsetParent:x,strategy:a}):b);return{top:(y.top-S.top+p.top)/C.y,bottom:(S.bottom-y.bottom+p.bottom)/C.y,left:(y.left-S.left+p.left)/C.x,right:(S.right-y.right+p.right)/C.x}}var sh=t=>({name:"arrow",options:t,async fn(e){let{x:n,y:r,placement:o,rects:i,platform:s,elements:l,middlewareData:a}=e,{element:c,padding:u=0}=rt(t,e)||{};if(c==null)return{};let d=ci(u),f={x:n,y:r},h=ai(o),p=li(h),m=await s.getDimensions(c),g=h==="y",y=g?"top":"left",b=g?"bottom":"right",x=g?"clientHeight":"clientWidth",C=i.reference[p]+i.reference[h]-f[h]-i.floating[p],S=f[h]-i.reference[h],R=await(s.getOffsetParent==null?void 0:s.getOffsetParent(c)),P=R?R[x]:0;(!P||!await(s.isElement==null?void 0:s.isElement(R)))&&(P=l.floating[x]||i.floating[p]);let v=C/2-S/2,B=P/2-m[p]/2-1,D=ze(d[y],B),_=ze(d[b],B),H=D,j=P-m[p]-_,U=P/2-m[p]/2+v,oe=si(H,U,j),X=!a.arrow&&He(o)!=null&&U!==oe&&i.reference[p]/2-(U<H?D:_)-m[p]/2<0,G=X?U<H?U-H:U-j:0;return{[h]:f[h]+G,data:{[h]:oe,centerOffset:U-oe-G,...X&&{alignmentOffset:G}},reset:X}}});function Px(t,e,n){return(t?[...n.filter(o=>He(o)===t),...n.filter(o=>He(o)!==t)]:n.filter(o=>Oe(o)===o)).filter(o=>t?He(o)===t||(e?pr(o)!==o:!1):!0)}var lh=function(t){return t===void 0&&(t={}),{name:"autoPlacement",options:t,async fn(e){var n,r,o;let{rects:i,middlewareData:s,placement:l,platform:a,elements:c}=e,{crossAxis:u=!1,alignment:d,allowedPlacements:f=Ul,autoAlignment:h=!0,...p}=rt(t,e),m=d!==void 0||f===Ul?Px(d||null,h,f):f,g=await en(e,p),y=((n=s.autoPlacement)==null?void 0:n.index)||0,b=m[y];if(b==null)return{};let x=ql(b,i,await(a.isRTL==null?void 0:a.isRTL(c.floating)));if(l!==b)return{reset:{placement:m[0]}};let C=[g[Oe(b)],g[x[0]],g[x[1]]],S=[...((r=s.autoPlacement)==null?void 0:r.overflows)||[],{placement:b,overflows:C}],R=m[y+1];if(R)return{data:{index:y+1,overflows:S},reset:{placement:R}};let P=S.map(D=>{let _=He(D.placement);return[D.placement,_&&u?D.overflows.slice(0,2).reduce((H,j)=>H+j,0):D.overflows[0],D.overflows]}).sort((D,_)=>D[1]-_[1]),B=((o=P.filter(D=>D[2].slice(0,He(D[0])?2:3).every(_=>_<=0))[0])==null?void 0:o[0])||P[0][0];return B!==l?{data:{index:y+1,overflows:S},reset:{placement:B}}:{}}}},ah=function(t){return t===void 0&&(t={}),{name:"flip",options:t,async fn(e){var n,r;let{placement:o,middlewareData:i,rects:s,initialPlacement:l,platform:a,elements:c}=e,{mainAxis:u=!0,crossAxis:d=!0,fallbackPlacements:f,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:p="none",flipAlignment:m=!0,...g}=rt(t,e);if((n=i.arrow)!=null&&n.alignmentOffset)return{};let y=Oe(o),b=je(l),x=Oe(l)===l,C=await(a.isRTL==null?void 0:a.isRTL(c.floating)),S=f||(x||!m?[mr(l)]:eh(l)),R=p!=="none";!f&&R&&S.push(...th(l,m,p,C));let P=[l,...S],v=await en(e,g),B=[],D=((r=i.flip)==null?void 0:r.overflows)||[];if(u&&B.push(v[y]),d){let U=ql(o,s,C);B.push(v[U[0]],v[U[1]])}if(D=[...D,{placement:o,overflows:B}],!B.every(U=>U<=0)){var _,H;let U=(((_=i.flip)==null?void 0:_.index)||0)+1,oe=P[U];if(oe&&(!(d==="alignment"?b!==je(oe):!1)||D.every(ie=>ie.overflows[0]>0&&je(ie.placement)===b)))return{data:{index:U,overflows:D},reset:{placement:oe}};let X=(H=D.filter(G=>G.overflows[0]<=0).sort((G,ie)=>G.overflows[1]-ie.overflows[1])[0])==null?void 0:H.placement;if(!X)switch(h){case"bestFit":{var j;let G=(j=D.filter(ie=>{if(R){let Te=je(ie.placement);return Te===b||Te==="y"}return!0}).map(ie=>[ie.placement,ie.overflows.filter(Te=>Te>0).reduce((Te,On)=>Te+On,0)]).sort((ie,Te)=>ie[1]-Te[1])[0])==null?void 0:j[0];G&&(X=G);break}case"initialPlacement":X=l;break}if(o!==X)return{reset:{placement:X}}}return{}}}};function rh(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function oh(t){return Kl.some(e=>t[e]>=0)}var ch=function(t){return t===void 0&&(t={}),{name:"hide",options:t,async fn(e){let{rects:n}=e,{strategy:r="referenceHidden",...o}=rt(t,e);switch(r){case"referenceHidden":{let i=await en(e,{...o,elementContext:"reference"}),s=rh(i,n.reference);return{data:{referenceHiddenOffsets:s,referenceHidden:oh(s)}}}case"escaped":{let i=await en(e,{...o,altBoundary:!0}),s=rh(i,n.floating);return{data:{escapedOffsets:s,escaped:oh(s)}}}default:return{}}}}};function uh(t){let e=ze(...t.map(i=>i.left)),n=ze(...t.map(i=>i.top)),r=fe(...t.map(i=>i.right)),o=fe(...t.map(i=>i.bottom));return{x:e,y:n,width:r-e,height:o-n}}function Ix(t){let e=t.slice().sort((o,i)=>o.y-i.y),n=[],r=null;for(let o=0;o<e.length;o++){let i=e[o];!r||i.y-r.y>r.height/2?n.push([i]):n[n.length-1].push(i),r=i}return n.map(o=>wt(uh(o)))}var dh=function(t){return t===void 0&&(t={}),{name:"inline",options:t,async fn(e){let{placement:n,elements:r,rects:o,platform:i,strategy:s}=e,{padding:l=2,x:a,y:c}=rt(t,e),u=Array.from(await(i.getClientRects==null?void 0:i.getClientRects(r.reference))||[]),d=Ix(u),f=wt(uh(u)),h=ci(l);function p(){if(d.length===2&&d[0].left>d[1].right&&a!=null&&c!=null)return d.find(g=>a>g.left-h.left&&a<g.right+h.right&&c>g.top-h.top&&c<g.bottom+h.bottom)||f;if(d.length>=2){if(je(n)==="y"){let D=d[0],_=d[d.length-1],H=Oe(n)==="top",j=D.top,U=_.bottom,oe=H?D.left:_.left,X=H?D.right:_.right,G=X-oe,ie=U-j;return{top:j,bottom:U,left:oe,right:X,width:G,height:ie,x:oe,y:j}}let g=Oe(n)==="left",y=fe(...d.map(D=>D.right)),b=ze(...d.map(D=>D.left)),x=d.filter(D=>g?D.left===b:D.right===y),C=x[0].top,S=x[x.length-1].bottom,R=b,P=y,v=P-R,B=S-C;return{top:C,bottom:S,left:R,right:P,width:v,height:B,x:R,y:C}}return f}let m=await i.getElementRects({reference:{getBoundingClientRect:p},floating:r.floating,strategy:s});return o.reference.x!==m.reference.x||o.reference.y!==m.reference.y||o.reference.width!==m.reference.width||o.reference.height!==m.reference.height?{reset:{rects:m}}:{}}}},Lx=new Set(["left","top"]);async function Bx(t,e){let{placement:n,platform:r,elements:o}=t,i=await(r.isRTL==null?void 0:r.isRTL(o.floating)),s=Oe(n),l=He(n),a=je(n)==="y",c=Lx.has(s)?-1:1,u=i&&a?-1:1,d=rt(e,t),{mainAxis:f,crossAxis:h,alignmentAxis:p}=typeof d=="number"?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&typeof p=="number"&&(h=l==="end"?p*-1:p),a?{x:h*u,y:f*c}:{x:f*c,y:h*u}}var fh=function(t){return t===void 0&&(t=0),{name:"offset",options:t,async fn(e){var n,r;let{x:o,y:i,placement:s,middlewareData:l}=e,a=await Bx(e,t);return s===((n=l.offset)==null?void 0:n.placement)&&(r=l.arrow)!=null&&r.alignmentOffset?{}:{x:o+a.x,y:i+a.y,data:{...a,placement:s}}}}},hh=function(t){return t===void 0&&(t={}),{name:"shift",options:t,async fn(e){let{x:n,y:r,placement:o}=e,{mainAxis:i=!0,crossAxis:s=!1,limiter:l={fn:g=>{let{x:y,y:b}=g;return{x:y,y:b}}},...a}=rt(t,e),c={x:n,y:r},u=await en(e,a),d=je(Oe(o)),f=Jl(d),h=c[f],p=c[d];if(i){let g=f==="y"?"top":"left",y=f==="y"?"bottom":"right",b=h+u[g],x=h-u[y];h=si(b,h,x)}if(s){let g=d==="y"?"top":"left",y=d==="y"?"bottom":"right",b=p+u[g],x=p-u[y];p=si(b,p,x)}let m=l.fn({...e,[f]:h,[d]:p});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[f]:i,[d]:s}}}}}};var ph=function(t){return t===void 0&&(t={}),{name:"size",options:t,async fn(e){var n,r;let{placement:o,rects:i,platform:s,elements:l}=e,{apply:a=()=>{},...c}=rt(t,e),u=await en(e,c),d=Oe(o),f=He(o),h=je(o)==="y",{width:p,height:m}=i.floating,g,y;d==="top"||d==="bottom"?(g=d,y=f===(await(s.isRTL==null?void 0:s.isRTL(l.floating))?"start":"end")?"left":"right"):(y=d,g=f==="end"?"top":"bottom");let b=m-u.top-u.bottom,x=p-u.left-u.right,C=ze(m-u[g],b),S=ze(p-u[y],x),R=!e.middlewareData.shift,P=C,v=S;if((n=e.middlewareData.shift)!=null&&n.enabled.x&&(v=x),(r=e.middlewareData.shift)!=null&&r.enabled.y&&(P=b),R&&!f){let D=fe(u.left,0),_=fe(u.right,0),H=fe(u.top,0),j=fe(u.bottom,0);h?v=p-2*(D!==0||_!==0?D+_:fe(u.left,u.right)):P=m-2*(H!==0||j!==0?H+j:fe(u.top,u.bottom))}await a({...e,availableWidth:v,availableHeight:P});let B=await s.getDimensions(l.floating);return p!==B.width||m!==B.height?{reset:{rects:!0}}:{}}}};function di(){return typeof window<"u"}function tn(t){return gh(t)?(t.nodeName||"").toLowerCase():"#document"}function ve(t){var e;return(t==null||(e=t.ownerDocument)==null?void 0:e.defaultView)||window}function ot(t){var e;return(e=(gh(t)?t.ownerDocument:t.document)||window.document)==null?void 0:e.documentElement}function gh(t){return di()?t instanceof Node||t instanceof ve(t).Node:!1}function Fe(t){return di()?t instanceof Element||t instanceof ve(t).Element:!1}function Ke(t){return di()?t instanceof HTMLElement||t instanceof ve(t).HTMLElement:!1}function mh(t){return!di()||typeof ShadowRoot>"u"?!1:t instanceof ShadowRoot||t instanceof ve(t).ShadowRoot}var zx=new Set(["inline","contents"]);function An(t){let{overflow:e,overflowX:n,overflowY:r,display:o}=$e(t);return/auto|scroll|overlay|hidden|clip/.test(e+r+n)&&!zx.has(o)}var Hx=new Set(["table","td","th"]);function yh(t){return Hx.has(tn(t))}var Fx=[":popover-open",":modal"];function yr(t){return Fx.some(e=>{try{return t.matches(e)}catch{return!1}})}var $x=["transform","translate","scale","rotate","perspective"],Vx=["transform","translate","scale","rotate","perspective","filter"],Wx=["paint","layout","strict","content"];function fi(t){let e=hi(),n=Fe(t)?$e(t):t;return $x.some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!e&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!e&&(n.filter?n.filter!=="none":!1)||Vx.some(r=>(n.willChange||"").includes(r))||Wx.some(r=>(n.contain||"").includes(r))}function bh(t){let e=St(t);for(;Ke(e)&&!nn(e);){if(fi(e))return e;if(yr(e))return null;e=St(e)}return null}function hi(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}var _x=new Set(["html","body","#document"]);function nn(t){return _x.has(tn(t))}function $e(t){return ve(t).getComputedStyle(t)}function br(t){return Fe(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function St(t){if(tn(t)==="html")return t;let e=t.assignedSlot||t.parentNode||mh(t)&&t.host||ot(t);return mh(e)?e.host:e}function xh(t){let e=St(t);return nn(e)?t.ownerDocument?t.ownerDocument.body:t.body:Ke(e)&&An(e)?e:xh(e)}function ui(t,e,n){var r;e===void 0&&(e=[]),n===void 0&&(n=!0);let o=xh(t),i=o===((r=t.ownerDocument)==null?void 0:r.body),s=ve(o);if(i){let l=pi(s);return e.concat(s,s.visualViewport||[],An(o)?o:[],l&&n?ui(l):[])}return e.concat(o,ui(o,[],n))}function pi(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function kh(t){let e=$e(t),n=parseFloat(e.width)||0,r=parseFloat(e.height)||0,o=Ke(t),i=o?t.offsetWidth:n,s=o?t.offsetHeight:r,l=gr(n)!==i||gr(r)!==s;return l&&(n=i,r=s),{width:n,height:r,$:l}}function Ch(t){return Fe(t)?t:t.contextElement}function En(t){let e=Ch(t);if(!Ke(e))return _e(1);let n=e.getBoundingClientRect(),{width:r,height:o,$:i}=kh(e),s=(i?gr(n.width):n.width)/r,l=(i?gr(n.height):n.height)/o;return(!s||!Number.isFinite(s))&&(s=1),(!l||!Number.isFinite(l))&&(l=1),{x:s,y:l}}var jx=_e(0);function Mh(t){let e=ve(t);return!hi()||!e.visualViewport?jx:{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}}function Kx(t,e,n){return e===void 0&&(e=!1),!n||e&&n!==ve(t)?!1:e}function xr(t,e,n,r){e===void 0&&(e=!1),n===void 0&&(n=!1);let o=t.getBoundingClientRect(),i=Ch(t),s=_e(1);e&&(r?Fe(r)&&(s=En(r)):s=En(t));let l=Kx(i,n,r)?Mh(i):_e(0),a=(o.left+l.x)/s.x,c=(o.top+l.y)/s.y,u=o.width/s.x,d=o.height/s.y;if(i){let f=ve(i),h=r&&Fe(r)?ve(r):r,p=f,m=pi(p);for(;m&&r&&h!==p;){let g=En(m),y=m.getBoundingClientRect(),b=$e(m),x=y.left+(m.clientLeft+parseFloat(b.paddingLeft))*g.x,C=y.top+(m.clientTop+parseFloat(b.paddingTop))*g.y;a*=g.x,c*=g.y,u*=g.x,d*=g.y,a+=x,c+=C,p=ve(m),m=pi(p)}}return wt({width:u,height:d,x:a,y:c})}function Yl(t,e){let n=br(t).scrollLeft;return e?e.left+n:xr(ot(t)).left+n}function vh(t,e,n){n===void 0&&(n=!1);let r=t.getBoundingClientRect(),o=r.left+e.scrollLeft-(n?0:Yl(t,r)),i=r.top+e.scrollTop;return{x:o,y:i}}function Ux(t){let{elements:e,rect:n,offsetParent:r,strategy:o}=t,i=o==="fixed",s=ot(r),l=e?yr(e.floating):!1;if(r===s||l&&i)return n;let a={scrollLeft:0,scrollTop:0},c=_e(1),u=_e(0),d=Ke(r);if((d||!d&&!i)&&((tn(r)!=="body"||An(s))&&(a=br(r)),Ke(r))){let h=xr(r);c=En(r),u.x=h.x+r.clientLeft,u.y=h.y+r.clientTop}let f=s&&!d&&!i?vh(s,a,!0):_e(0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-a.scrollLeft*c.x+u.x+f.x,y:n.y*c.y-a.scrollTop*c.y+u.y+f.y}}function Jx(t){return Array.from(t.getClientRects())}function qx(t){let e=ot(t),n=br(t),r=t.ownerDocument.body,o=fe(e.scrollWidth,e.clientWidth,r.scrollWidth,r.clientWidth),i=fe(e.scrollHeight,e.clientHeight,r.scrollHeight,r.clientHeight),s=-n.scrollLeft+Yl(t),l=-n.scrollTop;return $e(r).direction==="rtl"&&(s+=fe(e.clientWidth,r.clientWidth)-o),{width:o,height:i,x:s,y:l}}function Gx(t,e){let n=ve(t),r=ot(t),o=n.visualViewport,i=r.clientWidth,s=r.clientHeight,l=0,a=0;if(o){i=o.width,s=o.height;let c=hi();(!c||c&&e==="fixed")&&(l=o.offsetLeft,a=o.offsetTop)}return{width:i,height:s,x:l,y:a}}var Yx=new Set(["absolute","fixed"]);function Xx(t,e){let n=xr(t,!0,e==="fixed"),r=n.top+t.clientTop,o=n.left+t.clientLeft,i=Ke(t)?En(t):_e(1),s=t.clientWidth*i.x,l=t.clientHeight*i.y,a=o*i.x,c=r*i.y;return{width:s,height:l,x:a,y:c}}function wh(t,e,n){let r;if(e==="viewport")r=Gx(t,n);else if(e==="document")r=qx(ot(t));else if(Fe(e))r=Xx(e,n);else{let o=Mh(t);r={x:e.x-o.x,y:e.y-o.y,width:e.width,height:e.height}}return wt(r)}function Th(t,e){let n=St(t);return n===e||!Fe(n)||nn(n)?!1:$e(n).position==="fixed"||Th(n,e)}function Qx(t,e){let n=e.get(t);if(n)return n;let r=ui(t,[],!1).filter(l=>Fe(l)&&tn(l)!=="body"),o=null,i=$e(t).position==="fixed",s=i?St(t):t;for(;Fe(s)&&!nn(s);){let l=$e(s),a=fi(s);!a&&l.position==="fixed"&&(o=null),(i?!a&&!o:!a&&l.position==="static"&&!!o&&Yx.has(o.position)||An(s)&&!a&&Th(t,s))?r=r.filter(u=>u!==s):o=l,s=St(s)}return e.set(t,r),r}function Zx(t){let{element:e,boundary:n,rootBoundary:r,strategy:o}=t,s=[...n==="clippingAncestors"?yr(e)?[]:Qx(e,this._c):[].concat(n),r],l=s[0],a=s.reduce((c,u)=>{let d=wh(e,u,o);return c.top=fe(d.top,c.top),c.right=ze(d.right,c.right),c.bottom=ze(d.bottom,c.bottom),c.left=fe(d.left,c.left),c},wh(e,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}}function ew(t){let{width:e,height:n}=kh(t);return{width:e,height:n}}function tw(t,e,n){let r=Ke(e),o=ot(e),i=n==="fixed",s=xr(t,!0,i,e),l={scrollLeft:0,scrollTop:0},a=_e(0);function c(){a.x=Yl(o)}if(r||!r&&!i)if((tn(e)!=="body"||An(o))&&(l=br(e)),r){let h=xr(e,!0,i,e);a.x=h.x+e.clientLeft,a.y=h.y+e.clientTop}else o&&c();i&&!r&&o&&c();let u=o&&!r&&!i?vh(o,l):_e(0),d=s.left+l.scrollLeft-a.x-u.x,f=s.top+l.scrollTop-a.y-u.y;return{x:d,y:f,width:s.width,height:s.height}}function Gl(t){return $e(t).position==="static"}function Sh(t,e){if(!Ke(t)||$e(t).position==="fixed")return null;if(e)return e(t);let n=t.offsetParent;return ot(t)===n&&(n=n.ownerDocument.body),n}function Ah(t,e){let n=ve(t);if(yr(t))return n;if(!Ke(t)){let o=St(t);for(;o&&!nn(o);){if(Fe(o)&&!Gl(o))return o;o=St(o)}return n}let r=Sh(t,e);for(;r&&yh(r)&&Gl(r);)r=Sh(r,e);return r&&nn(r)&&Gl(r)&&!fi(r)?n:r||bh(t)||n}var nw=async function(t){let e=this.getOffsetParent||Ah,n=this.getDimensions,r=await n(t.floating);return{reference:tw(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function rw(t){return $e(t).direction==="rtl"}var ow={convertOffsetParentRelativeRectToViewportRelativeRect:Ux,getDocumentElement:ot,getClippingRect:Zx,getOffsetParent:Ah,getElementRects:nw,getClientRects:Jx,getDimensions:ew,getScale:En,isElement:Fe,isRTL:rw};var Eh=fh,Oh=lh,mi=hh,gi=ah,Nh=ph,Rh=ch,Dh=sh,Ph=dh;var yi=(t,e,n)=>{let r=new Map,o={platform:ow,...n},i={...o.platform,_c:r};return ih(t,e,{...o,platform:i})};var Ih=(t,e)=>{yi({getBoundingClientRect:()=>{let{from:r,to:o}=t.state.selection,i=t.view.coordsAtPos(r),s=t.view.coordsAtPos(o);return{top:Math.min(i.top,s.top),bottom:Math.max(i.bottom,s.bottom),left:Math.min(i.left,s.left),right:Math.max(i.right,s.right),width:Math.abs(s.right-i.left),height:Math.abs(s.bottom-i.top),x:Math.min(i.left,s.left),y:Math.min(i.top,s.top)}}},e,{placement:"bottom-start",strategy:"absolute",middleware:[mi(),gi()]}).then(({x:r,y:o,strategy:i})=>{e.style.width="max-content",e.style.position=i,e.style.left=`${r}px`,e.style.top=`${o}px`})},Lh=({mergeTags:t,noMergeTagSearchResultsMessage:e})=>({items:({query:n})=>t.filter(r=>r.toLowerCase().replace(/\s/g,"").includes(n.toLowerCase())),render:()=>{let n,r=0,o=null,i=()=>{let d=document.createElement("div");return d.className="fi-dropdown-panel fi-dropdown-list",d},s=()=>{if(!n||!o)return;let d=o.items||[];if(n.innerHTML="",d.length)d.forEach((f,h)=>{let p=document.createElement("button");p.className=`fi-dropdown-list-item fi-dropdown-list-item-label ${h===r?"fi-selected":""}`,p.textContent=f,p.type="button",p.addEventListener("click",()=>l(h)),n.appendChild(p)});else{let f=document.createElement("div");f.className="fi-dropdown-header",f.textContent=e,n.appendChild(f)}},l=d=>{if(!o)return;let h=(o.items||[])[d];h&&o.command({id:h})},a=()=>{if(!o)return;let d=o.items||[];d.length!==0&&(r=(r+d.length-1)%d.length,s())},c=()=>{if(!o)return;let d=o.items||[];d.length!==0&&(r=(r+1)%d.length,s())},u=()=>{l(r)};return{onStart:d=>{o=d,r=0,n=i(),n.style.position="absolute",s(),document.body.appendChild(n),d.clientRect&&Ih(d.editor,n)},onUpdate:d=>{o=d,r=0,s(),d.clientRect&&Ih(d.editor,n)},onKeyDown:d=>d.event.key==="Escape"?(n&&n.parentNode&&n.parentNode.removeChild(n),!0):d.event.key==="ArrowUp"?(a(),!0):d.event.key==="ArrowDown"?(c(),!0):d.event.key==="Enter"?(u(),!0):!1,onExit:()=>{n&&n.parentNode&&n.parentNode.removeChild(n)}}}});var Bh=async({customExtensionUrls:t,deleteCustomBlockButtonIconHtml:e,editCustomBlockButtonIconHtml:n,editCustomBlockUsing:r,insertCustomBlockUsing:o,key:i,mergeTags:s,noMergeTagSearchResultsMessage:l,placeholder:a,statePath:c,uploadingFileMessage:u,$wire:d})=>[yd,bd,Ol,xd,wd,Sd.configure({deleteCustomBlockButtonIconHtml:e,editCustomBlockButtonIconHtml:n,editCustomBlockUsing:r,insertCustomBlockUsing:o}),Cd,vd,Md,Td,pd,md,Ad,Ed,Od,Nd,Rd,Pd.configure({inline:!0}),Id,Yd.configure({autolink:!0,openOnClick:!1}),Nl,af.configure({get$WireUsing:()=>d,key:i,statePath:c,uploadingMessage:u}),...s.length?[uf.configure({deleteTriggerWithBackspace:!0,suggestion:Lh({mergeTags:s,noMergeTagSearchResultsMessage:l})})]:[],Dl,df,ff.configure({placeholder:a}),hf,pf,mf,gf,Jf.configure({table:{resizable:!0}}),qf,Gf.configure({types:["heading","paragraph"],alignments:["start","center","end","justify"],defaultAlignment:"start"}),Yf,gd,...(await Promise.all(t.map(async f=>(new RegExp("^(?:[a-z+]+:)?//","i").test(f)||(f=new URL(f,document.baseURI).href),(await import(f)).default)))).flat()];function iw(t,e){let n=Math.min(t.top,e.top),r=Math.max(t.bottom,e.bottom),o=Math.min(t.left,e.left),s=Math.max(t.right,e.right)-o,l=r-n,a=o,c=n;return new DOMRect(a,c,s,l)}var sw=class{constructor({editor:t,element:e,view:n,updateDelay:r=250,resizeDelay:o=60,shouldShow:i,options:s}){this.preventHide=!1,this.isVisible=!1,this.floatingUIOptions={strategy:"absolute",placement:"top",offset:8,flip:{},shift:{},arrow:!1,size:!1,autoPlacement:!1,hide:!1,inline:!1,onShow:void 0,onHide:void 0,onUpdate:void 0,onDestroy:void 0},this.shouldShow=({view:l,state:a,from:c,to:u})=>{let{doc:d,selection:f}=a,{empty:h}=f,p=!d.textBetween(c,u).length&&ao(a.selection),m=this.element.contains(document.activeElement);return!(!(l.hasFocus()||m)||h||p||!this.editor.isEditable)},this.mousedownHandler=()=>{this.preventHide=!0},this.dragstartHandler=()=>{this.hide()},this.resizeHandler=()=>{this.resizeDebounceTimer&&clearTimeout(this.resizeDebounceTimer),this.resizeDebounceTimer=window.setTimeout(()=>{this.updatePosition()},this.resizeDelay)},this.focusHandler=()=>{setTimeout(()=>this.update(this.editor.view))},this.blurHandler=({event:l})=>{var a;if(this.preventHide){this.preventHide=!1;return}l?.relatedTarget&&((a=this.element.parentNode)!=null&&a.contains(l.relatedTarget))||l?.relatedTarget!==this.editor.view.dom&&this.hide()},this.handleDebouncedUpdate=(l,a)=>{let c=!a?.selection.eq(l.state.selection),u=!a?.doc.eq(l.state.doc);!c&&!u||(this.updateDebounceTimer&&clearTimeout(this.updateDebounceTimer),this.updateDebounceTimer=window.setTimeout(()=>{this.updateHandler(l,c,u,a)},this.updateDelay))},this.updateHandler=(l,a,c,u)=>{let{composing:d}=l;if(d||!a&&!c)return;if(!this.getShouldShow(u)){this.hide();return}this.updatePosition(),this.show()},this.editor=t,this.element=e,this.view=n,this.updateDelay=r,this.resizeDelay=o,this.floatingUIOptions={...this.floatingUIOptions,...s},this.element.tabIndex=0,i&&(this.shouldShow=i),this.element.addEventListener("mousedown",this.mousedownHandler,{capture:!0}),this.view.dom.addEventListener("dragstart",this.dragstartHandler),this.editor.on("focus",this.focusHandler),this.editor.on("blur",this.blurHandler),window.addEventListener("resize",this.resizeHandler),this.update(n,n.state),this.getShouldShow()&&this.show()}get middlewares(){let t=[];return this.floatingUIOptions.flip&&t.push(gi(typeof this.floatingUIOptions.flip!="boolean"?this.floatingUIOptions.flip:void 0)),this.floatingUIOptions.shift&&t.push(mi(typeof this.floatingUIOptions.shift!="boolean"?this.floatingUIOptions.shift:void 0)),this.floatingUIOptions.offset&&t.push(Eh(typeof this.floatingUIOptions.offset!="boolean"?this.floatingUIOptions.offset:void 0)),this.floatingUIOptions.arrow&&t.push(Dh(this.floatingUIOptions.arrow)),this.floatingUIOptions.size&&t.push(Nh(typeof this.floatingUIOptions.size!="boolean"?this.floatingUIOptions.size:void 0)),this.floatingUIOptions.autoPlacement&&t.push(Oh(typeof this.floatingUIOptions.autoPlacement!="boolean"?this.floatingUIOptions.autoPlacement:void 0)),this.floatingUIOptions.hide&&t.push(Rh(typeof this.floatingUIOptions.hide!="boolean"?this.floatingUIOptions.hide:void 0)),this.floatingUIOptions.inline&&t.push(Ph(typeof this.floatingUIOptions.inline!="boolean"?this.floatingUIOptions.inline:void 0)),t}updatePosition(){let{selection:t}=this.editor.state,e={getBoundingClientRect:()=>$u(this.view,t.from,t.to)};if(t instanceof K){let{$anchorCell:n,$headCell:r}=t,o=n?n.pos:r.pos,i=r?r.pos:n.pos,s=this.view.nodeDOM(o),l=this.view.nodeDOM(i);if(!s||!l)return;let a=s===l?s.getBoundingClientRect():iw(s.getBoundingClientRect(),l.getBoundingClientRect());e={getBoundingClientRect:()=>a}}yi(e,this.element,{placement:this.floatingUIOptions.placement,strategy:this.floatingUIOptions.strategy,middleware:this.middlewares}).then(({x:n,y:r,strategy:o})=>{this.element.style.width="max-content",this.element.style.position=o,this.element.style.left=`${n}px`,this.element.style.top=`${r}px`,this.isVisible&&this.floatingUIOptions.onUpdate&&this.floatingUIOptions.onUpdate()})}update(t,e){let{state:n}=t,r=n.selection.from!==n.selection.to;if(this.updateDelay>0&&r){this.handleDebouncedUpdate(t,e);return}let o=!e?.selection.eq(t.state.selection),i=!e?.doc.eq(t.state.doc);this.updateHandler(t,o,i,e)}getShouldShow(t){var e;let{state:n}=this.view,{selection:r}=n,{ranges:o}=r,i=Math.min(...o.map(a=>a.$from.pos)),s=Math.max(...o.map(a=>a.$to.pos));return(e=this.shouldShow)==null?void 0:e.call(this,{editor:this.editor,element:this.element,view:this.view,state:n,oldState:t,from:i,to:s})}show(){var t;this.isVisible||(this.element.style.visibility="visible",this.element.style.opacity="1",(t=this.view.dom.parentElement)==null||t.appendChild(this.element),this.floatingUIOptions.onShow&&this.floatingUIOptions.onShow(),this.isVisible=!0)}hide(){this.isVisible&&(this.element.style.visibility="hidden",this.element.style.opacity="0",this.element.remove(),this.floatingUIOptions.onHide&&this.floatingUIOptions.onHide(),this.isVisible=!1)}destroy(){this.hide(),this.element.removeEventListener("mousedown",this.mousedownHandler,{capture:!0}),this.view.dom.removeEventListener("dragstart",this.dragstartHandler),window.removeEventListener("resize",this.resizeHandler),this.editor.off("focus",this.focusHandler),this.editor.off("blur",this.blurHandler),this.floatingUIOptions.onDestroy&&this.floatingUIOptions.onDestroy()}},Xl=t=>new N({key:typeof t.pluginKey=="string"?new L(t.pluginKey):t.pluginKey,view:e=>new sw({view:e,...t})}),Cv=$.create({name:"bubbleMenu",addOptions(){return{element:null,pluginKey:"bubbleMenu",updateDelay:void 0,shouldShow:null}},addProseMirrorPlugins(){return this.options.element?[Xl({pluginKey:this.options.pluginKey,editor:this.editor,element:this.options.element,updateDelay:this.options.updateDelay,options:this.options.options,shouldShow:this.options.shouldShow})]:[]}});function lw({activePanel:t,deleteCustomBlockButtonIconHtml:e,editCustomBlockButtonIconHtml:n,extensions:r,key:o,isDisabled:i,isLiveDebounced:s,isLiveOnBlur:l,liveDebounce:a,livewireId:c,mergeTags:u,noMergeTagSearchResultsMessage:d,placeholder:f,state:h,statePath:p,uploadingFileMessage:m,floatingToolbars:g}){let y;return{state:h,activePanel:t,editorSelection:{type:"text",anchor:1,head:1},isUploadingFile:!1,shouldUpdateState:!0,editorUpdatedAt:Date.now(),async init(){y=new rd({editable:!i,element:this.$refs.editor,extensions:await Bh({customExtensionUrls:r,deleteCustomBlockButtonIconHtml:e,editCustomBlockButtonIconHtml:n,editCustomBlockUsing:(b,x)=>this.$wire.mountAction("customBlock",{editorSelection:this.editorSelection,id:b,config:x,mode:"edit"},{schemaComponent:o}),insertCustomBlockUsing:(b,x=null)=>this.$wire.mountAction("customBlock",{id:b,dragPosition:x,mode:"insert"},{schemaComponent:o}),key:o,mergeTags:u,noMergeTagSearchResultsMessage:d,placeholder:f,statePath:p,uploadingFileMessage:m,$wire:this.$wire,floatingToolbars:g}),content:this.state}),Object.keys(g).forEach(b=>{let x=this.$refs[`floatingToolbar::${b}`];if(!x){console.warn(`Floating toolbar [${b}] not found.`);return}y.registerPlugin(Xl({editor:y,element:x,pluginKey:`floatingToolbar::${b}`,shouldShow:({editor:C})=>C.isFocused&&C.isActive(b),options:{placement:"bottom",offset:15}}))}),y.on("create",()=>{this.editorUpdatedAt=Date.now()}),y.on("update",({editor:b})=>this.$nextTick(()=>{this.editorUpdatedAt=Date.now(),this.state=b.getJSON(),this.shouldUpdateState=!1,s&&Alpine.debounce(()=>this.$wire.commit(),a??300)})),y.on("selectionUpdate",({transaction:b})=>{this.editorUpdatedAt=Date.now(),this.editorSelection=b.selection.toJSON()}),l&&y.on("blur",()=>this.$wire.commit()),this.$watch("state",()=>{if(!this.shouldUpdateState){this.shouldUpdateState=!0;return}y.commands.setContent(this.state)}),window.addEventListener("run-rich-editor-commands",b=>{b.detail.livewireId===c&&b.detail.key===o&&this.runEditorCommands(b.detail)}),window.addEventListener("rich-editor-uploading-file",b=>{b.detail.livewireId===c&&b.detail.key===o&&(this.isUploadingFile=!0,b.stopPropagation())}),window.addEventListener("rich-editor-uploaded-file",b=>{b.detail.livewireId===c&&b.detail.key===o&&(this.isUploadingFile=!1,b.stopPropagation())}),window.dispatchEvent(new CustomEvent(`schema-component-${c}-${o}-loaded`))},getEditor(){return y},$getEditor(){return this.getEditor()},setEditorSelection(b){b&&(this.editorSelection=b,y.chain().command(({tr:x})=>(x.setSelection(E.fromJSON(y.state.doc,this.editorSelection)),!0)).run())},runEditorCommands({commands:b,editorSelection:x}){this.setEditorSelection(x);let C=y.chain();b.forEach(S=>C=C[S.name](...S.arguments??[])),C.run()},togglePanel(b=null){if(this.isPanelActive(b)){this.activePanel=null;return}this.activePanel=b},isPanelActive(b=null){return b===null?this.activePanel!==null:this.activePanel===b},insertMergeTag(b){y.chain().focus().insertContent([{type:"mergeTag",attrs:{id:b}},{type:"text",text:" "}]).run()}}}export{lw as default};
