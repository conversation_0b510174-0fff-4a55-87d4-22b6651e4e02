<?php

namespace App\Listeners;

use App\Enums\ActivityActionType;
use App\Events\RecordActivityEvent;
use Spatie\Backup\Events\BackupHasFailed;

class LogFailedBackup
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(BackupHasFailed $event): void
    {
        event(
            new RecordActivityEvent(
                model: 'backup',
                action: ActivityActionType::BACKUP_FAILED,
                data: [
                    'backupName' => $event->backupDestination->backupName(),
                    'diskName' => $event->backupDestination->diskName(),
                    'error' => $event->exception->getMessage(),
                ],
                attributes: []
            )
        );
    }
}
