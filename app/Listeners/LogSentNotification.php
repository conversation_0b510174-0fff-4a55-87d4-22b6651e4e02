<?php

namespace App\Listeners;

use App\Enums\ActivityActionType;
use App\Events\RecordActivityEvent;
use Illuminate\Notifications\Events\NotificationSent;

class LogSentNotification
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(NotificationSent $event): void
    {
        $notification = null;

        if (method_exists($event->notification, 'toDatabase')) {
            $notification = $event->notification->toDatabase($event->notifiable);
        }

        event(
            new RecordActivityEvent(
                model: 'notifications',
                action: ActivityActionType::NOTIFICATION_SENT,
                data: [
                    'channel' => $event->channel,
                    'notifiable' => $event->notifiable->withoutRelations()->toArray(),
                    'notification' => $notification,
                ],
                attributes: [],
                user: auth()->user()
            )
        );
    }
}
