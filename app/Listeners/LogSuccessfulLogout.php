<?php

namespace App\Listeners;

use App\Enums\ActivityActionType;
use App\Events\RecordActivityEvent;
use Illuminate\Auth\Events\Logout;

class LogSuccessfulLogout
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(Logout $event): void
    {
        event(
            new RecordActivityEvent(
                model: $event->user->getTable(),
                action: ActivityActionType::LOGOUT,
                data: [],
                attributes: [],
                user: $event->user
            )
        );
    }
}
