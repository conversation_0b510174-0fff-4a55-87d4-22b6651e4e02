<?php

namespace App\Listeners;

use App\Enums\ActivityActionType;
use App\Events\PasswordForgotten;
use App\Events\RecordActivityEvent;
use App\Notifications\ResetPasswordRequestCreated;

class ResetPasswordRequested
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(PasswordForgotten $event): void
    {
        $event->user->notify(
            new ResetPasswordRequestCreated(
                token: $event->token,
                password: $event->password
            )
        );

        event(
            new RecordActivityEvent(
                model: $event->user->getTable(),
                action: ActivityActionType::PASSWORD_FORGOT,
                data: [],
                attributes: [],
                user: $event->user
            )
        );
    }
}
