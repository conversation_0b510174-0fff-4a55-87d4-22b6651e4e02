<?php

namespace App\Listeners;

use App\Enums\ActivityActionType;
use App\Events\RecordActivityEvent;
use Illuminate\Auth\Events\Registered;

class LogRegisteredUser
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(Registered $event): void
    {
        event(
            new RecordActivityEvent(
                model: $event->user->getTable(),
                action: ActivityActionType::REGISTER,
                data: [],
                attributes: [],
                user: $event->user
            )
        );
    }
}
