<?php

namespace App\Listeners;

use App\Enums\ActivityActionType;
use App\Events\RecordActivityEvent;
use Spatie\Backup\Events\BackupWasSuccessful;

class LogSuccessfulBackup
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(BackupWasSuccessful $event): void
    {
        event(
            new RecordActivityEvent(
                model: 'backup',
                action: ActivityActionType::BACKUP_SUCCESSFUL,
                data: [
                    'backupName' => $event->backupDestination->backupName(),
                    'diskName' => $event->backupDestination->diskName(),
                ],
                attributes: []
            )
        );
    }
}
