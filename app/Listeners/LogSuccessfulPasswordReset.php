<?php

namespace App\Listeners;

use App\Enums\ActivityActionType;
use App\Events\PasswordReset;
use App\Events\RecordActivityEvent;

class LogSuccessfulPasswordReset
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(PasswordReset $event): void
    {
        event(
            new RecordActivityEvent(
                model: $event->user->getTable(),
                action: ActivityActionType::PASSWORD_RESET,
                data: [],
                attributes: [],
                user: $event->user
            )
        );
    }
}
