<?php

namespace App\Listeners;

use App\Enums\ActivityActionType;
use App\Events\RecordActivityEvent;
use Illuminate\Auth\Events\Login;

class LogSuccessfulLogin
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(Login $event): void
    {
        event(
            new RecordActivityEvent(
                model: $event->user->getTable(),
                action: ActivityActionType::LOGIN,
                data: [
                    'type' => 'WEB',
                    'at' => now()->toString(),
                ],
                attributes: [],
                user: $event->user
            )
        );
    }
}
