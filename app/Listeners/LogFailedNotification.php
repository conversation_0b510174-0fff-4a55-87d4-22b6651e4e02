<?php

namespace App\Listeners;

use App\Enums\ActivityActionType;
use App\Events\RecordActivityEvent;
use Illuminate\Notifications\Events\NotificationFailed;

class LogFailedNotification
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(NotificationFailed $event): void
    {
        $notification = null;

        if (method_exists($event->notification, 'toDatabase')) {
            $notification = $event->notification->toDatabase($event->notifiable);
        }

        event(
            new RecordActivityEvent(
                model: 'notifications',
                action: ActivityActionType::NOTIFICATION_FAILED,
                data: [
                    'channel' => $event->channel,
                    'notifiable' => $event->notifiable->withoutRelations()->toArray(),
                    'notification' => $notification,
                    'data' => $event->data,
                ],
                attributes: [],
                user: auth()->user()
            )
        );
    }
}
