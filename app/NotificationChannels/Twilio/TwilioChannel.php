<?php

namespace App\NotificationChannels\Twilio;

use App\NotificationChannels\Twilio\Exceptions\CouldNotSendNotification;
use Illuminate\Notifications\Notification;

class TwilioChannel
{
    public function __construct(protected TwilioClient $twilio) {}

    /**
     * Send the given notification.
     *
     * @param  mixed  $notifiable
     *
     * @throws CouldNotSendNotification
     */
    public function send($notifiable, Notification $notification): void
    {
        if (! $to = $notifiable->routeNotificationFor('twilio')) {
            return;
        }

        $message = $notification->toTwilio($notifiable);

        if (is_string($message)) {
            $message = new TwilioMessage($message);
        }

        $this->twilio->send(
            to: $message->getDestination() ?? $to,

            message: $message->shouldConvertToASCII() ? iconv('UTF-8', 'ASCII//TRANSLIT', (string) $message->getBody()) : $message->getBody(),
        );
    }
}
