<?php

namespace App\NotificationChannels\Twilio\Exceptions;

use Exception;

class CouldNotSendNotification extends Exception
{
    /**
     * @param  string  $message
     * @param  int  $code
     */
    public static function serviceRespondedWithAnError($message, $code): static
    {
        return new static(
            "<PERSON><PERSON><PERSON> responded with an error '{$message}: {$code}'"
        );
    }
}
