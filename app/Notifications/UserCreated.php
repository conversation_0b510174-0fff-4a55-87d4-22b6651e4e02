<?php

namespace App\Notifications;

use App\Models\User;
use App\NotificationChannels\Twilio\TwilioMessage;
use Filament\Notifications\Notification as FilamentNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;

class UserCreated extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        public readonly string $password
    ) {
        $this->afterCommit();
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'twilio'];
    }

    /**
     * Get the twilio representation of the notification.
     */
    public function toTwilio(User $notifiable): TwilioMessage
    {
        $message = (object) $this->toDatabase($notifiable);

        return (new TwilioMessage($message->body))->toASCII();
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase(User $notifiable): array
    {
        $appName = config('app.name');

        return FilamentNotification::make()
            ->success()
            ->title(__('Compte s3g créé'))
            ->body(__("Bienvenue {$notifiable->name}, votre compte $appName a été créé en tant que {$notifiable->role->getLabel()}.\nTéléphone: {$notifiable->phone}\nMot de passe: {$this->password}\n{$this->getPlayStoreLink()}"))
            ->getDatabaseMessage();
    }

    private function getPlayStoreLink(): string
    {
        $playStoreId = config('app.playstore');
        if (blank($playStoreId)) {
            return '';
        }

        return __("Téléchargez l'appli").": https://play.google.com/store/apps/details?id={$playStoreId}";
    }

    /**
     * Determine which connections should be used for each notification channel.
     *
     * @return array<string, string>
     */
    public function viaConnections(): array
    {
        return [
            'database' => 'sync',
        ];
    }
}
