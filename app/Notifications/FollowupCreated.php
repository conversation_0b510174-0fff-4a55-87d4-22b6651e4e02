<?php

namespace App\Notifications;

use App\Models\User;
use App\Models\Victim\Followup;
use Filament\Notifications\Notification as FilamentNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\Notification as FcmNotification;

class FollowupCreated extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        public readonly Followup $followup
    ) {
        $this->afterCommit();
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', FcmChannel::class];
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase(User $notifiable): array
    {
        $instance = $this->followup->instance;
        $survivorCode = $instance->survivor->code;
        $healthCenterName = $instance->healthCenter->name;
        $companionName = $this->followup->companion?->user->name ?? __('Système');

        return FilamentNotification::make()
            ->info()
            ->title(__('Nouveau suivi créé'))
            ->body(__("{$notifiable->name}, un nouveau suivi a été créé par {$companionName} pour le cas {$survivorCode} au {$healthCenterName}."))
            ->getDatabaseMessage();
    }

    /**
     * Get the FCM representation of the notification.
     */
    public function toFcm(User $notifiable): FcmMessage
    {
        $message = (object) $this->toDatabase($notifiable);

        return
            new FcmMessage(
                notification: new FcmNotification(
                    title: $message->title,
                    body: $message->body,
                )
            );
    }

    /**
     * Determine which connections should be used for each notification channel.
     *
     * @return array<string, string>
     */
    public function viaConnections(): array
    {
        return [
            'database' => 'sync',
        ];
    }
}
