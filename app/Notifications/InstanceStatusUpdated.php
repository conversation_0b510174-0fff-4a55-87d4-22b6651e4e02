<?php

namespace App\Notifications;

use App\Models\User;
use App\Models\Victim\Companion;
use App\Models\Victim\Instance;
use Filament\Notifications\Notification as FilamentNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\Notification as FcmNotification;

class InstanceStatusUpdated extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        public readonly Instance $instance,
        public readonly Companion $companion
    ) {
        $this->afterCommit();
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', FcmChannel::class];
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase(User $notifiable): array
    {
        $companionType = $this->companion->type->getLabel();
        $survivorCode = $this->instance->survivor->code;
        $healthCenterName = $this->instance->healthCenter->name;

        return FilamentNotification::make()
            ->info()
            ->title($this->getTitle().' - '.$survivorCode)
            ->body(__("{$notifiable->name}, le cas $survivorCode auquel vous étiez assigné en tant qu'Accompagnant $companionType au centre de santé $healthCenterName a été {$this->getStatus()}."))
            ->getDatabaseMessage();
    }

    /**
     * Get the FCM representation of the notification.
     */
    public function toFcm(User $notifiable): FcmMessage
    {
        $message = (object) $this->toDatabase($notifiable);

        return
            new FcmMessage(
                notification: new FcmNotification(
                    title: $message->title,
                    body: $message->body,
                )
            );
    }

    public function getStatus()
    {
        return $this->instance->closed ? __('clôturé') : __('ouvert');
    }

    public function getTitle(): string
    {
        return $this->instance->closed ? __('Cas clôturé') : __('Cas ouvert');
    }

    /**
     * Determine which connections should be used for each notification channel.
     *
     * @return array<string, string>
     */
    public function viaConnections(): array
    {
        return [
            'database' => 'sync',
        ];
    }
}
