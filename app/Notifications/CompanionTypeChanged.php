<?php

namespace App\Notifications;

use App\Models\User;
use App\Models\Victim\Companion;
use Filament\Notifications\Notification as FilamentNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\Notification as FcmNotification;

class CompanionTypeChanged extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        public readonly Companion $companion
    ) {
        $this->afterCommit();
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', FcmChannel::class];
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase(User $notifiable): array
    {
        $instance = $this->companion->instance;
        $companionType = $this->companion->type->getLabel();

        $survivorCode = $instance->survivor->code;
        $healthCenterName = $instance->healthCenter->name;

        return FilamentNotification::make()
            ->info()
            ->title(__("Changement de type d'accompagnant"))
            ->body(__("{$notifiable->name}, votre type d'accompagnant pour le cas $survivorCode au $healthCenterName a été changé en $companionType."))
            ->getDatabaseMessage();
    }

    /**
     * Get the FCM representation of the notification.
     */
    public function toFcm(User $notifiable): FcmMessage
    {
        $message = (object) $this->toDatabase($notifiable);

        return
            new FcmMessage(
                notification: new FcmNotification(
                    title: $message->title,
                    body: $message->body,
                ),
                data: [
                    'type' => 'companion_type_changed',
                    'companion_id' => (string) $this->companion->id,
                    'instance_id' => (string) $this->companion->instance_id,
                    'route' => '/app/companion/' . $this->companion->id . '/detail',
                ]
            );
    }

    /**
     * Determine which connections should be used for each notification channel.
     *
     * @return array<string, string>
     */
    public function viaConnections(): array
    {
        return [
            'database' => 'sync',
        ];
    }
}
