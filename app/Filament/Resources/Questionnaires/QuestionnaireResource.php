<?php

namespace App\Filament\Resources\Questionnaires;

use App\Enums\QuestionnaireResponseType;
use App\Filament\Resources\Questionnaires\Pages\CreateQuestionnaire;
use App\Filament\Resources\Questionnaires\Pages\EditQuestionnaire;
use App\Filament\Resources\Questionnaires\Pages\ListQuestionnaires;
use App\Models\Questionnaire;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\ToggleButtons;
use Filament\Resources\Resource;
use Filament\Schemas\Components\Utilities\Get;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class QuestionnaireResource extends Resource
{
    protected static ?string $model = Questionnaire::class;

    protected static ?int $navigationSort = 5;

    protected static string|\UnitEnum|null $navigationGroup = 'Global';

    protected static ?string $navigationLabel = 'Questionnaires';

    protected static ?string $modelLabel = 'Questionnaire';

    protected static ?string $pluralModelLabel = 'Questionnaires';

    protected static string|\BackedEnum|null $navigationIcon = 'heroicon-o-document-text';

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('question')
                    ->label(__('Question'))
                    ->unique(ignoreRecord: true)
                    ->required(),

                ToggleButtons::make('type')
                    ->inline()
                    ->live()
                    ->label(__('Format de la response'))
                    ->options(QuestionnaireResponseType::class)
                    ->required(),

                TextInput::make('hint')
                    ->label(__('Indice'))
                    ->nullable(),

                Repeater::make('choices')
                    ->relationship()
                    ->label(__('Choix Multiple'))
                    ->hidden(fn (Get $get): bool => $get('type') !== QuestionnaireResponseType::CHOICES->value)
                    ->schema([
                        TextInput::make('choice')
                            ->required()
                            ->label(__('Choix')),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('question')
                    ->searchable(),

                TextColumn::make('type')
                    ->badge()
                    ->searchable(),

                TextColumn::make('created_at')
                    ->label(__('Créé le'))
                    ->since()
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('updated_at')
                    ->label(__('Mis à jour le'))
                    ->since()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->recordActions([
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListQuestionnaires::route('/'),
            'create' => CreateQuestionnaire::route('/create'),
            'edit' => EditQuestionnaire::route('/{record}/edit'),
        ];
    }
}
