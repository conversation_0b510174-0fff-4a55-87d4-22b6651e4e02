<?php

namespace App\Filament\Resources\HealthCenters\Widgets;

use App\Enums\UserRole;
use App\Models\Health\HealthCenter;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class HealthCenterOverview extends BaseWidget
{
    public ?HealthCenter $record = null;

    protected function getStats(): array
    {
        $instances = $this->record?->instances()->count();
        $aps = $this->record?->users()->where('role', UserRole::APS)->count();
        $companions = $this->record?->users()->where('role', UserRole::COMPANION)->count();
        $relapses = $this->record?->relapses()->count();

        return [
            Stat::make(__('Cas'), $instances),
            Stat::make(__('APS'), $aps),
            Stat::make(__('Accompagnants'), $companions),
            Stat::make(__('Rechutes'), $relapses),
        ];
    }
}
