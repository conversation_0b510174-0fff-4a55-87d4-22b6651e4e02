<?php

namespace App\Filament\Resources\HealthCenters\RelationManagers;

use App\Enums\UserResponsibility;
use App\Enums\UserRole;
use Filament\Actions\AttachAction;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\CreateAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\DetachAction;
use Filament\Actions\EditAction;
use Filament\Forms\Components\ToggleButtons;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Schema;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class CompanionsRelationManager extends RelationManager
{
    protected static string $relationship = 'users';

    protected static ?string $modelLabel = 'Accompagnant';

    protected static ?string $title = 'Accompagnants';

    public function form(Schema $schema): Schema
    {
        return $schema
            ->columns(1)
            ->components(APSRelationManager::getDefaultFormSchema(UserRole::COMPANION));
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->modifyQueryUsing(fn (Builder $query) => $query->where('role', UserRole::COMPANION))
            ->columns(APSRelationManager::getDefaultTableSchema(UserRole::COMPANION))
            ->filters([])
            ->headerActions([
                AttachAction::make()
                    ->recordSelectOptionsQuery(
                        fn (Builder $query) => $query->where('role', UserRole::COMPANION)
                    )
                    ->form(fn (AttachAction $action): array => [
                        $action->getRecordSelect(),
                        ToggleButtons::make('responsibility')
                            ->inline()
                            ->default(UserResponsibility::COMPANION)
                            ->label(__('Responsabilité'))
                            ->options(UserResponsibility::transCompanion())
                            ->required(),
                    ])
                    ->recordSelectSearchColumns(['name', 'phone', 'email']),
                CreateAction::make(),
            ])
            ->defaultSort('users.id', 'desc')
            ->recordActions([
                DetachAction::make(),
                EditAction::make(),
                // Tables\Actions\DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
