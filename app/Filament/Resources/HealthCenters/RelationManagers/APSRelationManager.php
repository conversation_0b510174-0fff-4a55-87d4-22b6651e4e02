<?php

namespace App\Filament\Resources\HealthCenters\RelationManagers;

use App\Enums\CompanionType;
use App\Enums\UserResponsibility;
use App\Enums\UserRole;
use Filament\Actions\AttachAction;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\CreateAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\DetachAction;
use Filament\Actions\EditAction;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\ToggleButtons;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Components\Fieldset;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Schema;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Str;

class APSRelationManager extends RelationManager
{
    protected static string $relationship = 'users';

    protected static ?string $modelLabel = 'APS';

    protected static ?string $title = 'APS';

    public function form(Schema $schema): Schema
    {
        return $schema
            ->disabled(! $this->hasAccess())
            ->columns(1)
            ->components(self::getDefaultFormSchema(UserRole::APS));
    }

    public static function getDefaultFormSchema(UserRole $userRole): array
    {
        return [
            TextInput::make('name')
                ->label(__('Nom'))
                ->required()
                ->autocomplete(false)
                ->minLength(3)
                ->maxLength(255),

            TextInput::make('phone')
                ->label(__('Numéro de téléphone'))
                ->required()
                ->tel()
                ->rules(['phone'])
                ->autocomplete(false)
                ->unique(ignoreRecord: true)
                ->maxLength(32),

            TextInput::make('email')
                ->email()
                ->label(__('Adresse Email'))
                ->nullable()
                ->autocomplete(false)
                ->unique(ignoreRecord: true)
                ->maxLength(255),

            Grid::make()
                ->columnSpanFull()
                ->schema([
                    ToggleButtons::make('responsibility')
                        ->label(__('Responsabilité'))
                        ->inline()
                        ->default(
                            match ($userRole) {
                                UserRole::COMPANION => UserResponsibility::COMPANION->value,
                                default => null,
                            }
                        )
                        ->options(
                            match ($userRole) {
                                UserRole::APS => UserResponsibility::transAPS(),
                                UserRole::COMPANION => UserResponsibility::transCompanion(),
                                default => UserResponsibility::trans(),
                            }
                        )
                        ->required(),

                    Select::make('role')
                        ->default($userRole->value)
                        ->label(__('Rôle'))
                        ->native(false)
                        ->disabledOn('edit')
                        ->options([$userRole->value => $userRole->getLabel()])
                        ->required(),
                ]),

            ...$userRole === UserRole::COMPANION ?
            [
                Fieldset::make()
                    ->relationship('companionRole')
                    ->columns(1)
                    ->columnSpanFull()
                    ->schema([
                        ToggleButtons::make('role')
                            ->label(__('Type Accompagnant'))
                            ->inline()
                            ->required()
                            ->options(CompanionType::class),
                    ]),
            ] :
            [],

            Textarea::make('description')
                ->label(__("Plus d'information"))
                ->nullable()
                ->autocomplete(false)
                ->maxLength(255),

            TextInput::make('password')
                ->label(__('Mot de Passe'))
                ->required()
                ->visibleOn('create')
                ->password()
                ->default(Str::random(10))
                ->readOnly(),
        ];
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->modifyQueryUsing(fn (Builder $query) => $query->where('role', UserRole::APS))
            ->columns(self::getDefaultTableSchema(UserRole::APS))
            ->filters([])
            ->headerActions([
                AttachAction::make()
                    ->hidden(! $this->hasAccess())
                    ->recordSelectOptionsQuery(
                        fn (Builder $query) => $query->where('role', UserRole::APS)
                    )
                    ->form(fn (AttachAction $action): array => [
                        $action->getRecordSelect(),

                        ToggleButtons::make('responsibility')
                            ->inline()
                            ->label(__('Responsabilité'))
                            ->options(UserResponsibility::transAPS())
                            ->required(),
                    ])
                    ->recordSelectSearchColumns(['name', 'phone', 'email']),

                CreateAction::make()
                    ->hidden(! $this->hasAccess()),
            ])
            ->defaultSort('health_center_user.id', 'desc')
            ->recordActions([
                DetachAction::make()
                    ->hidden(! $this->hasAccess()),

                EditAction::make()
                    ->hidden(! $this->hasAccess()),

                // Tables\Actions\DeleteAction::make()
                //     ->hidden(!$this->hasAccess()),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ])->hidden(! $this->hasAccess()),
            ]);
    }

    public function hasAccess()
    {
        return once(function (): bool {
            $auth = auth()->user();

            if ($auth->isRoot()) {
                return true;
            }

            $owner = $this->getOwnerRecord();

            $auth = $owner->users()
                ->where('health_center_user.user_id', auth()->user()->id)
                ->withPivot(['responsibility'])
                ->first();

            return $auth->pivot->responsibility === UserResponsibility::ADMINISTRATOR->value;
        });
    }

    public static function getDefaultTableSchema(UserRole $userRole): array
    {
        return [
            TextColumn::make('name')
                ->label(__('Nom')),

            TextColumn::make('phone')
                ->label(__('Numéro de téléphone')),

            TextColumn::make('email')
                ->label(__('Adresse Email')),

            TextColumn::make('role')
                ->badge(),

            TextColumn::make('responsibility')
                ->label(__('Responsabilité'))
                ->badge(),

            TextColumn::make('created_at')
                ->label(__('Créé le'))
                ->since()
                ->sortable()
                ->toggleable(),
        ];
    }
}
