<?php

namespace App\Filament\Resources\HealthCenters\RelationManagers;

use App\Enums\InstanceType;
use App\Filament\Resources\Instances\InstanceResource;
use App\Models\Victim\Instance;
use Filament\Actions\Action;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\CreateAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\RestoreBulkAction;
use Filament\Actions\ViewAction;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\ToggleButtons;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class InstancesRelationManager extends RelationManager
{
    protected static string $relationship = 'instances';

    protected static ?string $modelLabel = 'Cas';

    protected static ?string $title = 'Cas';

    public function form(Schema $schema): Schema
    {
        $owner = $this->getOwnerRecord();

        return $schema
            ->columns(1)
            ->components([

                Select::make('survivor_id')
                    ->native(false)
                    ->label(__('Survivant'))
                    ->disabledOn('edit')
                    ->relationship(
                        'survivor',
                        'code',
                        fn (Builder $query) => $query->where('health_center_id', $owner->id)
                    )
                    ->preload()
                    ->searchable()
                    ->required()
                    ->lazy()
                    ->createOptionForm([
                        Hidden::make('health_center_id')
                            ->default($owner->id)
                            ->required(),

                        TextInput::make('code')
                            ->label(__('Code'))
                            ->required()
                            ->disabledOn('edit')
                            ->unique(ignoreRecord: true)
                            ->maxLength(255),

                        Textarea::make('description')
                            ->label(__('Description')),
                    ])
                    ->createOptionAction(fn (Action $action): Action => $action
                        ->modalHeading(__('Créer Survivant'))
                        ->modalSubmitActionLabel(__('Créer Survivant'))
                        ->modalWidth('md')),

                ToggleButtons::make('type')
                    ->label(__('Type'))
                    ->default(InstanceType::NEW)
                    ->options(InstanceType::class)
                    ->inline()
                    ->required(),

                TextInput::make('code')
                    ->default(Instance::getUniqueCode())
                    ->disabled()
                    ->dehydrated()
                    ->required()
                    ->maxLength(32)
                    ->unique(ignoreRecord: true),

                Textarea::make('description')
                    ->rows(5)
                    ->nullable(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('code')
            ->modifyQueryUsing(
                fn (Builder $query) => $query->with('survivor')
                    ->withoutGlobalScopes([
                        SoftDeletingScope::class,
                    ])
            )
            ->columns([
                TextColumn::make('survivor.code')
                    ->label(__('Code Survivant'))
                    ->searchable(),

                TextColumn::make('code')
                    ->searchable(),

                TextColumn::make('type')
                    ->badge()
                    ->sortable(),

                IconColumn::make('status'),

                TextColumn::make('created_at')
                    ->label(__('Ajouté le'))
                    ->since()
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('deleted_at')
                    ->label(__('Supprimé le'))
                    ->since()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                TrashedFilter::make(),
            ])
            ->headerActions([
                CreateAction::make(),
            ])
            ->defaultSort('instances.id', 'desc')
            ->recordActions([
                ViewAction::make()
                    ->url(function (ViewAction $action): string {
                        $record = $action->getRecord();

                        return InstanceResource::getUrl('edit', ['record' => $record->id]);
                    }),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                    RestoreBulkAction::make(),
                ]),
            ]);
    }
}
