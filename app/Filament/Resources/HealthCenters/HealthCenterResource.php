<?php

namespace App\Filament\Resources\HealthCenters;

use App\Filament\Resources\HealthCenters\Pages\CreateHealthCenter;
use App\Filament\Resources\HealthCenters\Pages\EditHealthCenter;
use App\Filament\Resources\HealthCenters\Pages\ListHealthCenters;
use App\Filament\Resources\HealthCenters\Pages\ViewHealthCenter;
use App\Filament\Resources\HealthCenters\RelationManagers\APSRelationManager;
use App\Filament\Resources\HealthCenters\RelationManagers\CompanionsRelationManager;
use App\Filament\Resources\HealthCenters\RelationManagers\InstancesRelationManager;
use App\Models\Health\HealthCenter;
use App\Models\User;
use Filament\Actions\Action;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ForceDeleteBulkAction;
use Filament\Actions\RestoreBulkAction;
use Filament\Actions\ViewAction;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Resource;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class HealthCenterResource extends Resource
{
    protected static ?string $recordTitleAttribute = 'name';

    protected static ?int $navigationSort = 1;

    protected static string|\UnitEnum|null $navigationGroup = 'Santé';

    protected static ?string $navigationLabel = 'Centres de santé';

    protected static ?string $modelLabel = 'Centre de santé';

    protected static ?string $pluralModelLabel = 'Centres de santé';

    protected static ?string $model = HealthCenter::class;

    protected static string|\BackedEnum|null $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->disabled(! self::hasAccess())
            ->components([
                Section::make()
                    ->schema([
                        Select::make('health_zone_id')
                            ->native(false)
                            ->label(__('Zone de santé'))
                            ->preload()
                            ->lazy()
                            ->searchable()
                            ->relationship('healthZone', 'name')
                            ->required()
                            ->createOptionForm([
                                TextInput::make('name')
                                    ->disabledOn('edit')
                                    ->label(__('Nom'))
                                    ->unique(ignoreRecord: true)
                                    ->required()
                                    ->maxLength(255),

                                TextInput::make('population_served')
                                    ->label(__('Population'))
                                    ->disabledOn('edit')
                                    ->required(false)
                                    ->numeric(),
                            ])
                            ->createOptionAction(fn (Action $action): Action => $action
                                ->modalHeading(__('Créer Zone de santé'))
                                ->modalSubmitActionLabel(__('Créer Zone de santé'))
                                ->modalWidth('lg')),

                        TextInput::make('name')
                            ->label(__('Nom'))
                            ->unique(ignoreRecord: true)
                            ->required(),

                        TextInput::make('address')
                            ->label(__("L'adresse"))
                            ->required(),

                        TextInput::make('phone')
                            ->label(__('Numéro de téléphone'))
                            ->tel()
                            ->rules(['phone'])
                            ->required(),

                        Textarea::make('services_offered')
                            ->label(__('Services proposés'))
                            ->required()
                            ->columnSpanFull(),

                    ])
                    ->columnSpan(['lg' => fn (?HealthCenter $record): int => $record instanceof HealthCenter ? 2 : 3]),

                Section::make()
                    ->schema([
                        Placeholder::make('created_at')
                            ->hiddenOn('create')
                            ->label(__('Créé le'))
                            ->content(fn (HealthCenter $record): ?string => $record->created_at?->diffForHumans()),

                        Placeholder::make('updated_at')
                            ->hiddenOn('create')
                            ->label(__('Mis à jour le'))
                            ->content(fn (HealthCenter $record): ?string => $record->updated_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?HealthCenter $record): bool => ! $record instanceof HealthCenter),
            ])
            ->columns(['sm' => 3, 'lg' => null]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label(__('Centre de santé'))
                    ->searchable(),

                TextColumn::make('healthZone.name')
                    ->label(__('Zone de santé'))
                    ->numeric()
                    ->sortable(),

                TextColumn::make('address')
                    ->label(__("L'adresse"))
                    ->searchable(),

                TextColumn::make('phone')
                    ->label(__('Numéro de téléphone'))
                    ->searchable(),

                TextColumn::make('deleted_at')
                    ->label(__('Supprimé le'))
                    ->since()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('created_at')
                    ->label(__('Créé le'))
                    ->since()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label(__('Mis à jour le'))
                    ->since()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                TrashedFilter::make(),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                    ForceDeleteBulkAction::make(),
                    RestoreBulkAction::make(),
                ])->hidden(! self::hasAccess()),
            ]);
    }

    public static function hasAccess()
    {
        return auth()->user()->isRoot();
    }

    public static function getRelations(): array
    {
        return [
            APSRelationManager::class,
            CompanionsRelationManager::class,
            InstancesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListHealthCenters::route('/'),
            'create' => CreateHealthCenter::route('/create'),
            'view' => ViewHealthCenter::route('/{record}'),
            'edit' => EditHealthCenter::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        /**
         * @var User $user
         */
        $user = auth()->user();

        if ($user->isAPS()) {
            $ids = $user->healthCenters()
                ->pluck('health_centers.id')
                ->toArray();

            return parent::getEloquentQuery()
                ->with(['healthZone'])
                ->whereIn('id', $ids);
        }

        return parent::getEloquentQuery()
            ->with(['healthZone'])
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
