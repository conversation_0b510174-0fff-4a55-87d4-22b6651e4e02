<?php

namespace App\Filament\Resources\Instances\RelationManagers;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\CreateAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Forms\Components\Textarea;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;

class RelapseRelationManager extends RelationManager
{
    protected static string $relationship = 'relapse';

    protected static ?string $modelLabel = 'Rechute';

    protected static ?string $title = 'Rechute';

    public function form(Schema $schema): Schema
    {

        return $schema
            ->columns(1)
            ->components([
                Textarea::make('description')
                    ->required()
                    ->minLength(10)
                    ->rows(8)
                    ->label('Description'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('created_at')
            ->columns([
                IconColumn::make('signale')
                    ->default('warning')
                    ->icon('heroicon-o-exclamation-circle')
                    ->color('warning'),

                TextColumn::make('created_at')
                    ->label(__('Signalé le'))
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([])
            ->headerActions([
                CreateAction::make()
                    ->hidden(fn () => $this->getOwnerRecord()->relapse()->exists())
                    ->label(__('Signaler une rechute')),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getBadgeColor(Model $ownerRecord, string $pageClass): ?string
    {
        return 'warning';
    }

    public static function getBadge(Model $ownerRecord, string $pageClass): ?string
    {
        return $ownerRecord->relapse()->exists() ? 'Signalé' : null;
    }

    public function isReadOnly(): bool
    {
        $record = $this->getOwnerRecord();

        return $record->closed || filled($record->deleted_at);
    }
}
