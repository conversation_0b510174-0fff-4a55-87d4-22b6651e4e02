<?php

namespace App\Filament\Resources\Instances\RelationManagers;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\ViewAction;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class FollowupsRelationManager extends RelationManager
{
    protected static string $relationship = 'followups';

    protected static ?string $modelLabel = 'Suivi';

    protected static ?string $title = 'Suivis';

    public function form(Schema $schema): Schema
    {
        return $schema->components([
            TextInput::make('title')
                ->label(__('Titre'))
                ->disabled()
                ->required(),

            Textarea::make('description')
                ->label(__('Description'))
                ->disabled()
                ->rows(7)
                ->columnSpanFull()
                ->required(),
        ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('id')
            ->modifyQueryUsing(fn (Builder $query) => $query->with('companion.user'))
            ->columns([
                TextColumn::make('companion.user.name')
                    ->label(__("Nom D'accompagnant"))
                    ->sortable()
                    ->searchable(),

                TextColumn::make('companion.type')
                    ->label(__("Type d'accompagnant"))
                    ->badge(),

                TextColumn::make('companion.user.phone')
                    ->label(__('Numéro de téléphone'))
                    ->searchable(),

                TextColumn::make('title')
                    ->width(200),

                TextColumn::make('created_at')
                    ->label(__('Ajouté le'))
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([])
            ->headerActions([])
            ->recordActions([
                ViewAction::make(),

                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public function isReadOnly(): bool
    {
        $record = $this->getOwnerRecord();

        return $record->closed || filled($record->deleted_at);
    }
}
