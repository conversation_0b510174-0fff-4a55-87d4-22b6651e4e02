<?php

namespace App\Filament\Resources\Instances\RelationManagers;

use App\Enums\QuestionnaireResponseType;
use App\Models\Questionnaire;
use App\Models\Victim\Instance;
use App\Validator\Attachment;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\CreateAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\ToggleButtons;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Components\Utilities\Get;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ViewColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class DiagnosticsRelationManager extends RelationManager
{
    protected static string $relationship = 'diagnostics';

    protected static ?string $modelLabel = 'Diagnostique';

    protected static ?string $title = 'Diagnostiques';

    public function form(Schema $schema): Schema
    {
        [$allQuestionnaires, $questionnaires] = once(function (): array {
            /**
             * @var Instance
             */
            $instance = $this->getOwnerRecord();
            $diagnostics = $instance->diagnostics->pluck('questionnaire_id')->all();

            $allQuestionnaires = Questionnaire::pluck('question', 'id');

            return [
                $allQuestionnaires->all(),
                $allQuestionnaires->filter(fn ($v, $k): bool => ! in_array($k, $diagnostics))->all(),
            ];
        });

        return $schema
            ->columns(1)
            ->components([
                Select::make('questionnaire_id')
                    ->label(__('Question'))
                    ->options($allQuestionnaires)
                    ->native(false)
                    ->visibleOn('edit')
                    ->disabled()
                    ->live()
                    ->required(),

                Select::make('questionnaire_id')
                    ->label(__('Question'))
                    ->options($questionnaires)
                    ->visibleOn('create')
                    ->native(false)
                    ->searchable()
                    ->live()
                    ->required(),

                TextInput::make('response')
                    ->hidden($this->hiddenField(QuestionnaireResponseType::TEXT))
                    ->hint(__('Entrez le texte'))
                    ->autocomplete(false)
                    ->required(),

                TextInput::make('response')
                    ->hidden($this->hiddenField(QuestionnaireResponseType::NUMBER))
                    ->hint(__('Entrez le nombre'))
                    ->numeric()
                    ->autocomplete(false)
                    ->required(),

                ToggleButtons::make('response')
                    ->hidden($this->hiddenField(QuestionnaireResponseType::BOOLEAN))
                    ->inline()
                    ->options([
                        'Oui' => __('Oui'),
                        'Non' => __('Non'),
                    ])
                    ->required(),

                ToggleButtons::make('response')
                    ->hidden($this->hiddenField(QuestionnaireResponseType::CHOICES))
                    ->hint(__("Choisissez parmi l'une des réponses suivantes"))
                    ->options(function (Get $get) {
                        if ($get('questionnaire_id') === null) {
                            return [];
                        }

                        $questionnaire = Questionnaire::find($get('questionnaire_id'));

                        return $questionnaire->choices->pluck('choice', 'choice')->all();
                    })
                    ->required(),

                DatePicker::make('response')
                    ->hidden($this->hiddenField(QuestionnaireResponseType::DATE))
                    ->hint(__('Entrez la date'))
                    ->format('d/m/Y')
                    ->native(false)
                    ->required(),

                FileUpload::make('response')
                    ->hidden($this->hiddenField(QuestionnaireResponseType::ATTACHMENT))
                    ->disk('public')
                    ->downloadable()
                    ->moveFile()
                    ->directory(Attachment::DIAGNOSTIC_UPLOAD_DIRECTORY)
                    ->acceptedFileTypes(Attachment::ATTACHMENT_MIME_TYPE)
                    ->required(),
            ]);
    }

    public function hiddenField(QuestionnaireResponseType $type)
    {
        return function (Get $get) use ($type): bool {
            if ($get('questionnaire_id') === null) {
                return true;
            }
            $questionnaire = Questionnaire::find($get('questionnaire_id'));

            return $questionnaire->type !== $type;
        };
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('questionnaire.question')
            ->modifyQueryUsing(fn (Builder $query) => $query->with('questionnaire'))
            ->columns([
                TextColumn::make('questionnaire.question')
                    ->label(__('Question')),

                TextColumn::make('questionnaire.type')
                    ->badge()
                    ->label(__('Format')),

                ViewColumn::make('response')
                    ->label(__('Réponse'))
                    ->view('filament.tables.columns.diagnostic-response'),

                TextColumn::make('created_at')
                    ->label(__('Ajouté le'))
                    ->since()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label(__('Modifié le'))
                    ->since()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([])
            ->headerActions([
                CreateAction::make()
                    ->createAnother(false),
            ])
            ->recordActions([
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->defaultSort('id', 'desc')
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public function isReadOnly(): bool
    {
        $record = $this->getOwnerRecord();

        return $record->closed || filled($record->deleted_at);
    }
}
