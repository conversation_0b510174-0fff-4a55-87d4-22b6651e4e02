<?php

namespace App\Filament\Resources\Instances\RelationManagers;

use App\Enums\TreatmentType;
use App\Models\Victim\Instance;
use App\Validator\Attachment;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\CreateAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\ToggleButtons;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class TreatmentsRelationManager extends RelationManager
{
    protected static string $relationship = 'treatments';

    protected static ?string $modelLabel = 'Prise en charge';

    protected static ?string $title = 'Prises en charge';

    public function form(Schema $schema): Schema
    {
        [$AllTypes, $types] = once(function (): array {
            /**
             * @var Instance
             */
            $owner = $this->getOwnerRecord();
            $types = $owner->treatments->pluck('type');

            $toArray = fn (TreatmentType $type) => [$type->value => $type->getLabel()];

            $collection = collect(TreatmentType::cases());

            return [
                $collection->mapWithKeys($toArray)->toArray(),

                $collection->filter(fn (TreatmentType $type): bool => ! $types->containsStrict($type))
                    ->mapWithKeys($toArray)
                    ->toArray(),
            ];
        });

        return $schema
            ->columns(1)
            ->components([
                ToggleButtons::make('type')
                    ->inline()
                    ->required()
                    ->disabled()
                    ->visibleOn('edit')
                    ->options($AllTypes),

                ToggleButtons::make('type')
                    ->inline()
                    ->required()
                    ->visibleOn('create')
                    ->disabledOn('edit')
                    ->options($types),

                Textarea::make('observation')
                    ->required()
                    ->minLength(5)
                    ->rows(5)
                    ->columnSpanFull()
                    ->label('Observation'),

                FileUpload::make('attachment')
                    ->disk('public')
                    ->downloadable()
                    ->moveFile()
                    ->directory(Attachment::TREATMENT_UPLOAD_DIRECTORY)
                    ->acceptedFileTypes(Attachment::ATTACHMENT_MIME_TYPE)
                    ->nullable(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('type')
            ->columns([
                TextColumn::make('type')
                    ->badge(),

                TextColumn::make('created_at')
                    ->label(__('Ajouté le'))
                    ->sortable()
                    ->toggleable(),

            ])
            ->filters([])
            ->headerActions([
                CreateAction::make(),
            ])
            ->recordActions([
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public function isReadOnly(): bool
    {
        $record = $this->getOwnerRecord();

        return $record->closed || filled($record->deleted_at);
    }
}
