<?php

namespace App\Filament\Resources\Instances\RelationManagers;

use App\Enums\CompanionType;
use App\Enums\UserResponsibility;
use App\Models\User;
use App\Models\Victim\Instance;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\CreateAction;
use Filament\Actions\EditAction;
use Filament\Actions\ForceDeleteAction;
use Filament\Actions\ForceDeleteBulkAction;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\ToggleButtons;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Components\Utilities\Get;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CompanionsRelationManager extends RelationManager
{
    protected static string $relationship = 'companions';

    protected static ?string $modelLabel = 'Accompagnant';

    protected static ?string $title = 'Accompagnants';

    public function form(Schema $schema): Schema
    {
        [$AllCompanions, $companions] = once(function (): array {
            /**
             * @var Instance
             */
            $instance = $this->getOwnerRecord();
            $companions = $instance->companions->pluck('user_id')->all();

            $allCompanion = $instance->healthCenter->users()
                ->where('responsibility', UserResponsibility::COMPANION)
                ->pluck('users.name', 'users.id');

            return [
                $allCompanion->all(),
                $allCompanion->filter(fn ($v, $k): bool => ! in_array($k, $companions))->all(),
            ];
        });

        return $schema
            ->columns(1)
            ->components([
                Select::make('user_id')
                    ->searchable()
                    ->options($AllCompanions)
                    ->visibleOn('edit')
                    ->disabled()
                    ->label(__('Accompagnant'))
                    ->placeholder(__('Sélectionner un accompagnant'))
                    ->native(false)
                    ->required(),

                Select::make('user_id')
                    ->searchable()
                    ->options($companions)
                    ->visibleOn('create')
                    ->label(__('Accompagnant'))
                    ->placeholder(__('Sélectionner un accompagnant'))
                    ->native(false)
                    ->live()
                    ->required(),

                Section::make()
                    ->columnSpanFull()
                    ->schema([
                        ToggleButtons::make('type')
                            ->inline()
                            ->required()
                            ->options(function (Get $get) {
                                if ($get('user_id') === null) {
                                    return [];
                                }
                                $user = User::find($get('user_id'));

                                if ($user->companionRole) {
                                    $role = $user->companionRole->role;

                                    return [$role->value => $role->getLabel()];
                                }

                                return CompanionType::class;
                            }),
                    ]),

            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn (Builder $query) => $query->with('user')
                ->withoutGlobalScopes([
                    SoftDeletingScope::class,
                ]))
            ->recordTitleAttribute('user.name')
            ->columns([
                TextColumn::make('user.name')
                    ->label(__("Nom D'accompagnant")),

                TextColumn::make('user.phone')
                    ->label(__('Numéro de téléphone')),

                TextColumn::make('type')->badge(),

                TextColumn::make('created_at')
                    ->label(__('Ajouté le')),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                CreateAction::make(),
            ])
            ->recordActions([
                EditAction::make(),
                ForceDeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    ForceDeleteBulkAction::make(),
                ]),
            ]);
    }

    public function isReadOnly(): bool
    {
        $record = $this->getOwnerRecord();

        return $record->closed || filled($record->deleted_at);
    }
}
