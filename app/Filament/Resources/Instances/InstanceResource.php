<?php

namespace App\Filament\Resources\Instances;

use App\Enums\InstanceStatus;
use App\Enums\InstanceType;
use App\Filament\Resources\Instances\Pages\EditInstance;
use App\Filament\Resources\Instances\Pages\ListInstances;
use App\Filament\Resources\Instances\RelationManagers\CompanionsRelationManager;
use App\Filament\Resources\Instances\RelationManagers\DiagnosticsRelationManager;
use App\Filament\Resources\Instances\RelationManagers\FollowupsRelationManager;
use App\Filament\Resources\Instances\RelationManagers\RelapseRelationManager;
use App\Filament\Resources\Instances\RelationManagers\TreatmentsRelationManager;
use App\Models\User;
use App\Models\Victim\Instance;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ForceDeleteBulkAction;
use Filament\Actions\RestoreBulkAction;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\ToggleButtons;
use Filament\Resources\Resource;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class InstanceResource extends Resource
{
    protected static ?string $model = Instance::class;

    protected static ?int $navigationSort = 2;

    protected static string|\BackedEnum|null $navigationIcon = 'heroicon-o-archive-box-arrow-down';

    protected static ?string $recordTitleAttribute = 'code';

    protected static string|\UnitEnum|null $navigationGroup = 'Santé';

    protected static ?string $navigationLabel = 'Cas';

    protected static ?string $modelLabel = 'Cas';

    protected static ?string $pluralModelLabel = 'Cas';

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make()
                    ->schema([
                        Select::make('healthCenter.id')
                            ->relationship('healthCenter', 'name')
                            ->label(__('Centre de santé'))
                            ->disabled()
                            ->required(),

                        Select::make('survivor.id')
                            ->label(__('Code Survivant'))
                            ->relationship('survivor', 'code')
                            ->disabled()
                            ->required(),

                        ToggleButtons::make('type')
                            ->options(InstanceType::class)
                            ->inline()
                            ->disabled()
                            ->required(),

                        TextInput::make('code')
                            ->disabled()
                            ->required(),

                        Select::make('status')
                            ->options(InstanceStatus::class)
                            ->disabled()
                            ->required(),

                        Textarea::make('description')
                            ->rows(5)
                            ->nullable(),

                    ])
                    ->columnSpan(['lg' => fn (?Instance $record): int => $record instanceof Instance ? 2 : 3]),

                Section::make()
                    ->schema([
                        Placeholder::make('created_at')
                            ->hiddenOn('create')
                            ->label(__('Créé le'))
                            ->content(fn (Instance $record): ?string => $record->created_at?->diffForHumans()),

                        Placeholder::make('updated_at')
                            ->hiddenOn('create')
                            ->label(__('Mis à jour le'))
                            ->content(fn (Instance $record): ?string => $record->updated_at?->diffForHumans()),

                        Placeholder::make('deleted_at')
                            ->hiddenOn('create')
                            ->label(__('Supprimé le'))
                            ->content(fn (Instance $record): ?string => $record->deleted_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?Instance $record): bool => ! $record instanceof Instance),
            ])
            ->columns(['sm' => 3, 'lg' => null]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('survivor.code')
                    ->label(__('Code Survivant'))
                    ->searchable(),

                TextColumn::make('healthCenter.name')
                    ->label(__('Centre de santé'))
                    ->searchable(),

                TextColumn::make('code')
                    ->searchable(),

                TextColumn::make('type')
                    ->badge()
                    ->sortable(),

                IconColumn::make('status'),

                TextColumn::make('description')
                    ->wrap()
                    ->limit(100),

                TextColumn::make('created_at')
                    ->label(__('Créé le'))
                    ->since()
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('deleted_at')
                    ->label(__('Supprimé le'))
                    ->since()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                TrashedFilter::make(),
            ])
            ->recordActions([
                EditAction::make(),
            ])
            ->defaultSort('id', 'desc')
            ->paginated([10, 25, 50, 100])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                    ForceDeleteBulkAction::make(),
                    RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            DiagnosticsRelationManager::class,
            TreatmentsRelationManager::class,
            CompanionsRelationManager::class,
            RelapseRelationManager::class,
            FollowupsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListInstances::route('/'),
            'edit' => EditInstance::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        /**
         * @var User $user
         */
        $user = auth()->user();

        if ($user->isAPS()) {
            $ids = $user->healthCenters()->pluck('health_centers.id')->toArray();

            return parent::getEloquentQuery()
                ->with(['healthCenter', 'survivor', 'companions'])
                ->whereIn('health_center_id', $ids);
        }

        return parent::getEloquentQuery()
            ->with(['healthCenter', 'survivor', 'companions'])
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
