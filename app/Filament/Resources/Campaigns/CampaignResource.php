<?php

namespace App\Filament\Resources\Campaigns;

use App\Enums\UserRole;
use App\Filament\Resources\Campaigns\Pages\ManageCampaigns;
use App\Models\Campaign;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\ViewAction;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class CampaignResource extends Resource
{
    protected static ?string $model = Campaign::class;

    protected static string|\BackedEnum|null $navigationIcon = 'heroicon-o-megaphone';

    protected static ?int $navigationSort = 7;

    protected static string|\UnitEnum|null $navigationGroup = 'Global';

    protected static ?string $navigationLabel = 'Campagnes';

    protected static ?string $modelLabel = 'Campagne';

    protected static ?string $pluralModelLabel = 'Campagnes';

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make('health_center_id')
                    ->label(__('Centre de santé (optionnel)'))
                    ->nullable()
                    ->native(false)
                    ->relationship('healthCenter', 'name'),

                Select::make('user_role')
                    ->label(__('Rôle (optionnel)'))
                    ->options(UserRole::options())
                    ->native(false)
                    ->nullable(),

                TextInput::make('title')
                    ->label(__('Titre'))
                    ->required()
                    ->maxLength(255),

                Textarea::make('body')
                    ->label(__('Message'))
                    ->required()
                    ->rows(5)
                    ->maxLength(2000)
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('healthCenter.name')
                    ->label(__('Centre de santé'))
                    ->searchable(),

                TextColumn::make('user_role')
                    ->label(__('Rôle'))
                    ->badge()
                    ->sortable(),

                TextColumn::make('title')
                    ->label(__('Titre'))
                    ->searchable(),

                TextColumn::make('created_at')
                    ->label(__('Créé le'))
                    ->since()
                    ->sortable(),

            ])
            ->filters([])
            ->recordActions([
                ViewAction::make(),

                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => ManageCampaigns::route('/'),
        ];
    }
}
