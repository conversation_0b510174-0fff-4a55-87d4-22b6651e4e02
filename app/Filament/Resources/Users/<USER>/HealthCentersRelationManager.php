<?php

namespace App\Filament\Resources\Users\RelationManagers;

use App\Filament\Resources\HealthCenters\HealthCenterResource;
use Filament\Actions\Action;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;

class HealthCentersRelationManager extends RelationManager
{
    protected static string $relationship = 'healthCenters';

    protected static ?string $modelLabel = 'Centre de santé';

    protected static ?string $title = 'Centres de santé';

    public function form(Schema $schema): Schema
    {
        return $schema
            ->components([]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                TextColumn::make('name')
                    ->label(__('Nom du centre de santé')),

                TextColumn::make('responsibility')
                    ->label(__('Responsabilité'))
                    ->badge(),
            ])
            ->filters([])
            ->headerActions([])
            ->recordActions([
                Action::make(__('Voir Plus'))
                    ->color('gray')
                    ->url(function (Action $action): string {
                        $record = $action->getRecord();

                        return HealthCenterResource::getUrl('view', ['record' => $record->id]);
                    }),
            ])
            ->toolbarActions([]);
    }

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool
    {
        return ! $ownerRecord->isRoot();
    }
}
