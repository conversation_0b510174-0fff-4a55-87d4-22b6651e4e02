<?php

namespace App\Filament\Resources\Relapses;

use App\Filament\Resources\Instances\InstanceResource;
use App\Filament\Resources\Relapses\Pages\ListRelapses;
use App\Models\User;
use App\Models\Victim\Instance;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\ViewAction;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class RelapseResource extends Resource
{
    protected static ?string $model = Instance::class;

    protected static ?int $navigationSort = 3;

    protected static string|\BackedEnum|null $navigationIcon = 'heroicon-o-arrow-trending-down';

    protected static string|\UnitEnum|null $navigationGroup = 'Santé';

    protected static ?string $navigationLabel = 'Rechutes';

    protected static ?string $modelLabel = 'Rechute';

    protected static ?string $pluralModelLabel = 'Rechutes';

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('relapse.created_at')
                    ->label(__('Signalé le'))
                    ->since()
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('survivor.code')
                    ->label(__('Code Survivant'))
                    ->searchable(),

                TextColumn::make('code')
                    ->label(__('Code Cas'))
                    ->searchable(),

            ])
            ->filters([])
            ->defaultSort('relapse.created_at', 'desc')
            ->paginated([10, 25, 50, 100])
            ->recordActions([
                ViewAction::make()
                    ->url(function (ViewAction $action): string {
                        $record = $action->getRecord();

                        return InstanceResource::getUrl('edit', [
                            'record' => $record->id,
                            'activeRelationManager' => 3,
                        ]);
                    }),
            ])
            ->toolbarActions([
                BulkActionGroup::make([]),
            ]);
    }

    public static function getEloquentQuery(): Builder
    {
        /**
         * @var User $user
         */
        $user = auth()->user();

        if ($user->isAPS()) {
            $ids = $user->healthCenters()->pluck('health_centers.id')->toArray();

            return parent::getEloquentQuery()
                ->with(['survivor', 'relapse'])
                ->has('relapse')
                ->whereIn('health_center_id', $ids);
        }

        return parent::getEloquentQuery()
            ->with(['survivor', 'relapse'])
            ->has('relapse')
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => ListRelapses::route('/'),
        ];
    }
}
