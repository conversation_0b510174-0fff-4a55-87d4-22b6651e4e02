<?php

namespace App\Filament\Resources\Activities;

use App\Filament\Resources\Activities\Pages\ListActivities;
use App\Filament\Resources\Activities\Pages\ViewActivity;
use App\Models\Activity;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\ViewAction;
use Filament\Forms\Components\KeyValue;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class ActivityResource extends Resource
{
    protected static ?string $model = Activity::class;

    protected static ?int $navigationSort = 8;

    protected static string|\UnitEnum|null $navigationGroup = 'Système';

    protected static ?string $navigationLabel = 'Activités';

    protected static ?string $modelLabel = 'Activité';

    protected static ?string $pluralModelLabel = 'Activités';

    protected static string|\BackedEnum|null $navigationIcon = 'heroicon-o-arrow-path-rounded-square';

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make('user_id')
                    ->label(__('Utilisateur'))
                    ->relationship('user', 'name'),

                TextInput::make('model')
                    ->label(__('Modèle'))
                    ->required(),

                Textarea::make('action')
                    ->label(__('Action'))
                    ->required()
                    ->columnSpanFull(),

                KeyValue::make('attributes')
                    ->label(__('Attributs modifiés'))
                    ->disabled()
                    ->columnSpanFull(),

                KeyValue::make('data')
                    ->label(__('Données'))
                    ->disabled()
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('user.name')
                    ->label(__('Utilisateur'))
                    ->searchable(),

                TextColumn::make('model')
                    ->label(__('Modèle'))
                    ->searchable(),

                TextColumn::make('action')
                    ->badge()
                    ->label(__('Action'))
                    ->sortable(),

                TextColumn::make('created_at')
                    ->label(__('Créé le'))
                    ->since()
                    ->sortable(),
            ])
            ->filters([])
            ->defaultSort('id', 'desc')
            ->paginated([10, 25, 50])
            ->recordActions([
                ViewAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([]),
            ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListActivities::route('/'),
            'view' => ViewActivity::route('/{record}'),
        ];
    }
}
