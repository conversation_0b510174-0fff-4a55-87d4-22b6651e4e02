<?php

namespace App\Filament\Resources\HealthZones;

use App\Filament\Resources\HealthZones\Pages\CreateHealthZone;
use App\Filament\Resources\HealthZones\Pages\EditHealthZone;
use App\Filament\Resources\HealthZones\Pages\ListHealthZones;
use App\Filament\Resources\HealthZones\Pages\ViewHealthZone;
use App\Filament\Resources\HealthZones\RelationManagers\HealthCentersRelationManager;
use App\Models\Health\HealthZone;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ForceDeleteBulkAction;
use Filament\Actions\RestoreBulkAction;
use Filament\Actions\ViewAction;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Resource;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class HealthZoneResource extends Resource
{
    protected static ?string $model = HealthZone::class;

    protected static ?int $navigationSort = 0;

    protected static string|\UnitEnum|null $navigationGroup = 'Santé';

    protected static ?string $navigationLabel = 'Zones de santé';

    protected static ?string $modelLabel = 'Zone de santé';

    protected static ?string $pluralModelLabel = 'Zones de santé';

    protected static string|\BackedEnum|null $navigationIcon = 'heroicon-o-cursor-arrow-rays';

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make()
                    ->schema([
                        TextInput::make('name')
                            ->label(__('Nom'))
                            ->unique(ignoreRecord: true)
                            ->columnSpanFull()
                            ->required(),

                        TextInput::make('province')
                            ->label(__('Province'))
                            ->required(),

                        TextInput::make('population_served')
                            ->label(__('Population'))
                            ->required()
                            ->numeric(),
                    ])
                    ->columns(2)
                    ->columnSpan(['lg' => fn (?HealthZone $record): int => $record instanceof HealthZone ? 2 : 3]),

                Section::make()
                    ->schema([
                        Placeholder::make('created_at')
                            ->label(__('Créé le'))
                            ->content(fn (HealthZone $record): ?string => $record->created_at?->diffForHumans()),

                        Placeholder::make('updated_at')
                            ->label(__('Mis à jour le'))
                            ->content(fn (HealthZone $record): ?string => $record->updated_at?->diffForHumans()),

                        Placeholder::make('health_centers_count')
                            ->label(__('Centres de santé'))
                            ->content(fn (HealthZone $record): ?int => $record->healthCenters()->count()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?HealthZone $record): bool => ! $record instanceof HealthZone),
            ])
            ->columns(['sm' => 3, 'lg' => null]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label(__('Nom'))
                    ->searchable(),

                TextColumn::make('health_centers_count')
                    ->counts('healthCenters')
                    ->label(__('Centres de santé'))
                    ->sortable(),

                TextColumn::make('population_served')
                    ->label(__('Population servie'))
                    ->numeric()
                    ->sortable(),

                TextColumn::make('deleted_at')
                    ->label(__('Supprimé le'))
                    ->since()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('created_at')
                    ->label(__('Créé le'))
                    ->since()
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('updated_at')
                    ->label(__('Mis à jour le'))
                    ->since()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                TrashedFilter::make(),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                    ForceDeleteBulkAction::make(),
                    RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            HealthCentersRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListHealthZones::route('/'),
            'create' => CreateHealthZone::route('/create'),
            'view' => ViewHealthZone::route('/{record}'),
            'edit' => EditHealthZone::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
