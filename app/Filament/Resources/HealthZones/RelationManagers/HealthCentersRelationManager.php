<?php

namespace App\Filament\Resources\HealthZones\RelationManagers;

use App\Filament\Resources\HealthCenters\HealthCenterResource;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\ViewAction;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class HealthCentersRelationManager extends RelationManager
{
    protected static string $relationship = 'healthCenters';

    protected static ?string $modelLabel = 'Centre de santé';

    protected static ?string $title = 'Centres de santé';

    public function form(Schema $schema): Schema
    {
        return $schema
            ->components([]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                TextColumn::make('name')
                    ->label(__('Centre de santé'))
                    ->searchable(),

                TextColumn::make('address')
                    ->label(__("L'adresse"))
                    ->limit(50)
                    ->searchable(),

                TextColumn::make('phone')
                    ->label(__('Numéro de téléphone'))
                    ->searchable(),

                TextColumn::make('created_at')
                    ->label(__('Créé le'))
                    ->since()
                    ->sortable(),
            ])
            ->filters([])
            ->headerActions([
            ])
            ->recordActions([
                ViewAction::make()
                    ->url(function (ViewAction $action): string {
                        $record = $action->getRecord();

                        return HealthCenterResource::getUrl('edit', ['record' => $record->id]);
                    }),

            ])
            ->toolbarActions([
                BulkActionGroup::make([]),
            ]);
    }
}
