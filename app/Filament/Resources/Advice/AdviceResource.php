<?php

namespace App\Filament\Resources\Advice;

use App\Filament\Resources\Advice\Pages\CreateAdvice;
use App\Filament\Resources\Advice\Pages\EditAdvice;
use App\Filament\Resources\Advice\Pages\ListAdvice;
use App\Models\Advice;
use App\Validator\Attachment;
use Filament\Actions\Action;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class AdviceResource extends Resource
{
    protected static ?string $model = Advice::class;

    protected static string|\BackedEnum|null $navigationIcon = 'heroicon-o-chat-bubble-left-right';

    protected static ?int $navigationSort = 4;

    protected static string|\UnitEnum|null $navigationGroup = 'Global';

    protected static ?string $navigationLabel = 'Conseils';

    protected static ?string $modelLabel = 'Conseil';

    protected static ?string $pluralModelLabel = 'Conseils';

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make('advice_language_id')
                    ->native(false)
                    ->label(__('Langue'))
                    ->relationship('adviceLanguage', 'name')
                    ->preload()
                    ->searchable()
                    ->required()
                    ->lazy()
                    ->createOptionForm([
                        TextInput::make('name')
                            ->label(__('Nom de la langue'))
                            ->required()
                            ->disabledOn('edit')
                            ->unique(ignoreRecord: true)
                            ->maxLength(50),
                    ])
                    ->createOptionAction(fn (Action $action): Action => $action
                        ->modalHeading(__('Créer Langue'))
                        ->modalSubmitActionLabel(__('Créer Langue'))
                        ->modalWidth('md')),

                TextInput::make('title')
                    ->label(__('Titre'))
                    ->required(),

                RichEditor::make('description')
                    ->label(__('Contenu'))
                    ->fileAttachmentsDirectory(Attachment::ADVICE_UPLOAD_DIRECTORY)
                    ->required()
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('adviceLanguage.name')
                    ->label(label: __('Langue'))
                    ->sortable(),

                TextColumn::make('title')
                    ->label(label: __('Titre'))
                    ->limit(50)
                    ->searchable(),

                TextColumn::make('created_at')
                    ->since()
                    ->sortable()
                    ->label(label: __('Ajouté le'))
                    ->toggleable(),

                TextColumn::make('updated_at')
                    ->since()
                    ->label(label: __('Modifié le'))
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([])
            ->paginated([10, 25, 50])
            ->recordActions([
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListAdvice::route('/'),
            'create' => CreateAdvice::route('/create'),
            'edit' => EditAdvice::route('/{record}/edit'),
        ];
    }
}
