<?php

namespace App\Filament\Pages;

use App\Models\Health\HealthCenter;
use Filament\Actions\Action;
use Filament\Actions\ActionGroup;
use Filament\Forms\Components as FormsComponents;
use Filament\Pages\Concerns\InteractsWithFormActions;
use Filament\Pages\Dashboard as BaseDashboard;
use Filament\Pages\Page;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Components\Utilities\Get;
use Filament\Schemas\Schema;
use Filament\Widgets\Concerns\InteractsWithPageFilters;

class Report extends Page
{
    use BaseDashboard\Concerns\HasFiltersForm;
    use InteractsWithFormActions;
    use InteractsWithPageFilters;

    protected static ?string $title = 'Rapport';

    protected static string|\BackedEnum|null $navigationIcon = 'heroicon-o-document-text';

    protected string $view = 'filament.pages.report';

    public static function canAccess(): bool
    {
        return auth()->user()->isRoot();
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('overview_report')
                ->label(__("Rapport d'aperçu"))
                ->url(route('report.overview'))
                ->keyBindings(['mod+g'])
                ->color('info'),
        ];
    }

    /**
     * @return array<Action | ActionGroup>
     */
    protected function getFormActions(): array
    {
        return [
            Action::make('generate')
                ->label(__('Générer le rapport'))
                ->url(fn () => route('report.parameterized', $this->filters))
                ->keyBindings(['mod+s']),
        ];
    }

    public function filtersForm(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make()
                    ->schema([
                        FormsComponents\Select::make('type')
                            ->native(false)
                            ->default('instance')
                            ->options([
                                'instance' => __('Cas'),
                                'relapse' => __('Rechute'),
                            ])
                            ->label(__('Type Rapport')),

                        FormsComponents\Select::make('healthCenters')
                            ->native(false)
                            ->multiple()
                            ->options(HealthCenter::all()->pluck('name', 'id'))
                            ->searchable()
                            ->label(__('Centres de Santé')),
                    ])
                    ->columnSpanFull()
                    ->columns(2),

                Section::make()
                    ->schema([
                        FormsComponents\DatePicker::make('startDate')
                            ->native(false)
                            ->placeholder('dd/mm/yyyy')
                            ->label(__('Date de début'))
                            ->maxDate(fn (Get $get) => $get('endDate') ?: now()),

                        FormsComponents\DatePicker::make('endDate')
                            ->native(false)
                            ->label(__('Date de fin'))
                            ->placeholder('dd/mm/yyyy')
                            ->minDate(fn (Get $get): mixed => $get('startDate') ?: null)
                            ->maxDate(now()),
                    ])
                    ->columnSpanFull()
                    ->columns(2),

                Section::make()
                    ->schema([
                        FormsComponents\Select::make('format')
                            ->native(true)
                            ->default('excel')
                            ->options([
                                'excel' => __('Excel'),
                                'pdf' => __('PDF'),
                            ])
                            ->label(__('Format')),
                    ])
                    ->columnSpanFull()
                    ->columns(2),
            ]);
    }
}
