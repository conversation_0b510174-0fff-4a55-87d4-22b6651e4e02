<?php

namespace App\Filament\Pages;

use App\Models\Health\HealthCenter;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Pages\Dashboard as BaseDashboard;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Components\Utilities\Get;
use Filament\Schemas\Schema;

class Dashboard extends BaseDashboard
{
    use BaseDashboard\Concerns\HasFiltersForm;

    public function filtersForm(Schema $schema): Schema
    {
        $auth = auth()->user();

        if (! $auth->isRoot()) {
            return $schema;
        }

        return $schema
            ->components([
                Section::make()
                    ->columnSpanFull()
                    ->schema([
                        Select::make('healthCenter')
                            ->native(false)
                            ->options(HealthCenter::all()->pluck('name', 'id'))
                            ->searchable()
                            ->label(__('Centre de Santé')),

                        DatePicker::make('startDate')
                            ->native(false)
                            ->placeholder('dd/mm/yyyy')
                            ->label(__('Date de début'))
                            ->maxDate(fn (Get $get) => $get('endDate') ?: now()),

                        DatePicker::make('endDate')
                            ->label(__('Date de fin'))
                            ->native(false)
                            ->placeholder('dd/mm/yyyy')
                            ->minDate(fn (Get $get): mixed => $get('startDate') ?: null)
                            ->maxDate(now()),
                    ])
                    ->columns(3),
            ]);
    }
}
