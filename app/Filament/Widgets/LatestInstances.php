<?php

namespace App\Filament\Widgets;

use App\Filament\Resources\Instances\InstanceResource;
use App\Models\Victim\Instance;
use Filament\Actions\Action;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;

class LatestInstances extends BaseWidget
{
    protected static ?string $heading = 'Derniers Cas';

    protected int|string|array $columnSpan = 'full';

    protected static ?int $sort = 4;

    public static function canView(): bool
    {
        return auth()->user()->isRoot();
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(Instance::query())
            ->defaultPaginationPageOption(5)
            ->defaultSort('created_at', 'desc')
            ->paginated([10, 25, 50, 100])
            ->columns([
                TextColumn::make('healthCenter.name')
                    ->label(__('Centre de santé'))
                    ->searchable(),

                TextColumn::make('survivor.code')
                    ->label(__('Code Survivant'))
                    ->searchable(),

                TextColumn::make('code')
                    ->searchable(),

                TextColumn::make('type')
                    ->badge()
                    ->sortable(),

                IconColumn::make('status'),

                TextColumn::make('created_at')
                    ->label(__('Ajouté le'))
                    ->since()
                    ->sortable()
                    ->toggleable(),
            ])
            ->recordActions([
                Action::make('open')
                    ->label(__('Ouvrir'))
                    ->url(fn (Instance $record): string => InstanceResource::getUrl('edit', ['record' => $record])),
            ]);
    }
}
