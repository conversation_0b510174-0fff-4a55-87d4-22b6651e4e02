<?php

namespace App\Filament\Widgets;

use App\Models\Victim\Relapse;
use Filament\Widgets\ChartWidget;
use Flowframe\Trend\Trend;
use Flowframe\Trend\TrendValue;

class Relapse<PERSON>hart extends ChartWidget
{
    protected ?string $heading = 'Rechutes par mois';

    protected static ?int $sort = 2;

    protected function getType(): string
    {
        return 'line';
    }

    public static function canView(): bool
    {
        return auth()->user()->isRoot();
    }

    protected function getData(): array
    {
        $data = Trend::model(Relapse::class)
            ->between(
                start: now()->subMonths(11),
                end: now(),
            )
            ->perMonth()
            ->count();

        return [
            'datasets' => [
                [
                    'label' => __('Rechutes'),
                    'fill' => 'start',
                    'data' => $data->map(fn (TrendValue $value): mixed => $value->aggregate),
                ],
            ],
            'labels' => $data->map(fn (TrendValue $value): string => $value->date),
        ];
    }
}
