<?php

namespace App\Filament\Widgets;

use App\Enums\InstanceStatus;
use App\Models\Victim\Instance;
use App\Models\Victim\Treatment;
use Filament\Widgets\Concerns\InteractsWithPageFilters;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Carbon;

class PerformanceMetricsWidget extends BaseWidget
{
    use InteractsWithPageFilters;

    protected ?string $heading = 'Indicateurs de Performance';

    protected static ?int $sort = 0;

    public static function canView(): bool
    {
        if (auth()->user()->isRoot()) {
            return true;
        }

        return (bool) auth()->user()->isAPS();
    }

    protected function getStats(): array
    {
        $startDate = ($this->pageFilters['startDate'] ?? null) === null ?
            now()->subMonths(3) :
            Carbon::parse($this->pageFilters['startDate']);

        $endDate = ($this->pageFilters['endDate'] ?? null) === null ?
            now() :
            Carbon::parse($this->pageFilters['endDate']);

        $healthCenterId = $this->pageFilters['healthCenter'] ?? null;

        $user = auth()->user();
        $instanceQuery = Instance::query();

        // Apply user-specific filters
        if ($user->isAPS()) {
            $healthCenterIds = $user->healthCenters()->pluck('health_centers.id')->toArray();
            $instanceQuery->whereIn('health_center_id', $healthCenterIds);
        }

        // Apply dashboard filters
        if ($healthCenterId) {
            $instanceQuery->where('health_center_id', $healthCenterId);
        }

        $instanceQuery->whereBetween('created_at', [$startDate, $endDate]);

        // Calculate metrics
        $totalCases = $instanceQuery->count();
        $closedCases = $instanceQuery->clone()->where('status', InstanceStatus::CLOSED)->count();
        $casesWithTreatment = $instanceQuery->clone()->whereHas('treatments')->count();
        $casesWithCompanions = $instanceQuery->clone()->whereHas('companions')->count();

        // Calculate rates
        $closureRate = $totalCases > 0 ? round(($closedCases / $totalCases) * 100, 1) : 0;
        $treatmentRate = $totalCases > 0 ? round(($casesWithTreatment / $totalCases) * 100, 1) : 0;
        $companionRate = $totalCases > 0 ? round(($casesWithCompanions / $totalCases) * 100, 1) : 0;

        // Calculate average time to treatment
        $avgTimeToTreatment = $this->calculateAverageTimeToTreatment($instanceQuery->clone());

        return [
            Stat::make(__('Taux de Fermeture'), $closureRate.'%')
                ->description(__(':closed sur :total cas fermés', ['closed' => $closedCases, 'total' => $totalCases]))
                ->descriptionIcon('heroicon-m-check-circle')
                ->color($closureRate >= 70 ? 'success' : ($closureRate >= 50 ? 'warning' : 'danger')),

            Stat::make(__('Taux de Traitement'), $treatmentRate.'%')
                ->description(__(':treated sur :total cas traités', ['treated' => $casesWithTreatment, 'total' => $totalCases]))
                ->descriptionIcon('heroicon-m-heart')
                ->color($treatmentRate >= 80 ? 'success' : ($treatmentRate >= 60 ? 'warning' : 'danger')),

            Stat::make(__('Taux d\'Accompagnement'), $companionRate.'%')
                ->description(__(':accompanied sur :total cas accompagnés', ['accompanied' => $casesWithCompanions, 'total' => $totalCases]))
                ->descriptionIcon('heroicon-m-user-group')
                ->color($companionRate >= 70 ? 'success' : ($companionRate >= 50 ? 'warning' : 'danger')),

            Stat::make(__('Temps Moyen au Traitement'), $avgTimeToTreatment)
                ->description(__('Délai moyen entre ouverture et premier traitement'))
                ->descriptionIcon('heroicon-m-clock')
                ->color('info'),
        ];
    }

    private function calculateAverageTimeToTreatment($instanceQuery): string
    {
        $instances = $instanceQuery->with(['treatments' => function ($query): void {
            $query->orderBy('created_at', 'asc');
        }])->get();

        $totalDays = 0;
        $count = 0;

        foreach ($instances as $instance) {
            $firstTreatment = $instance->treatments->first();
            if ($firstTreatment) {
                $days = $instance->created_at->diffInDays($firstTreatment->created_at);
                $totalDays += $days;
                $count++;
            }
        }

        if ($count === 0) {
            return __('N/A');
        }

        $avgDays = round($totalDays / $count, 1);
        if ($avgDays < 1) {
            return __('< 1 jour');
        }

        if ($avgDays < 7) {
            return $avgDays.' '.__('jours');
        }
        $weeks = round($avgDays / 7, 1);

        return $weeks.' '.__('semaines');
    }
}
