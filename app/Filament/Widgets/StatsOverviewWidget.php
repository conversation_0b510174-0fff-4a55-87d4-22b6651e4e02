<?php

namespace App\Filament\Widgets;

use App\Enums\InstanceStatus;
use App\Enums\InstanceType;
use App\Models\Health\HealthCenter;
use App\Models\Victim\Instance;
use App\Models\Victim\Relapse;
use Filament\Widgets\Concerns\InteractsWithPageFilters;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Flowframe\Trend\Trend;
use Flowframe\Trend\TrendValue;
use Illuminate\Support\Carbon;
use Illuminate\Support\Number;

class StatsOverviewWidget extends BaseWidget
{
    use InteractsWithPageFilters;

    protected static ?int $sort = 1;

    public static function canView(): bool
    {
        return auth()->user()->isRoot();
    }

    protected function getStats(): array
    {
        $startDate = ($this->pageFilters['startDate'] ?? null) === null ?
            now()->subMonths(2) :
            Carbon::parse($this->pageFilters['startDate']);

        $endDate = ($this->pageFilters['endDate'] ?? null) === null ?
            now() :
            Carbon::parse($this->pageFilters['endDate']);

        $healthCenterId = $this->pageFilters['healthCenter'] ?? null;

        $instanceQuery = match ($healthCenterId) {
            null => Instance::query(),
            default => HealthCenter::where('id', $healthCenterId)
                ->first()
                ->instances()
                ->getQuery(),
        };

        $relapseQuery = match ($healthCenterId) {
            null => Relapse::query(),
            default => HealthCenter::where('id', $healthCenterId)
                ->first()
                ->relapses()
                ->getQuery(),
        };

        // Get trend data
        $relapseData = Trend::query($relapseQuery)
            ->dateColumn('relapses.created_at')
            ->between(start: $startDate, end: $endDate)
            ->perMonth()
            ->count();

        $instanceData = Trend::query($instanceQuery)
            ->dateColumn('instances.created_at')
            ->between(start: $startDate, end: $endDate)
            ->perMonth()
            ->count();

        // Calculate additional metrics
        $totalInstances = $instanceQuery->count();
        $openCases = $instanceQuery->where('status', InstanceStatus::OPEN)->count();
        $instanceQuery->where('status', InstanceStatus::CLOSED)->count();
        $newCases = $instanceQuery->where('type', InstanceType::NEW)->count();
        $relapseCases = $instanceQuery->where('type', InstanceType::RELAPSE)->count();

        // Calculate percentages
        $openCasePercentage = $totalInstances > 0 ? round(($openCases / $totalInstances) * 100, 1) : 0;
        $relapseRate = $totalInstances > 0 ? round(($relapseCases / $totalInstances) * 100, 1) : 0;

        $formatNumber = function (int $number): string {
            if ($number < 1000) {
                return (string) Number::format($number, 0);
            }

            if ($number < 1000000) {
                return Number::format($number / 1000, 2).'k';
            }

            return Number::format($number / 1000000, 2).'m';
        };

        return [
            Stat::make(__('Total des Cas'), $formatNumber($totalInstances))
                ->description(__('Nouveaux: :new | Rechutes: :relapse', ['new' => $newCases, 'relapse' => $relapseCases]))
                ->descriptionIcon('heroicon-m-chart-bar')
                ->chart($instanceData->map(fn (TrendValue $value): mixed => $value->aggregate)->toArray())
                ->color('primary'),

            Stat::make(__('Cas Ouverts'), $formatNumber($openCases))
                ->description(__(':percentage% du total', ['percentage' => $openCasePercentage]))
                ->descriptionIcon('heroicon-m-folder-open')
                ->color('success'),

            Stat::make(__('Taux de Rechute'), $relapseRate.'%')
                ->description(__(':count rechutes détectées', ['count' => $relapseData->sum('aggregate')]))
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->chart($relapseData->map(fn (TrendValue $value): mixed => $value->aggregate)->toArray())
                ->color('danger'),

            Stat::make(__('Centres de Santé'), $formatNumber(HealthCenter::count()))
                ->description(__('Actifs dans le système'))
                ->descriptionIcon('heroicon-m-building-office-2')
                ->color('info'),
        ];
    }
}
