<?php

namespace App\Filament\Auth;

use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Component;

class EditProfile extends \Filament\Auth\Pages\EditProfile
{
    protected function getPhoneFormComponent(): Component
    {
        return TextInput::make('phone')
            ->label(__('Numéro de téléphone'))
            ->rules(['phone'])
            ->required()
            ->maxLength(255)
            ->unique(ignoreRecord: true);
    }

    protected function getForms(): array
    {
        return [
            'form' => $this->form(
                $this->makeForm()
                    ->components([
                        $this->getNameFormComponent(),
                        $this->getPhoneFormComponent(),
                        $this->getEmailFormComponent(),
                        $this->getPasswordFormComponent(),
                        $this->getPasswordConfirmationFormComponent(),
                    ])
                    ->operation('edit')
                    ->model($this->getUser())
                    ->statePath('data')
                    ->inlineLabel(! static::isSimple()),
            ),
        ];
    }
}
