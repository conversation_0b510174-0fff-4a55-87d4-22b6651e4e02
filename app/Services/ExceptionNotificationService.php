<?php

namespace App\Services;

use App\Notifications\ExceptionOccurredNotification;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Throwable;

class ExceptionNotificationService
{
    /**
     * Send SMS notification for exceptions to admin users
     */
    public function notifyException(Throwable $exception, ?Request $request = null): void
    {
        // Check if SMS notifications are enabled
        if (! $this->shouldSendNotification($exception)) {
            return;
        }

        // Check rate limiting to prevent spam
        if ($this->isRateLimited($exception)) {
            Log::info('Exception SMS notification rate limited', [
                'exception' => $exception::class,
                'message' => $exception->getMessage(),
            ]);

            return;
        }

        try {
            $url = $request instanceof Request ? $request->fullUrl() : 'N/A';
            $userId = auth()->id();
            $context = $this->buildContext($request);

            // Get the configured phone number for notifications
            $notificationPhone = $this->getNotificationPhone();

            if ($notificationPhone === null || $notificationPhone === '' || $notificationPhone === '0') {
                Log::info('No phone number configured for exception SMS notifications');

                return;
            }

            // Create a simple notifiable object with the phone number
            $notifiable = $this->createNotifiable($notificationPhone);

            // Send notification
            $notifiable->notify(new ExceptionOccurredNotification(
                exception: $exception,
                url: $url,
                userId: $userId,
                context: $context
            ));

            Log::info('Exception SMS notification sent', [
                'exception' => $exception::class,
                'phone' => $notificationPhone,
            ]);
        } catch (Throwable $notificationException) {
            // Don't let notification failures break the application
            Log::error('Failed to send exception SMS notification', [
                'original_exception' => $exception::class,
                'notification_exception' => $notificationException->getMessage(),
            ]);
        }
    }

    /**
     * Check if SMS notifications should be sent for this exception
     */
    private function shouldSendNotification(Throwable $exception): bool
    {
        // Check if phone number is configured
        if (in_array($this->getNotificationPhone(), [null, '', '0'], true)) {
            return false;
        }

        // Check environment restriction
        $allowedEnvironments = explode(',', (string) config('app.sms_exception_notifications_environment', 'production'));
        if (! in_array(config('app.env'), $allowedEnvironments)) {
            return false;
        }

        // Skip certain exception types that shouldn't trigger SMS
        $skipExceptions = [
            AuthenticationException::class,
            ValidationException::class,
            NotFoundHttpException::class,
            MethodNotAllowedHttpException::class,
            ModelNotFoundException::class,
        ];

        foreach ($skipExceptions as $skipException) {
            if ($exception instanceof $skipException) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if we're hitting rate limits for this exception type
     */
    private function isRateLimited(Throwable $exception): bool
    {
        $rateLimit = config('app.sms_exception_notifications_rate_limit', 5);
        $cacheKey = 'exception_sms_rate_limit:'.md5($exception::class.$exception->getMessage());

        $count = Cache::get($cacheKey, 0);

        if ($count >= $rateLimit) {
            return true;
        }

        // Increment counter with 1 hour expiry
        Cache::put($cacheKey, $count + 1, now()->addHour());

        return false;
    }

    /**
     * Get the configured phone number for exception notifications
     */
    private function getNotificationPhone(): ?string
    {
        $phone = config('app.sms_exception_notifications_phone');

        if (empty($phone)) {
            return null;
        }

        // Validate phone number format
        if (! $this->isValidPhoneNumber($phone)) {
            Log::warning('Invalid phone number configured for SMS exception notifications', [
                'phone' => $phone,
            ]);

            return null;
        }

        return $phone;
    }

    /**
     * Validate phone number using regex
     * Supports international formats like +1234567890, +33123456789, etc.
     */
    private function isValidPhoneNumber(string $phone): bool
    {
        // Remove any whitespace
        $phone = preg_replace('/\s+/', '', $phone);

        // Basic international phone number regex
        // Supports: +[country code][number] format
        // Examples: +1234567890, +33123456789, +250123456789
        $pattern = '/^\+[1-9]\d{1,14}$/';

        return preg_match($pattern, (string) $phone) === 1;
    }

    /**
     * Create a notifiable object for SMS
     */
    private function createNotifiable(string $phone): object
    {
        return new class($phone)
        {
            use Notifiable;

            public function __construct(private string $phone) {}

            public function routeNotificationForTwilio(): string
            {
                return $this->phone;
            }

            public function withoutRelations(): static
            {
                return $this;
            }

            public function toArray(): array
            {
                return ['phone' => $this->phone];
            }
        };
    }

    /**
     * Build context information for the notification
     */
    private function buildContext(?Request $request): ?array
    {
        if (! $request instanceof Request) {
            return null;
        }

        return [
            'method' => $request->method(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'referer' => $request->header('referer'),
        ];
    }
}
