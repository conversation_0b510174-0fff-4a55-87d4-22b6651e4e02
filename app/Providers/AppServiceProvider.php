<?php

namespace App\Providers;

use App\Models\Health\HealthCenter;
use App\Models\Health\HealthZone;
use App\Models\User;
use App\Models\Victim\Instance;
use App\Policies\HealthCenterPolicy;
use App\Policies\HealthZonePolicy;
use App\Policies\InstancePolicy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;
use Laravel\Pulse\Facades\Pulse;
use URL;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        if (app()->isProduction()) {
            URL::forceScheme('https');
        }

        Model::automaticallyEagerLoadRelationships();

        Gate::policy(HealthCenter::class, HealthCenterPolicy::class);

        Gate::policy(HealthZone::class, HealthZonePolicy::class);

        Gate::policy(Instance::class, InstancePolicy::class);

        Gate::define('viewPulse', fn (User $user): bool => $user->isRoot());

        Pulse::user(fn ($user): array => [
            'name' => $user->name,
            'extra' => $user->phone,
        ]);

        Builder::macro('whereLike', function ($attributes, string $searchTerm): object {
            $this->where(function (Builder $query) use ($attributes, $searchTerm): void {
                foreach (Arr::wrap($attributes) as $attribute) {
                    $query->when(
                        str_contains($attribute, '.'),
                        function (Builder $query) use ($attribute, $searchTerm): void {
                            [$relationName, $relationAttribute] = explode('.', (string) $attribute);

                            $query->orWhereHas($relationName, function (Builder $query) use ($relationAttribute, $searchTerm): void {
                                $query->where($relationAttribute, 'LIKE', "%{$searchTerm}%");
                            });
                        },
                        function (Builder $query) use ($attribute, $searchTerm): void {
                            $query->orWhere($attribute, 'LIKE', "%{$searchTerm}%");
                        }
                    );
                }
            });

            return $this;
        });
    }
}
