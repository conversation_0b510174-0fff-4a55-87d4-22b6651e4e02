<?php

namespace App\Console\Commands;

use App\Models\Activity;
use Illuminate\Console\Command;

class DeleteOldActivityRecords extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:activity-purge {--months=1}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete activity records from a model that are older than a given number of months';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $months = $this->option('months');
        $date = now()->subMonths($months);

        Activity::where('created_at', '<', $date)->delete();

        $this->info('Old records have been deleted successfully.');
    }
}
