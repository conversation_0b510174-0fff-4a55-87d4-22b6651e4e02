<?php

namespace App\Models\Victim;

use App\Enums\InstanceStatus;
use App\Enums\InstanceType;
use App\Models\Health\HealthCenter;
use App\Observers\InstanceObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @mixin IdeHelperInstance
 */
#[ObservedBy([InstanceObserver::class])]
class Instance extends Model
{
    use HasFactory, HasUlids, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'code',
        'description',
        'type',
        'status',
        'survivor_id',
    ];

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        'type' => InstanceType::NEW,
        'status' => InstanceStatus::OPEN,
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'type' => InstanceType::class,
            'status' => InstanceStatus::class,
        ];
    }

    protected function opened(): Attribute
    {
        return Attribute::make(
            get: fn (): bool => $this->status === InstanceStatus::OPEN,
        );
    }

    protected function closed(): Attribute
    {
        return Attribute::make(
            get: fn (): bool => $this->status === InstanceStatus::CLOSED,
        );
    }

    public function toggleStatus(): void
    {
        $this->update([
            'status' => $this->status === InstanceStatus::OPEN ?
                InstanceStatus::CLOSED :
                InstanceStatus::OPEN,
        ]);
    }

    public static function getUniqueCode(): string
    {
        return 'CS-'.random_int(10000, 999999);
    }

    public function treatmentsLabels(): ?string
    {
        $labels = $this->treatments->map(fn ($treatment) => $treatment->type->getLabel());

        return $labels->isEmpty() ? null : implode(', ', $labels->toArray());
    }

    public function creator()
    {
        return $this->hasOne(InstanceCreator::class);
    }

    public function healthCenter(): BelongsTo
    {
        return $this->belongsTo(HealthCenter::class);
    }

    public function survivor(): BelongsTo
    {
        return $this->belongsTo(Survivor::class);
    }

    public function relapse(): HasOne
    {
        return $this->hasOne(Relapse::class);
    }

    public function diagnostics(): HasMany
    {
        return $this->hasMany(Diagnostic::class);
    }

    public function treatments(): HasMany
    {
        return $this->hasMany(Treatment::class);
    }

    public function companions(): HasMany
    {
        return $this->hasMany(Companion::class);
    }

    public function followups(): HasMany
    {
        return $this->hasMany(Followup::class);
    }
}
