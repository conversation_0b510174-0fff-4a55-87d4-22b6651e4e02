<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Enums\UserResponsibility;
use App\Enums\UserRole;
use App\Models\Health\HealthCenter;
use App\Models\Victim\Companion;
use App\Observers\UserObserver;
use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

/**
 * @mixin IdeHelperUser
 */
#[ObservedBy([UserObserver::class])]
class User extends Authenticatable implements FilamentUser
{
    use HasApiTokens, HasFactory, HasUlids, Notifiable, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'role',
        'phone',
        'password',
        'description',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'role' => UserRole::class,
        ];
    }

    /**
     * Route notifications for the Twilio channel.
     */
    public function routeNotificationForTwilio($notification): string
    {
        return $this->phone;
    }

    /**
     * Specifies the user's FCM token
     *
     * @return string|null
     */
    public function routeNotificationForFcm()
    {
        return $this->fcmToken?->token ?? null;
    }

    /**
     * Get the user's preferred locale.
     */
    public function preferredLocale(): string
    {
        return config('app.locale');
    }

    public function canAccessPanel(Panel $panel): bool
    {
        if ($this->isRoot()) {
            return true;
        }

        return $this->isAPS();
    }

    public function isRoot(): bool
    {
        return $this->role === UserRole::ROOT;
    }

    public function isAPS(): bool
    {
        return $this->role === UserRole::APS;
    }

    public function isCompanion(): bool
    {
        return $this->role === UserRole::COMPANION;
    }

    public static function randomPassword(): string
    {
        return (string) random_int(100000, 999999);
    }

    public function healthCenters()
    {
        return $this->belongsToMany(HealthCenter::class)
            ->using(HealthCenterUser::class)
            ->withPivot('responsibility');
    }

    public function companions()
    {
        return $this->hasMany(Companion::class);
    }

    public function belongsToHealthCenter(HealthCenter|string $healthCenter, ?UserResponsibility $responsibility = null): bool
    {
        $query = $this->healthCenters()->where(
            column: 'health_centers.id',
            operator: '=',
            value: is_string($healthCenter) ? $healthCenter : $healthCenter->id
        );

        if ($responsibility instanceof UserResponsibility) {
            $query = $query->wherePivot('responsibility', $responsibility);
        }

        return $query->exists();
    }

    public function fcmToken()
    {
        return $this->hasOne(FcmToken::class);
    }

    public function companionRole()
    {
        return $this->hasOne(CompanionRole::class);
    }
}
