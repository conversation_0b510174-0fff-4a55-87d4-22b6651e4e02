<?php

namespace App\Models\Health;

use App\Enums\UserRole;
use App\Models\HealthCenterUser;
use App\Models\User;
use App\Models\Victim\Instance;
use App\Models\Victim\Relapse;
use App\Models\Victim\Survivor;
use App\Observers\HealthCenterObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @mixin IdeHelperHealthCenter
 */
#[ObservedBy([HealthCenterObserver::class])]
class HealthCenter extends Model
{
    use HasFactory, HasUlids, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'address',
        'phone',
        'services_offered',
        'health_zone_id',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [];
    }

    public function healthZone(): BelongsTo
    {
        return $this->belongsTo(HealthZone::class);
    }

    public function users()
    {
        return $this->belongsToMany(User::class)
            ->using(HealthCenterUser::class)
            ->withPivot('responsibility');
    }

    public function aps()
    {
        return $this->users()->where('role', UserRole::APS);
    }

    public function companions()
    {
        return $this->users()->where('role', UserRole::COMPANION);
    }

    public function instances()
    {
        return $this->hasMany(Instance::class);
    }

    public function survivors()
    {
        return $this->hasMany(Survivor::class);
    }

    public function relapses()
    {
        return $this->hasManyThrough(Relapse::class, Instance::class);
    }
}
