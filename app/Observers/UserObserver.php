<?php

namespace App\Observers;

use App\Enums\ActivityActionType;
use App\Events\RecordActivityEvent;
use App\Models\User;
use App\Notifications\UserCreated;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;
use Illuminate\Support\Facades\Hash;

class UserObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the User "created" event.
     */
    public function created(User $user): void
    {
        $password = User::randomPassword();

        logger(
            "User {$user->phone} created with password: $password"
        );

        $user->fill(['password' => Hash::make($password)])->saveQuietly();

        $user->notify(new UserCreated($password));

        // Record activity
        event(
            new RecordActivityEvent(
                model: $user->getTable(),
                action: ActivityActionType::CREATE,
                data: $user->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the User "updated" event.
     */
    public function updated(User $user): void
    {
        // Record activity
        event(
            new RecordActivityEvent(
                model: $user->getTable(),
                action: ActivityActionType::UPDATE,
                data: $user->withoutRelations()->toArray(),
                attributes: $user->getChanges(),
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the User "deleted" event.
     */
    public function deleted(User $user): void
    {
        // Record activity
        event(
            new RecordActivityEvent(
                model: $user->getTable(),
                action: ActivityActionType::DELETE,
                data: $user->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the User "restored" event.
     */
    public function restored(User $user): void
    {
        // Record activity
        event(
            new RecordActivityEvent(
                model: $user->getTable(),
                action: ActivityActionType::RESTORE,
                data: $user->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the User "force deleted" event.
     */
    public function forceDeleted(User $user): void
    {
        // Record activity
        event(
            new RecordActivityEvent(
                model: $user->getTable(),
                action: ActivityActionType::FORCE_DELETE,
                data: $user->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }
}
