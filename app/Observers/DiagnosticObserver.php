<?php

namespace App\Observers;

use App\Enums\ActivityActionType;
use App\Events\RecordActivityEvent;
use App\Models\Victim\Diagnostic;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;

class DiagnosticObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the Diagnostic "created" event.
     */
    public function created(Diagnostic $diagnostic): void
    {
        event(
            new RecordActivityEvent(
                model: $diagnostic->getTable(),
                action: ActivityActionType::CREATE,
                data: $diagnostic->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the Diagnostic "updated" event.
     */
    public function updated(Diagnostic $diagnostic): void
    {
        event(
            new RecordActivityEvent(
                model: $diagnostic->getTable(),
                action: ActivityActionType::UPDATE,
                data: $diagnostic->withoutRelations()->toArray(),
                attributes: $diagnostic->getChanges(),
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the Diagnostic "deleted" event.
     */
    public function deleted(Diagnostic $diagnostic): void
    {
        event(
            new RecordActivityEvent(
                model: $diagnostic->getTable(),
                action: ActivityActionType::DELETE,
                data: $diagnostic->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the Diagnostic "restored" event.
     */
    public function restored(Diagnostic $diagnostic): void
    {
        event(
            new RecordActivityEvent(
                model: $diagnostic->getTable(),
                action: ActivityActionType::RESTORE,
                data: $diagnostic->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the Diagnostic "force deleted" event.
     */
    public function forceDeleted(Diagnostic $diagnostic): void
    {
        event(
            new RecordActivityEvent(
                model: $diagnostic->getTable(),
                action: ActivityActionType::FORCE_DELETE,
                data: $diagnostic->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }
}
