<?php

namespace App\Observers;

use App\Models\Campaign;
use App\Models\User;
use App\Notifications\CampaignCreated;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;

class CampaignObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the Campaign "created" event.
     */
    public function created(Campaign $campaign): void
    {
        $authUser = auth()->user();
        $query = $campaign->health_center_id ? $campaign->healthCenter->users() : User::query();
        $query = $campaign->user_role ? $query->where('users.role', $campaign->user_role) : $query;

        $query->each(function (User $user) use ($campaign, $authUser): void {
            if ($authUser?->id === $user->id) {
                return;
            }

            $user->notify(new CampaignCreated($campaign));
        });

    }

    /**
     * Handle the Campaign "updated" event.
     */
    public function updated(Campaign $campaign): void
    {
        //
    }

    /**
     * Handle the Campaign "deleted" event.
     */
    public function deleted(Campaign $campaign): void
    {
        //
    }

    /**
     * Handle the Campaign "restored" event.
     */
    public function restored(Campaign $campaign): void
    {
        //
    }

    /**
     * Handle the Campaign "force deleted" event.
     */
    public function forceDeleted(Campaign $campaign): void
    {
        //
    }
}
