<?php

namespace App\Observers;

use App\Enums\ActivityActionType;
use App\Enums\UserRole;
use App\Events\RecordActivityEvent;
use App\Models\User;
use App\Models\Victim\Companion;
use App\Models\Victim\Relapse;
use App\Notifications\RelapseCreated;
use App\Notifications\RelapseRemoved;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;

class RelapseObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the Relapse "created" event.
     */
    public function created(Relapse $relapse): void
    {
        $this->getUsers($relapse)
            ->each(function (User $user) use ($relapse): void {
                $user->notify(new RelapseCreated($relapse));
            });

        // Record activity
        event(
            new RecordActivityEvent(
                model: $relapse->getTable(),
                action: ActivityActionType::CREATE,
                data: $relapse->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the Relapse "updated" event.
     */
    public function updated(Relapse $relapse): void
    {
        // Record activity
        event(
            new RecordActivityEvent(
                model: $relapse->getTable(),
                action: ActivityActionType::UPDATE,
                data: $relapse->withoutRelations()->toArray(),
                attributes: $relapse->getChanges(),
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the Relapse "deleted" event.
     */
    public function deleted(Relapse $relapse): void
    {
        $this->getUsers($relapse)
            ->each(function (User $user) use ($relapse): void {
                $user->notify(new RelapseRemoved($relapse->instance));
            });

        // Record activity
        event(
            new RecordActivityEvent(
                model: $relapse->getTable(),
                action: ActivityActionType::DELETE,
                data: $relapse->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    private function getUsers(Relapse $relapse)
    {
        $users = $relapse->instance->healthCenter->users
            ->filter(fn (User $user): bool => $user->role !== UserRole::COMPANION)
            ->filter(fn (User $user): bool => $user->id !== auth()->user()?->id);

        $companionUsers = $relapse->instance
            ->companions
            ->map(fn (Companion $companion) => $companion->user)
            ->filter(fn (User $user): bool => $user->id !== auth()->user()?->id);

        return $users->merge($companionUsers);
    }

    /**
     * Handle the Relapse "restored" event.
     */
    public function restored(Relapse $relapse): void
    {
        // Record activity
        event(
            new RecordActivityEvent(
                model: $relapse->getTable(),
                action: ActivityActionType::RESTORE,
                data: $relapse->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the Relapse "force deleted" event.
     */
    public function forceDeleted(Relapse $relapse): void
    {
        // Record activity
        event(
            new RecordActivityEvent(
                model: $relapse->getTable(),
                action: ActivityActionType::FORCE_DELETE,
                data: $relapse->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }
}
