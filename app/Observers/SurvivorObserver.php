<?php

namespace App\Observers;

use App\Enums\ActivityActionType;
use App\Events\RecordActivityEvent;
use App\Models\Victim\Survivor;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;

class SurvivorObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the Survivor "created" event.
     */
    public function created(Survivor $survivor): void
    {
        event(
            new RecordActivityEvent(
                model: $survivor->getTable(),
                action: ActivityActionType::CREATE,
                data: $survivor->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the Survivor "updated" event.
     */
    public function updated(Survivor $survivor): void
    {
        event(
            new RecordActivityEvent(
                model: $survivor->getTable(),
                action: ActivityActionType::UPDATE,
                data: $survivor->withoutRelations()->toArray(),
                attributes: $survivor->getChanges(),
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the Survivor "deleted" event.
     */
    public function deleted(Survivor $survivor): void
    {
        event(
            new RecordActivityEvent(
                model: $survivor->getTable(),
                action: ActivityActionType::DELETE,
                data: $survivor->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the Survivor "restored" event.
     */
    public function restored(Survivor $survivor): void
    {
        event(
            new RecordActivityEvent(
                model: $survivor->getTable(),
                action: ActivityActionType::RESTORE,
                data: $survivor->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the Survivor "force deleted" event.
     */
    public function forceDeleted(Survivor $survivor): void
    {
        event(
            new RecordActivityEvent(
                model: $survivor->getTable(),
                action: ActivityActionType::FORCE_DELETE,
                data: $survivor->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }
}
