<?php

namespace App\Observers;

use App\Enums\ActivityActionType;
use App\Events\RecordActivityEvent;
use App\Models\HealthCenterUser;
use App\Models\Victim\Companion;
use App\Notifications\HealthCenterUserAttached;
use App\Notifications\HealthCenterUserDeleted;
use App\Notifications\HealthCenterUserResponsibilityChanged;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;

class HealthCenterUserObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the HealthCenterUser "created" event.
     */
    public function created(HealthCenterUser $healthCenterUser): void
    {
        $healthCenterUser->user->notify(
            new HealthCenterUserAttached($healthCenterUser)
        );

        // Record activity
        event(
            new RecordActivityEvent(
                model: $healthCenterUser->getTable(),
                action: ActivityActionType::CREATE,
                data: $healthCenterUser->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the HealthCenterUser "updated" event.
     */
    public function updated(HealthCenterUser $healthCenterUser): void
    {
        if ($healthCenterUser->wasChanged('responsibility')) {
            $healthCenterUser->user->notify(
                new HealthCenterUserResponsibilityChanged($healthCenterUser)
            );
        }

        // Record activity
        event(
            new RecordActivityEvent(
                model: $healthCenterUser->getTable(),
                action: ActivityActionType::UPDATE,
                data: $healthCenterUser->withoutRelations()->toArray(),
                attributes: $healthCenterUser->getChanges(),
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the HealthCenterUser "deleted" event.
     */
    public function deleted(HealthCenterUser $healthCenterUser): void
    {
        // Clean up
        Companion::whereRelation('instance', 'health_center_id', '=', $healthCenterUser->health_center_id)
            ->where('user_id', $healthCenterUser->user_id)
            ->get()
            ->each(fn (Companion $companion) => $companion->forceDeleteQuietly());

        $healthCenterUser->user->notify(
            new HealthCenterUserDeleted($healthCenterUser->healthCenter)
        );

        // Record activity
        event(
            new RecordActivityEvent(
                model: $healthCenterUser->getTable(),
                action: ActivityActionType::DELETE,
                data: $healthCenterUser->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the HealthCenterUser "restored" event.
     */
    public function restored(HealthCenterUser $healthCenterUser): void
    {
        // Record activity
        event(
            new RecordActivityEvent(
                model: $healthCenterUser->getTable(),
                action: ActivityActionType::RESTORE,
                data: $healthCenterUser->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the HealthCenterUser "force deleted" event.
     */
    public function forceDeleted(HealthCenterUser $healthCenterUser): void
    {
        // Clean up
        Companion::whereRelation('instance', 'health_center_id', '=', $healthCenterUser->health_center_id)
            ->where('user_id', $healthCenterUser->user_id)
            ->get()
            ->each(fn (Companion $companion) => $companion->forceDeleteQuietly());

        // Record activity
        event(
            new RecordActivityEvent(
                model: $healthCenterUser->getTable(),
                action: ActivityActionType::FORCE_DELETE,
                data: $healthCenterUser->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }
}
