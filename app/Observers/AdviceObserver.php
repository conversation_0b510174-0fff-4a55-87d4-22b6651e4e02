<?php

namespace App\Observers;

use App\Enums\ActivityActionType;
use App\Events\RecordActivityEvent;
use App\Models\Advice;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;

class AdviceObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the Advice "created" event.
     */
    public function created(Advice $advice): void
    {
        event(
            new RecordActivityEvent(
                model: $advice->getTable(),
                action: ActivityActionType::CREATE,
                data: $advice->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the Advice "updated" event.
     */
    public function updated(Advice $advice): void
    {
        event(
            new RecordActivityEvent(
                model: $advice->getTable(),
                action: ActivityActionType::UPDATE,
                data: $advice->withoutRelations()->toArray(),
                attributes: $advice->getChanges(),
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the Advice "deleted" event.
     */
    public function deleted(Advice $advice): void
    {
        event(
            new RecordActivityEvent(
                model: $advice->getTable(),
                action: ActivityActionType::DELETE,
                data: $advice->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the Advice "restored" event.
     */
    public function restored(Advice $advice): void
    {
        event(
            new RecordActivityEvent(
                model: $advice->getTable(),
                action: ActivityActionType::RESTORE,
                data: $advice->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the Advice "force deleted" event.
     */
    public function forceDeleted(Advice $advice): void
    {
        event(
            new RecordActivityEvent(
                model: $advice->getTable(),
                action: ActivityActionType::FORCE_DELETE,
                data: $advice->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }
}
