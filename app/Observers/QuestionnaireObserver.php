<?php

namespace App\Observers;

use App\Enums\ActivityActionType;
use App\Events\RecordActivityEvent;
use App\Models\Questionnaire;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;

class QuestionnaireObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the Questionnaire "created" event.
     */
    public function created(Questionnaire $questionnaire): void
    {
        event(
            new RecordActivityEvent(
                model: $questionnaire->getTable(),
                action: ActivityActionType::CREATE,
                data: $questionnaire->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the Questionnaire "updated" event.
     */
    public function updated(Questionnaire $questionnaire): void
    {
        event(
            new RecordActivityEvent(
                model: $questionnaire->getTable(),
                action: ActivityActionType::UPDATE,
                data: $questionnaire->withoutRelations()->toArray(),
                attributes: $questionnaire->getChanges(),
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the Questionnaire "deleted" event.
     */
    public function deleted(Questionnaire $questionnaire): void
    {
        event(
            new RecordActivityEvent(
                model: $questionnaire->getTable(),
                action: ActivityActionType::DELETE,
                data: $questionnaire->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the Questionnaire "restored" event.
     */
    public function restored(Questionnaire $questionnaire): void
    {
        event(
            new RecordActivityEvent(
                model: $questionnaire->getTable(),
                action: ActivityActionType::RESTORE,
                data: $questionnaire->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the Questionnaire "force deleted" event.
     */
    public function forceDeleted(Questionnaire $questionnaire): void
    {
        event(
            new RecordActivityEvent(
                model: $questionnaire->getTable(),
                action: ActivityActionType::FORCE_DELETE,
                data: $questionnaire->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }
}
