<?php

namespace App\Observers;

use App\Enums\ActivityActionType;
use App\Events\RecordActivityEvent;
use App\Models\Health\HealthZone;

class HealthZoneObserver
{
    /**
     * Handle the HealthZone "created" event.
     */
    public function created(HealthZone $healthZone): void
    {
        event(
            new RecordActivityEvent(
                model: $healthZone->getTable(),
                action: ActivityActionType::CREATE,
                data: $healthZone->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the HealthZone "updated" event.
     */
    public function updated(HealthZone $healthZone): void
    {
        event(
            new RecordActivityEvent(
                model: $healthZone->getTable(),
                action: ActivityActionType::UPDATE,
                data: $healthZone->withoutRelations()->toArray(),
                attributes: $healthZone->getChanges(),
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the HealthZone "deleted" event.
     */
    public function deleted(HealthZone $healthZone): void
    {
        event(
            new RecordActivityEvent(
                model: $healthZone->getTable(),
                action: ActivityActionType::DELETE,
                data: $healthZone->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the HealthZone "restored" event.
     */
    public function restored(HealthZone $healthZone): void
    {
        event(
            new RecordActivityEvent(
                model: $healthZone->getTable(),
                action: ActivityActionType::RESTORE,
                data: $healthZone->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the HealthZone "force deleted" event.
     */
    public function forceDeleted(HealthZone $healthZone): void
    {
        event(
            new RecordActivityEvent(
                model: $healthZone->getTable(),
                action: ActivityActionType::FORCE_DELETE,
                data: $healthZone->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }
}
