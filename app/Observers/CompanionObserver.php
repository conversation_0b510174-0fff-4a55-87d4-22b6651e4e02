<?php

namespace App\Observers;

use App\Enums\ActivityActionType;
use App\Events\RecordActivityEvent;
use App\Models\Victim\Companion;
use App\Notifications\CompanionCreated;
use App\Notifications\CompanionDeleted;
use App\Notifications\CompanionTypeChanged;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;

class CompanionObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the Companion "created" event.
     */
    public function created(Companion $companion): void
    {
        $companion->user->notify(
            new CompanionCreated($companion)
        );

        // Record activity
        event(
            new RecordActivityEvent(
                model: $companion->getTable(),
                action: ActivityActionType::CREATE,
                data: $companion->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the Companion "updated" event.
     */
    public function updated(Companion $companion): void
    {
        if ($companion->wasChanged('type')) {
            $companion->user->notify(
                new CompanionTypeChanged($companion)
            );
        }

        // Record activity
        event(
            new RecordActivityEvent(
                model: $companion->getTable(),
                action: ActivityActionType::UPDATE,
                data: $companion->withoutRelations()->toArray(),
                attributes: $companion->getChanges(),
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the Companion "deleted" event.
     */
    public function deleted(Companion $companion): void
    {
        $instance = $companion->instance;

        $companion->user->notify(
            new CompanionDeleted(
                survivor: $instance->survivor,
                healthCenter: $instance->healthCenter,
            )
        );

        // Record activity
        event(
            new RecordActivityEvent(
                model: $companion->getTable(),
                action: ActivityActionType::DELETE,
                data: $companion->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the Companion "restored" event.
     */
    public function restored(Companion $companion): void
    {
        // Record activity
        event(
            new RecordActivityEvent(
                model: $companion->getTable(),
                action: ActivityActionType::RESTORE,
                data: $companion->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the Companion "force deleted" event.
     */
    public function forceDeleted(Companion $companion): void
    {
        // Record activity
        event(
            new RecordActivityEvent(
                model: $companion->getTable(),
                action: ActivityActionType::FORCE_DELETE,
                data: $companion->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }
}
