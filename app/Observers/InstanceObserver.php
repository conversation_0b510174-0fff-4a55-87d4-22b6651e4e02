<?php

namespace App\Observers;

use App\Enums\ActivityActionType;
use App\Enums\UserRole;
use App\Events\RecordActivityEvent;
use App\Models\Victim\Companion;
use App\Models\Victim\Instance;
use App\Notifications\InstanceStatusUpdated;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;

class InstanceObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the Instance "created" event.
     */
    public function created(Instance $instance): void
    {
        $userAPS = auth()->user();

        if ($userAPS !== null) {
            if ($userAPS->role !== UserRole::APS) {
                $userAPS = $instance->healthCenter->aps()->first() ?? auth()->user();
            }

            $instance->creator()->create(['user_id' => $userAPS->id]);
        }

        // Record activity
        event(
            new RecordActivityEvent(
                model: $instance->getTable(),
                action: ActivityActionType::CREATE,
                data: $instance->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the Instance "updated" event.
     */
    public function updated(Instance $instance): void
    {
        if (! $instance->wasChanged('status')) {
            return;
        }

        $instance->companions->each(function (Companion $companion) use ($instance): void {
            $companion->user->notify(
                new InstanceStatusUpdated(
                    instance: $instance,
                    companion: $companion
                )
            );
        });

        // Record activity
        event(
            new RecordActivityEvent(
                model: $instance->getTable(),
                action: ActivityActionType::UPDATE,
                data: $instance->withoutRelations()->toArray(),
                attributes: $instance->getChanges(),
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the Instance "deleted" event.
     */
    public function deleted(Instance $instance): void
    {
        // Delete companions
        $instance->companions()
            ->withTrashed()
            ->each(fn (Companion $companion) => $companion->deleteQuietly());

        // Record activity
        event(
            new RecordActivityEvent(
                model: $instance->getTable(),
                action: ActivityActionType::DELETE,
                data: $instance->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the Instance "restored" event.
     */
    public function restored(Instance $instance): void
    {
        // Restore companions
        $instance->companions()
            ->withTrashed()
            ->each(fn (Companion $companion) => $companion->restoreQuietly());

        // Record activity
        event(
            new RecordActivityEvent(
                model: $instance->getTable(),
                action: ActivityActionType::RESTORE,
                data: $instance->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the Instance "force deleted" event.
     */
    public function forceDeleted(Instance $instance): void
    {
        // Record activity
        event(
            new RecordActivityEvent(
                model: $instance->getTable(),
                action: ActivityActionType::FORCE_DELETE,
                data: $instance->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }
}
