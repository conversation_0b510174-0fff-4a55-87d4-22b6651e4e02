<?php

namespace App\Observers;

use App\Enums\ActivityActionType;
use App\Events\RecordActivityEvent;
use App\Models\Health\HealthCenter;

class HealthCenterObserver
{
    /**
     * Handle the HealthCenter "created" event.
     */
    public function created(HealthCenter $healthCenter): void
    {
        event(
            new RecordActivityEvent(
                model: $healthCenter->getTable(),
                action: ActivityActionType::CREATE,
                data: $healthCenter->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the HealthCenter "updated" event.
     */
    public function updated(HealthCenter $healthCenter): void
    {
        event(
            new RecordActivityEvent(
                model: $healthCenter->getTable(),
                action: ActivityActionType::UPDATE,
                data: $healthCenter->withoutRelations()->toArray(),
                attributes: $healthCenter->getChanges(),
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the HealthCenter "deleted" event.
     */
    public function deleted(HealthCenter $healthCenter): void
    {
        event(
            new RecordActivityEvent(
                model: $healthCenter->getTable(),
                action: ActivityActionType::DELETE,
                data: $healthCenter->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the HealthCenter "restored" event.
     */
    public function restored(HealthCenter $healthCenter): void
    {
        event(
            new RecordActivityEvent(
                model: $healthCenter->getTable(),
                action: ActivityActionType::RESTORE,
                data: $healthCenter->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the HealthCenter "force deleted" event.
     */
    public function forceDeleted(HealthCenter $healthCenter): void
    {
        event(
            new RecordActivityEvent(
                model: $healthCenter->getTable(),
                action: ActivityActionType::FORCE_DELETE,
                data: $healthCenter->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }
}
