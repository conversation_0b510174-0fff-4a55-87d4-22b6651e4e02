<?php

namespace App\Observers;

use App\Enums\ActivityActionType;
use App\Events\RecordActivityEvent;
use App\Models\Victim\Treatment;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;

class TreatmentObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the Treatment "created" event.
     */
    public function created(Treatment $treatment): void
    {
        event(
            new RecordActivityEvent(
                model: $treatment->getTable(),
                action: ActivityActionType::CREATE,
                data: $treatment->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );

        // clear treatment observation dashes
        $this->clearTreatmentObservationDashes($treatment, '-----');
        $this->clearTreatmentObservationDashes($treatment, '----');
    }

    /**
     * Handle the Treatment "updated" event.
     */
    public function updated(Treatment $treatment): void
    {
        event(
            new RecordActivityEvent(
                model: $treatment->getTable(),
                action: ActivityActionType::UPDATE,
                data: $treatment->withoutRelations()->toArray(),
                attributes: $treatment->getChanges(),
                user: auth()->user(),
            )
        );

        // Update treatment observation dashes
        $this->clearTreatmentObservationDashes($treatment, '-----');
        $this->clearTreatmentObservationDashes($treatment, '----');
    }

    /**
     * Handle the Treatment "deleted" event.
     */
    public function deleted(Treatment $treatment): void
    {
        event(
            new RecordActivityEvent(
                model: $treatment->getTable(),
                action: ActivityActionType::DELETE,
                data: $treatment->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the Treatment "restored" event.
     */
    public function restored(Treatment $treatment): void
    {
        event(
            new RecordActivityEvent(
                model: $treatment->getTable(),
                action: ActivityActionType::RESTORE,
                data: $treatment->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    /**
     * Handle the Treatment "force deleted" event.
     */
    public function forceDeleted(Treatment $treatment): void
    {
        event(
            new RecordActivityEvent(
                model: $treatment->getTable(),
                action: ActivityActionType::FORCE_DELETE,
                data: $treatment->withoutRelations()->toArray(),
                attributes: [],
                user: auth()->user(),
            )
        );
    }

    protected function clearTreatmentObservationDashes(Treatment $treatment, string $symbol)
    {
        if (trim($treatment->observation) !== $symbol) {
            $treatment->observation = str_replace($symbol, '', $treatment->observation);
            $treatment->saveQuietly();
        }
    }
}
