<?php

namespace App\Http\Requests;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class CreateCompanionRelapseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $instance = $this->route('companion')->instance;

        return [
            'description' => [
                'required',
                'min:10',
                function (string $attribute, mixed $value, Closure $fail) use ($instance): void {
                    if ($instance->relapse) {
                        $fail(__('Une rechute a déjà été enregistrée pour cette instance.'));
                    }
                },
            ],
        ];
    }
}
