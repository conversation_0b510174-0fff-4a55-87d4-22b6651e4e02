<?php

namespace App\Http\Requests;

use App\Enums\InstanceType;
use App\Models\Victim\Instance;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Str;

class CreateInstanceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'survivor_code' => ['required', 'string', 'max:50'],
            'code' => ['required', 'string', 'unique:instances', 'max:50'],
            'type' => ['required', Rule::enum(InstanceType::class)],
            'description' => ['nullable', 'string'],
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // loop instances to ensure unique code
        $count = 0;
        $uniqueCode = Instance::getUniqueCode();
        while (Instance::where('code', $uniqueCode)->exists()) {
            // Prevent infinite loop in case of an error
            if ($count++ > 100) {
                // generate a uuid if too many attempts
                $uniqueCode = Str::ulid()->toBase32();
                break;
            }

            $uniqueCode = Instance::getUniqueCode();
        }

        $this->merge(['code' => $uniqueCode]);
    }
}
