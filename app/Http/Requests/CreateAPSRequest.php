<?php

namespace App\Http\Requests;

use App\Enums\UserResponsibility;
use App\Enums\UserRole;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateAPSRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $user = $this->user();
        if ($user->isRoot()) {
            return true;
        }

        return (bool) $user->belongsToHealthCenter(
            healthCenter: $this->route('health_center'),
            responsibility: UserResponsibility::ADMINISTRATOR
        );
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'min:3', 'max:255'],
            'phone' => ['required', 'phone', 'unique:users'],
            'email' => ['nullable', 'email', 'unique:users', 'max:255'],
            'role' => ['required', Rule::enum(UserRole::class)->only([UserRole::APS])],
            'responsibility' => [
                'required',
                Rule::enum(UserResponsibility::class)->only([
                    UserResponsibility::OPERATOR,
                    UserResponsibility::ADMINISTRATOR,
                ]),
            ],
            'description' => ['nullable', 'string'],
        ];
    }
}
