<?php

namespace App\Http\Requests;

use App\Enums\CompanionType;
use App\Enums\UserResponsibility;
use App\Enums\UserRole;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateCompanionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $user = $this->route('user');

        return [
            'name' => ['required', 'string', 'min:3', 'max:255'],
            'phone' => [
                'required',
                'phone',
                Rule::unique('users')->ignore($user->id),
            ],
            'email' => [
                'nullable',
                'email',
                'max:255',
                Rule::unique('users')->ignore($user->id),
            ],
            'role' => [
                'required',
                Rule::enum(UserRole::class)->only([UserRole::COMPANION]),
            ],
            'responsibility' => [
                'required',
                Rule::enum(UserResponsibility::class)->only([UserResponsibility::COMPANION]),
            ],
            'companion_role' => [
                'required',
                Rule::enum(CompanionType::class),
            ],
            'description' => ['nullable', 'string'],
        ];
    }
}
