<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class EnsureUserBelongsToCompanionHealthCenter
{
    /**
     * Handle an incoming request.
     *
     * @param  Closure(Request):Response  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();
        $companion = $request->route('companion');

        if (filled($companion)) {
            $instance = $companion->instance;
            $healthCenter = $instance->healthCenter;

            if (
                $user->id !== $companion->user_id || blank($healthCenter) ||
                ! $user->belongsToHealthCenter($healthCenter)
            ) {
                abort(403, __("Vous n'êtes pas autorisé à accéder à cette ressource."));
            }
        }

        return $next($request);
    }
}
