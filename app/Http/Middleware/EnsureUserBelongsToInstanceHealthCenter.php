<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class EnsureUserBelongsToInstanceHealthCenter
{
    /**
     * Handle an incoming request.
     *
     * @param  Closure(Request):Response  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();
        $instance = $request->route('instance');
        $healthCenter = $instance->healthCenter;

        if ((! $user->isRoot() && ! $user->belongsToHealthCenter($healthCenter)) || blank($healthCenter)) {
            abort(403, __("Vous n'êtes pas autorisé à accéder à cette ressource."));
        }

        return $next($request);
    }
}
