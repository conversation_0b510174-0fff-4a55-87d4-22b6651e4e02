<?php

namespace App\Http\Controllers\API\Instance;

use App\Http\Controllers\Controller;
use App\Http\Requests\CreateDiagnosticRequest;
use App\Http\Requests\UpdateDiagnosticRequest;
use App\Http\Resources\Diagnostic\DiagnosticCollection;
use App\Http\Resources\Diagnostic\DiagnosticResource;
use App\Models\Victim\Diagnostic;
use App\Models\Victim\Instance;
use App\Validator\Attachment;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;
use Illuminate\Support\Facades\Storage;

class DiagnosticController extends Controller implements HasMiddleware
{
    /**
     * Get the middleware that should be assigned to the controller.
     */
    public static function middleware(): array
    {
        return [
            new Middleware('user.belongs-to-instance-healthCenter'),
        ];
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Instance $instance): DiagnosticCollection
    {
        return new DiagnosticCollection($instance->diagnostics()->latest()->get());
    }

    /**
     * Display the specified resource.
     */
    public function show(Instance $instance, Diagnostic $diagnostic): DiagnosticResource
    {
        $diagnostic = $instance->diagnostics()->where('diagnostics.id', $diagnostic->id)->firstOrFail();

        return new DiagnosticResource($diagnostic);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CreateDiagnosticRequest $request, Instance $instance): DiagnosticResource
    {
        $data = $request->validated();

        if ($request->hasFile('response')) {
            $file = $request->file('response');
            $mime = $file->getMimeType();

            abort_if(
                ! in_array($mime, Attachment::ATTACHMENT_MIME_TYPE),
                422,
                __("L'extension du fichier n'est pas valide.")
            );

            $data['response'] = $file->store(Attachment::DIAGNOSTIC_UPLOAD_DIRECTORY, 'public');
        }

        $diagnostic = $instance->diagnostics()->create($data);

        return new DiagnosticResource($diagnostic);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateDiagnosticRequest $request, Instance $instance, Diagnostic $diagnostic): DiagnosticResource
    {
        $data = $request->validated();

        if ($request->hasFile('response')) {
            if (filled($diagnostic->response)) {
                Storage::disk('public')->delete($diagnostic->response);
            }

            $file = $request->file('response');
            $mime = $file->getMimeType();

            abort_if(
                ! in_array($mime, Attachment::ATTACHMENT_MIME_TYPE),
                422,
                __("L'extension du fichier n'est pas valide.")
            );

            $data['response'] = $file->store(Attachment::DIAGNOSTIC_UPLOAD_DIRECTORY, 'public');
        }

        $diagnostic->fill($data);
        $diagnostic->save();

        $diagnostic->refresh();

        return new DiagnosticResource($diagnostic);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Instance $instance, Diagnostic $diagnostic): array
    {
        $diagnostic->deleteOrFail();

        return [
            'message' => __('Diagnostic supprimé avec succès.'),
        ];
    }
}
