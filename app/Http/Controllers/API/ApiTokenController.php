<?php

namespace App\Http\Controllers\API;

use App\Enums\ActivityActionType;
use App\Events\RecordActivityEvent;
use App\Http\Controllers\Controller;
use App\Http\Resources\User\UserResource;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class ApiTokenController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request): array
    {
        $request->validate([
            'phone' => 'required|phone',
            'password' => 'required',
            'device_name' => 'required',
        ]);

        $user = User::where('phone', $request->phone)->first();

        if (! $user || ! Hash::check($request->password, $user->password)) {
            throw ValidationException::withMessages([
                'phone' => [__('Les informations fournies sont incorrectes.')],
            ]);
        }

        // Record activity
        event(
            new RecordActivityEvent(
                model: $user->getTable(),
                action: ActivityActionType::LOGIN,
                data: [
                    'type' => 'API_TOKEN',
                    'client_ip' => $request->ip(),
                    'at' => now()->toString(),
                ],
                attributes: [],
                user: $user,
            )
        );

        return [
            'user' => (new UserResource($user))->toArray($request),
            'access_token' => $user->createToken($request->device_name)->plainTextToken,
        ];
    }
}
