<?php

namespace App\Http\Controllers\API\HealthCenter;

use App\Http\Controllers\Controller;
use App\Http\Resources\Instance as InstanceResource;
use App\Http\Resources\Instance\InstanceCollection;
use App\Models\Health\HealthCenter;
use Illuminate\Http\Request;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;

class InstanceRelapsedController extends Controller implements HasMiddleware
{
    /**
     * Get the middleware that should be assigned to the controller.
     */
    public static function middleware(): array
    {
        return [
            new Middleware('user.belongs-to-healthCenter'),
        ];
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request, HealthCenter $healthCenter): InstanceCollection
    {
        $query = $healthCenter->instances()
            ->with(['survivor', 'healthCenter'])
            ->has('relapse')
            ->latest();

        if ($search = $request->query('search')) {
            $query = $query->whereLike(['survivor.code', 'code'], $search);
        }

        return new InstanceResource\InstanceCollection($query->paginate(30));
    }
}
