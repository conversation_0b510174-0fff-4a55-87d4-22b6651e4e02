<?php

namespace App\Http\Controllers\API\HealthCenter;

use App\Http\Controllers\Controller;
use App\Http\Requests\CreateAPSRequest;
use App\Http\Requests\DestroyAPSRequest;
use App\Http\Requests\UpdateAPSRequest;
use App\Http\Resources\User as UserResource;
use App\Http\Resources\User\UserCollection;
use App\Models\Health\HealthCenter;
use App\Models\User;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;
use Illuminate\Support\Str;

class APSController extends Controller implements HasMiddleware
{
    /**
     * Get the middleware that should be assigned to the controller.
     */
    public static function middleware(): array
    {
        return [
            new Middleware('user.belongs-to-healthCenter', except: [
                'store',
                'update',
                'destroy',
            ]),
        ];
    }

    /**
     * Display a listing of the resource.
     */
    public function index(HealthCenter $healthCenter): UserCollection
    {
        $aps = $healthCenter->aps()->get();

        return new UserResource\UserCollection($aps);
    }

    /**
     * Display the specified resource.
     */
    public function show(HealthCenter $healthCenter, User $user): \App\Http\Resources\User\UserResource
    {
        $user = $healthCenter->aps()->where('users.id', $user->id)->firstOrFail();

        return new UserResource\UserResource($user);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CreateAPSRequest $request, HealthCenter $healthCenter): \App\Http\Resources\User\UserResource
    {
        $data = $request->validated();

        $createdUser = User::create([
            ...$data,
            'password' => Str::random(8),
        ]);

        $healthCenter->users()->attach($createdUser->id, [
            'responsibility' => $data['responsibility'],
        ]);

        $user = $healthCenter->users()->where('users.id', $createdUser->id)->firstOrFail();

        return new UserResource\UserResource($user);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateAPSRequest $request, HealthCenter $healthCenter, User $user): \App\Http\Resources\User\UserResource
    {
        $data = $request->validated();

        $user->fill($data)->save();

        $healthCenter->users()->updateExistingPivot($user->id, [
            'responsibility' => $data['responsibility'],
        ]);

        $user->refresh();

        $user = $healthCenter->users()->where('users.id', $user->id)->firstOrFail();

        return new UserResource\UserResource($user);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(DestroyAPSRequest $request, HealthCenter $healthCenter, User $user): array
    {
        $healthCenter->users()->detach($user->id);

        return ['message' => __("L'utilisateur APS a été supprimé avec succès.")];
    }
}
