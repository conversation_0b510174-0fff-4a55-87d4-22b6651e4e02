<?php

namespace App\Http\Controllers\API\HealthCenter;

use App\Http\Controllers\Controller;
use App\Http\Requests\CreateInstanceRequest;
use App\Http\Requests\UpdateInstanceRequest;
use App\Http\Resources\Instance as InstanceResource;
use App\Http\Resources\Instance\InstanceCollection;
use App\Http\Resources\Instance\InstanceShowResource;
use App\Models\Health\HealthCenter;
use App\Models\Victim\Instance;
use Illuminate\Http\Request;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;

class InstanceController extends Controller implements HasMiddleware
{
    /**
     * Get the middleware that should be assigned to the controller.
     */
    public static function middleware(): array
    {
        return [
            new Middleware('user.belongs-to-healthCenter'),
        ];
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request, HealthCenter $healthCenter): InstanceCollection
    {
        $query = $healthCenter->instances()->latest();

        if ($search = $request->query('search')) {
            $query = $query->whereLike(['survivor.code', 'code'], $search);
        }

        return new InstanceResource\InstanceCollection($query->paginate(30));
    }

    /**
     * Display the specified resource.
     */
    public function show(HealthCenter $healthCenter, Instance $instance): InstanceShowResource
    {
        $instance = $healthCenter->instances()->where('instances.id', $instance->id)->firstOrFail();

        return new InstanceResource\InstanceShowResource($instance);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CreateInstanceRequest $request, HealthCenter $healthCenter): InstanceShowResource
    {
        $data = $request->validated();

        $survivor = $healthCenter->survivors()->createOrFirst(['code' => $data['survivor_code']]);

        $instance = $healthCenter->instances()->create([
            'survivor_id' => $survivor->id,
            'code' => $data['code'],
            'type' => $data['type'],
            'description' => $data['description'],
        ]);

        return new InstanceResource\InstanceShowResource($instance);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateInstanceRequest $request, HealthCenter $healthCenter, Instance $instance): InstanceShowResource
    {
        $data = $request->validated();

        $instance->fill($data)->save();

        $instance->refresh();

        return new InstanceResource\InstanceShowResource($instance);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(HealthCenter $healthCenter, Instance $instance): array
    {
        $instance->deleteOrFail();

        return ['message' => __('Le cas a été supprimé.')];
    }
}
