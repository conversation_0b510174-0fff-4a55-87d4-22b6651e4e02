<?php

namespace App\Http\Controllers\API\HealthCenter;

use App\Enums\UserRole;
use App\Http\Controllers\Controller;
use App\Http\Requests\AttachCompanionRequest;
use App\Http\Resources\User\UserCollection;
use App\Models\Health\HealthCenter;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;

class CompanionAttachController extends Controller implements HasMiddleware
{
    /**
     * Get the middleware that should be assigned to the controller.
     */
    public static function middleware(): array
    {
        return [
            new Middleware('user.belongs-to-healthCenter'),
        ];
    }

    /**
     * search companions not attached to health center.
     */
    public function search(Request $request, HealthCenter $healthCenter): UserCollection
    {
        $search = $request->query('search');

        if (blank($search)) {
            return new UserCollection(collect());
        }

        $healthCenterUsers = $healthCenter->companions()->pluck('users.id');

        $usersQuery = User::where('role', UserRole::COMPANION)->whereNotIn('id', $healthCenterUsers);

        $query = $usersQuery->whereLike(['name', 'phone', 'email'], $search)->limit(10);

        return new UserCollection($query->get());
    }

    /**
     * attach companion to health center.
     */
    public function attach(AttachCompanionRequest $request, HealthCenter $healthCenter): array
    {
        $data = $request->validated();

        $user = User::where('id', $data['user_id'])->where('role', UserRole::COMPANION)->firstOrFail();

        abort_if(
            $healthCenter->users()->where('users.id', $user->id)->exists(),
            409,
            __('Cet utilisateur est déjà associé à ce centre de santé')
        );

        $healthCenter->users()->attach(
            $user->id,
            ['responsibility' => $data['responsibility']]
        );

        return ['message' => __('Utilisateur associé avec succès à ce centre de santé')];
    }
}
