<?php

namespace App\Http\Controllers\API\Password;

use App\Events\PasswordForgotten;
use App\Http\Controllers\Controller;
use App\Http\Requests\ForgotPasswordRequest;
use App\Models\ResetPasswordRequest;
use App\Models\User;
use Illuminate\Http\Request;

class ForgotPasswordController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(ForgotPasswordRequest $request): array
    {
        $data = (object) $request->validated();

        // Check if the user has already requested a password reset in the last 30 minutes
        $hasRequested = ResetPasswordRequest::where('phone', $data->phone)
            ->where('expires_at', '>', now())
            ->exists();

        abort_if($hasRequested, 422, __('Vous avez déjà fait une demande de réinitialisation de mot de passe. Veuillez réessayer dans 30 minutes.'));

        $user = User::where('phone', $data->phone)->first();

        abort_if(! $user, 404, __('Aucun utilisateur trouvé avec ce numéro de téléphone.'));

        // Generate uniqued token and password
        $token = User::randomPassword();
        $password = User::randomPassword();

        // Create the password reset request
        ResetPasswordRequest::create([
            'phone' => $data->phone,
            'token' => $token,
            'password' => $password,
            'expires_at' => now()->addMinutes(30),
        ]);

        // Dispatch the event
        event(
            new PasswordForgotten(
                user: $user,
                token: $token,
                password: $password,
            )
        );

        return [
            'message' => __('Un code de réinitialisation de mot de passe vous a été envoyé par SMS.'),
        ];
    }
}
