<?php

namespace App\Http\Controllers\API\Companion;

use App\Http\Controllers\Controller;
use App\Http\Requests\CreateCompanionRelapseRequest;
use App\Http\Requests\UpdateCompanionRelapseRequest;
use App\Http\Resources\Relapse\RelapseResource;
use App\Models\Victim\Companion;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;

class RelapseController extends Controller implements HasMiddleware
{
    /**
     * Get the middleware that should be assigned to the controller.
     */
    public static function middleware(): array
    {
        return [
            new Middleware('user.belongs-to-companion-healthCenter'),
        ];
    }

    /**
     * Display the specified resource.
     */
    public function show(Companion $companion): RelapseResource
    {
        $instance = $companion->instance;
        if (! $instance->relapse) {
            abort(403, "Aucune rechute n\'a été enregistrée pour cette instance.");
        }

        return new RelapseResource($instance->relapse);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CreateCompanionRelapseRequest $request, Companion $companion): RelapseResource
    {
        $data = $request->validated();

        $instance = $companion->instance;

        abort_if($instance->relapse, 403, __('Une rechute a déjà été enregistrée pour cette instance.'));

        $relapse = $instance->relapse()->create($data);

        return new RelapseResource($relapse);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateCompanionRelapseRequest $request, Companion $companion): RelapseResource
    {
        $data = $request->validated();

        $relapse = $companion->instance->relapse;

        abort_if(! $relapse, 403, __("Aucune rechute n'a été enregistrée pour cette instance."));

        $relapse->fill($data);
        $relapse->save();

        $relapse->refresh();

        return new RelapseResource($relapse);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Companion $companion): array
    {
        $companion->instance->relapse()->delete();

        return [
            'message' => __('La rechute a bien été supprimée.'),
        ];
    }
}
