<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\Advice\AdviceCollection;
use App\Http\Resources\Advice\AdviceShowResource;
use App\Http\Resources\Advice as AdviceResource;
use App\Models\Advice;
use Illuminate\Http\Request;

class AdviceController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function index(Request $request): AdviceCollection
    {
        $advices = Advice::paginate(80);

        return new AdviceResource\AdviceCollection($advices);
    }

    public function show(Request $request, Advice $advice): AdviceShowResource
    {
        return new AdviceResource\AdviceShowResource($advice);
    }
}
