<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\Questionnaire\QuestionnaireCollection;
use App\Http\Resources\QuestionnaireChoice\QuestionnaireChoiceCollection;
use App\Models\Questionnaire;

class QuestionnaireController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): QuestionnaireCollection
    {
        return new QuestionnaireCollection(Questionnaire::all());
    }

    /**
     * Display a listing of the resource choices.
     */
    public function choices(Questionnaire $questionnaire): QuestionnaireChoiceCollection
    {
        return new QuestionnaireChoiceCollection($questionnaire->choices);
    }
}
