<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\HealthCenter\HealthCenterCollection;
use App\Models\Health\HealthCenter;
use Illuminate\Http\Request;

class HealthCenterController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): HealthCenterCollection
    {
        $user = $request->user();

        $healthCenters = $user->isRoot() ? HealthCenter::query() : $user->healthCenters();

        $healthCenters = $healthCenters->with(['users', 'instances', 'relapses']);

        return new HealthCenterCollection($healthCenters->get());
    }
}
