<?php

namespace App\Http\Controllers\Report;

use App\Http\Controllers\Controller;
use App\Models\Health\HealthCenter;
use App\Models\Health\HealthZone;
use App\Models\Victim\Instance;
use App\Models\Victim\Relapse;
use Illuminate\Http\Request;
use Spatie\LaravelPdf\PdfBuilder;

use function Spatie\LaravelPdf\Support\pdf;

class OverviewReportController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request): PdfBuilder
    {
        $healthCenters = HealthCenter::all();

        $instanceCount = Instance::count();
        $relapseCount = Relapse::count();
        $healthZoneCount = HealthZone::count();
        $healthCenterCount = HealthCenter::count();

        $appname = config('app.name');
        $today = now()->format('d-m-Y');

        return pdf()
            ->margins(top: 32, right: 32, bottom: 32, left: 32, unit: 'px')
            ->view('reports.overview', [
                'instanceCount' => $instanceCount,
                'relapseCount' => $relapseCount,
                'healthZoneCount' => $healthZoneCount,
                'healthCenterCount' => $healthCenterCount,
                'healthCenters' => $healthCenters,
            ])
            ->name("$appname-report-overview-$today.pdf");
    }
}
