{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "appstract/laravel-opcache": "^4.0", "filament/filament": "^4.0", "flowframe/laravel-trend": "^0.4.0", "guzzlehttp/guzzle": "^7.9", "laravel-notification-channels/fcm": "^5.1", "laravel/folio": "^1.1", "laravel/framework": "^12.1", "laravel/octane": "^2.10", "laravel/pulse": "^1.4", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.10", "league/flysystem-aws-s3-v3": "^3.0", "maatwebsite/excel": "^3.1", "predis/predis": "^2.3", "propaganistas/laravel-phone": "^5.2", "spatie/laravel-backup": "^9.2", "spatie/laravel-pdf": "^1.5", "twilio/sdk": "^8.0"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.8", "barryvdh/laravel-ide-helper": "^3.5", "fakerphp/faker": "^1.23", "larastan/larastan": "^3.1", "laravel/pint": "^1.13", "laravel/sail": "^1.41", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.6", "pestphp/pest": "^3.7", "pestphp/pest-plugin-laravel": "^3.1", "rector/rector": "^2.1", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}