- name: Check if .env exists
  become_user: www-data
  stat:
    path: "{{ deploy_path }}/.env"
  register: env_file

- name: Get existing APP_KEY from .env
  become_user: www-data
  shell: "grep '^APP_KEY=' {{ deploy_path }}/.env || true"
  changed_when: false
  failed_when: false
  register: app_key_content
  when: env_file.stat.exists

- name: Set fact about whether APP_KEY exists
  set_fact:
    app_key_exists: "{{ app_key_content.stdout != '' }}"
  when: env_file.stat.exists

- name: Copy .env file
  template:
    src: .env.j2
    dest: "{{ deploy_path }}/.env"
    force: true
    owner: www-data
    group: www-data
    mode: "0775"
