---
# tasks file for app
- name: Add an exception for this directory
  command: git config --global --add safe.directory {{ deploy_path }}
  ignore_errors: true

- name: Create a directory if it does not exist
  ansible.builtin.file:
    path: "{{ item }}"
    state: directory
    mode: "0755"
  loop:
    - /var/www/.cache
    - /var/www/.ansible

- name: Recursively change ownership of a directory
  ansible.builtin.file:
    path: "{{ item }}"
    state: directory
    recurse: yes
    owner: www-data
    group: www-data
  loop:
    - /var/www/.cache
    - /var/www/.ansible

- name: Clone Git repository
  git:
    repo: "{{ repository_url }}"
    dest: "{{ deploy_path }}"
    clone: yes
    update: yes
    force: true
    version: "{{ repository_version }}"

- name: Set permissions
  file:
    path: "{{ deploy_path }}"
    recurse: yes
    owner: www-data
    group: www-data

- name: Set permissions
  file:
    path: "{{ deploy_path }}"
    recurse: yes
    owner: www-data
    group: www-data

- name: Firebase Copy firebase
  copy:
    content: "{{ firebase_credentials | b64decode }}"
    dest: "{{ deploy_path }}/storage/app/firebase-auth.json"
    force: true
    owner: www-data
    group: www-data
    mode: "0775"

- name: Include .ENV and install dependencies
  block:
    - name: Check if .env exists
      become_user: www-data
      stat:
        path: "{{ deploy_path }}/.env"
      register: env_file

    - name: Get existing APP_KEY from .env
      become_user: www-data
      shell: "grep '^APP_KEY=' {{ deploy_path }}/.env || true"
      changed_when: false
      failed_when: false
      register: app_key_content
      when: env_file.stat.exists

    - name: Set fact about APP_KEY value
      set_fact:
        app_key_value: "{{ app_key_content.stdout | regex_replace('^APP_KEY=(.*)$', '\\1') }}"
      when: env_file.stat.exists

    - name: Set fact about whether APP_KEY exists
      set_fact:
        app_key_exists: "{{ app_key_value != '' }}"
      when: env_file.stat.exists

    - name: Copy .env file
      template:
        src: .env.j2
        dest: "{{ deploy_path }}/.env"
        force: true
        owner: www-data
        group: www-data
        mode: "0775"

    - name: Run composer install with optimize-autoloader and no-dev options
      become_user: www-data
      community.general.composer:
        command: install
        no_dev: true
        optimize_autoloader: true
        working_dir: "{{ deploy_path }}"

    - name: Generate key if APP_KEY is empty
      become_user: www-data
      command: php artisan key:generate --force
      args:
        chdir: "{{ deploy_path }}"
      when: app_key_exists is not defined or not app_key_exists

    - name: Set APP_KEY from backup if it was previously set
      become_user: www-data
      lineinfile:
        path: "{{ deploy_path }}/.env"
        regexp: "^(APP_KEY=).*"
        line: "{{ app_key_content.stdout }}"
        state: present
      when: app_key_exists is defined and app_key_exists

- name: Migrate the database
  command: php artisan migrate --force
  args:
    chdir: "{{ deploy_path }}"

- name: Storage Link
  become_user: www-data
  command: php artisan storage:link --force
  args:
    chdir: "{{ deploy_path }}"

# Cache Clearing
- name: Caching Blade Icons | Clear
  become_user: www-data
  command: php artisan icons:clear
  args:
    chdir: "{{ deploy_path }}"

- name: Caching Filament components | Clear
  become_user: www-data
  command: php artisan filament:clear-cached-components
  args:
    chdir: "{{ deploy_path }}"

- name: Laravel Optimization | Clear
  become_user: www-data
  command: php artisan optimize:clear
  args:
    chdir: "{{ deploy_path }}"

# Apply Cache
- name: Caching Blade Icons
  become_user: www-data
  command: php artisan icons:cache
  args:
    chdir: "{{ deploy_path }}"

- name: Caching Filament components
  become_user: www-data
  command: php artisan filament:cache-components
  args:
    chdir: "{{ deploy_path }}"

- name: Laravel Optimization | Optimize
  become_user: www-data
  command: php artisan optimize
  args:
    chdir: "{{ deploy_path }}"

- include_tasks: assets.yml

# Restart services here

- name: Laravel Pulse | Restart
  become_user: www-data
  command: php artisan pulse:restart
  args:
    chdir: "{{ deploy_path }}"

- name: Laravel Queue | Restart
  become_user: www-data
  command: php artisan queue:restart
  args:
    chdir: "{{ deploy_path }}"

- include_tasks: supervisor.yml

- include_tasks: opcache.yaml
  when: opcache_enable

- name: Set permissions
  file:
    path: "{{ deploy_path }}"
    recurse: yes
    owner: www-data
    group: www-data

- name: Restart PHP-FPM service
  systemd:
    name: "php{{ php_version }}-fpm"
    state: restarted

- name: Ensure nginx service is running
  systemd:
    name: nginx
    state: started
