- name: Clear OpCache
  become_user: www-data
  command: php artisan opcache:clear
  args:
    chdir: "{{ deploy_path }}"
  register: clear_opcache

- name: Show output of clearing OpCache
  debug:
    msg: "{{ clear_opcache.stdout }}"

- name: Configuring Op<PERSON>ache
  become_user: www-data
  command: php artisan opcache:config
  args:
    chdir: "{{ deploy_path }}"
  register: config_opcache

- name: Show OpCache configuration
  debug:
    msg: "{{ config_opcache.stdout }}"

- name: Check OpCache Status
  become_user: www-data
  command: php artisan opcache:status
  args:
    chdir: "{{ deploy_path }}"
  register: status_opcache

- name: Show OpCache status
  debug:
    msg: "{{ status_opcache.stdout }}"

- name: Force Compile OpCache
  become_user: www-data
  command: php artisan opcache:compile --force
  args:
    chdir: "{{ deploy_path }}"
  ignore_errors: true
  register: compile_opcache

- name: Show output of forcing compile OpCache
  debug:
    msg: "{{ compile_opcache.stdout }}"
